FROM acrah01seadahss01.azurecr.io/ci-image-base/veracode-prisma:latest
RUN wget https://download.oracle.com/java/21/latest/jdk-21_linux-x64_bin.deb && apt install ./jdk-21_linux-x64_bin.deb
#RUN update-alternatives --config java
RUN update-alternatives --install /usr/bin/java java /usr/lib/jvm/jdk-21.0.4-oracle-x64/bin/java 2100
RUN update-alternatives --set java /usr/lib/jvm/jdk-21.0.4-oracle-x64/bin/java
RUN useradd -ms /bin/bash 1001 && mkdir /uam && chown -R 1001 /uam
USER 1001
WORKDIR /uam
