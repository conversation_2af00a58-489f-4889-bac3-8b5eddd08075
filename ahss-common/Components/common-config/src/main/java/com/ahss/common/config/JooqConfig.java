package com.ahss.common.config;

import javax.sql.DataSource;
import org.jooq.DSLContext;
import org.jooq.SQLDialect;
import org.jooq.impl.DefaultConfiguration;
import org.jooq.impl.DefaultDSLContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.TransactionAwareDataSourceProxy;

@Configuration
public class JooqConfig {

    @Bean
    public org.jooq.Configuration jooqConfiguration(DataSource dataSource) {
        DefaultConfiguration jooqConfig = new DefaultConfiguration();
        jooqConfig.set(new TransactionAwareDataSourceProxy(dataSource));
        jooqConfig.set(SQLDialect.POSTGRES);
        return jooqConfig;
    }

    @Bean
    public DSLContext dslContext(org.jooq.Configuration jooqConfig) {
        return new DefaultDSLContext(jooqConfig);
    }
}
