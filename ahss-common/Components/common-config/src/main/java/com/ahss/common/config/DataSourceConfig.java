package com.ahss.common.config;

import com.azure.core.credential.AccessToken;
import com.azure.core.credential.TokenCredential;
import com.azure.core.credential.TokenRequestContext;
import com.azure.identity.DefaultAzureCredentialBuilder;
import javax.sql.DataSource;
import org.postgresql.ds.PGSimpleDataSource;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class DataSourceConfig {

    @Value("${spring.datasource.url}")
    private String url;

    @Value("${spring.datasource.username}")
    private String username;

    @Value("${spring.cloud.azure.credential.client-id}")
    private String clientId;

    @Bean
    @ConditionalOnProperty(name = "spring.datasource.azure.passwordless-enabled", havingValue = "true")
    public DataSource dataSource() throws Exception {
        String accessToken = getAccessToken();
        PGSimpleDataSource dataSource = new PGSimpleDataSource();
        dataSource.setUrl(url);
        dataSource.setUser(username);
        dataSource.setPassword(accessToken);
        return dataSource;
    }

    private String getAccessToken() throws Exception {
        TokenCredential credential = new DefaultAzureCredentialBuilder().managedIdentityClientId(clientId).build();
        AccessToken token = credential.getToken(new TokenRequestContext().addScopes("https://ossrdbms-aad.database.windows.net/.default")).block();
        return token.getToken();
    }
}
