package com.ahss.common.config.interceptor;

import com.ahss.common.utils.masking.SensitiveDataMasker;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.util.ContentCachingRequestWrapper;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;


@Slf4j
@Component
public class LoggingInterceptor implements HandlerInterceptor {

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {

        logMethodAndUrl(request);

        logHeaders(request);

        logAccessToken(request);

        logRequestParameters(request);

        if ("POST".equalsIgnoreCase(request.getMethod()) || "PUT".equalsIgnoreCase(request.getMethod())) {
            logRequestBody(request);
        }

        return true;
    }

    private void logMethodAndUrl(HttpServletRequest request) {
        log.info("HTTP Method: {}", request.getMethod());
        log.info("Request URL: {}", request.getRequestURL());
    }

    private void logHeaders(HttpServletRequest request) {
        Map<String, String> headers = new HashMap<>();
        Enumeration<String> headerNames = request.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();
            headers.put(headerName, request.getHeader(headerName));
        }
        try {
            log.info("Request Headers: {}", objectMapper.writeValueAsString(headers));
        } catch (Exception e) {
            log.warn("Failed to log request headers: {}", e.getMessage());
        }
    }

    private void logAccessToken(HttpServletRequest request) {
        String authorizationHeader = request.getHeader("Authorization");
        if (authorizationHeader != null && authorizationHeader.startsWith("Bearer ")) {
            String token = authorizationHeader.substring(7);
            String maskedToken = token.length() > 10
                ? token.substring(0, 5) + "*****" + token.substring(token.length() - 5)
                : "*****";
            log.info("Access Token (masked): {}", maskedToken);
        } else {
            log.warn("Authorization header not found or invalid.");
        }
    }

    private void logRequestParameters(HttpServletRequest request) {
        Map<String, String[]> parameterMap = request.getParameterMap();
        try {
            Map<String, Object> maskedParams = SensitiveDataMasker.maskSensitiveData(parameterMap);
            log.info("Request Parameters: {}", objectMapper.writeValueAsString(maskedParams));
        } catch (Exception e) {
            log.warn("Failed to log request parameters: {}", e.getMessage());
        }
    }

    private void logRequestBody(HttpServletRequest request) {
        try {
            if (request instanceof ContentCachingRequestWrapper cachingRequest) {
                String body = new String(cachingRequest.getContentAsByteArray(), request.getCharacterEncoding());
                log.info("Request Body: {}", body);
            } else {
                log.warn("Request body cannot be logged because request is not wrapped with ContentCachingRequestWrapper.");
            }
        } catch (Exception e) {
            log.warn("Failed to log request body: {}", e.getMessage());
        }
    }
}
