package com.ahss.common.domain.context;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class UserContext {

  private String email;
  private List<String> memberOf;
  private String sub;
  private String name;
  private String accessToken;
  private List<String> authorities;
  private List<String> products;
  private List<String> tenants;

    public UserContext extractMemberOf(String pattern) {
        Pattern adGroupPattern = Pattern.compile(pattern);
        authorities = new ArrayList<>();
        products = new ArrayList<>();
        tenants = new ArrayList<>();
        memberOf
            .stream()
            .map(adGroupPattern::matcher)
            .filter(Matcher::matches)
            .forEach(matcher -> {
                products.add(matcher.group("product"));
                tenants.add(matcher.group("tenant") + "_" + matcher.group("market"));
                authorities.add(matcher.group("authority"));
            });
        return this;
  }
}
