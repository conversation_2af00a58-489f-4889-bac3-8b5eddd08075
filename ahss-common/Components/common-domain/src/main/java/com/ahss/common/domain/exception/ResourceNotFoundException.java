package com.ahss.common.domain.exception;

import com.ahss.common.api.AHSSResponseCode;
import lombok.Data;

@Data
public class ResourceNotFoundException extends RuntimeException {

    private AHSSResponseCode code;
    private String message;

    public ResourceNotFoundException() {
        super("Resource not found");
        this.code = AHSSResponseCode.RC_404_000;
    }

    public ResourceNotFoundException(String message) {
        super(message);
        this.message = message;
        this.code = AHSSResponseCode.RC_404_000;
    }

    public ResourceNotFoundException(AHSSResponseCode code) {
        super(code.getMessage());
        this.message = code.getMessage();
        this.code = code;
    }

    public ResourceNotFoundException(String message, Throwable throwable) {
        super(message, throwable);
        this.message = message;
        this.code = AHSSResponseCode.RC_404_000;
    }
}
