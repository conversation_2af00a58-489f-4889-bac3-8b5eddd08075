package com.ahss.common.domain.exception;

import com.ahss.common.api.AHSSResponseCode;
import lombok.Data;

@Data
public class BadRequestException extends RuntimeException {

    private String message;
    private AHSSResponseCode code;

    public BadRequestException() {
        super(AHSSResponseCode.RC_400_000.getMessage());
        this.code = AHSSResponseCode.RC_400_000;
    }

    public BadRequestException(String message, AHSSResponseCode code) {
        super(message);
        this.code = code;
        this.message = message;
    }

    public BadRequestException(AHSSResponseCode code) {
        super(code.getMessage());
        this.code = code;
    }

    public BadRequestException(String message, Throwable throwable) {
        super(message, throwable);
        this.message = message;
        this.code = AHSSResponseCode.RC_400_000;
    }
}
