package com.ahss.common.utils.cryptography;

import static org.junit.jupiter.api.Assertions.assertArrayEquals;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertInstanceOf;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.ahss.common.utils.cryptography.model.RsaKeyData;
import com.ahss.common.utils.cryptography.model.SecretKeyData;
import com.nimbusds.jose.jwk.KeyUse;
import com.nimbusds.jose.jwk.RSAKey;
import java.security.KeyPair;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;

@Slf4j
class KeyGeneratorUtilTest {

    @Nested
    @DisplayName("AES Key Generation Tests")
    class AesKeyTests {

        @Test
        @DisplayName("Should generate and convert AES keys correctly")
        void testAesKeyGeneration() throws Exception {
            // Generate a new key
            SecretKeyData keyData = KeyGeneratorUtil.generateSecretKey();

            // Verify key properties
            assertNotNull(keyData.getSecretKey());
            assertEquals(32, keyData.getRawBytes().length); // 256 bits = 32 bytes
            assertNotNull(keyData.getBase64Encoded());
            assertNotNull(keyData.getHexEncoded());

            // Test conversion back from base64
            var secretKeyFromBase64 = KeyGeneratorUtil.base64ToSecretKey(keyData.getBase64Encoded());
            assertArrayEquals(keyData.getRawBytes(), secretKeyFromBase64.getEncoded());

            // Test conversion back from hex
            var secretKeyFromHex = KeyGeneratorUtil.hexToSecretKey(keyData.getHexEncoded());
            assertArrayEquals(keyData.getRawBytes(), secretKeyFromHex.getEncoded());

            log.info("Generated AES key: {}", keyData);
        }

        @Test
        @DisplayName("Should generate password-friendly keys")
        void testPasswordFriendlyKeyGeneration() {
            // Test various lengths
            int[] testLengths = {16, 32, 64};

            for (int length : testLengths) {
                String passwordKey = KeyGeneratorUtil.generateRandomPasswordKey(length);
                assertEquals(length, passwordKey.length());
                assertTrue(passwordKey.matches("^[A-HJ-NP-Za-km-z2-9]+$"), "Key should only contain allowed characters");
            }
        }
    }

    @Nested
    @DisplayName("RSA Key Generation Tests")
    class RsaKeyTests {

        @Test
        @DisplayName("Should generate RSA key pair correctly")
        void testRsaKeyPairGeneration() throws Exception {
            KeyPair keyPair = KeyGeneratorUtil.generateRsaKeyPair();

            assertNotNull(keyPair);
            assertInstanceOf(RSAPublicKey.class, keyPair.getPublic());
            assertInstanceOf(RSAPrivateKey.class, keyPair.getPrivate());

            RSAPublicKey publicKey = (RSAPublicKey) keyPair.getPublic();
            assertEquals(2048, publicKey.getModulus().bitLength());
        }

        @Test
        @DisplayName("Should generate RSA JWK correctly")
        void testRsaJwkGeneration() throws Exception {
            String keyId = UUID.randomUUID().toString();
            RSAKey rsaKey = KeyGeneratorUtil.generateRsaJwk(keyId, KeyUse.SIGNATURE);
            RsaKeyData rsaKeyData = new RsaKeyData(rsaKey);
            log.info("Generated RSA key: {}", rsaKeyData.toString());

            assertNotNull(rsaKey);
            assertTrue(rsaKey.isPrivate());
            assertEquals(keyId, rsaKey.getKeyID());
            assertEquals(KeyUse.SIGNATURE, rsaKey.getKeyUse());
        }

        @Test
        @DisplayName("Should convert between RSA key formats correctly")
        void testRsaKeyConversion() throws Exception {
            // Generate initial key pair
            KeyPair keyPair = KeyGeneratorUtil.generateRsaKeyPair();
            RSAPublicKey publicKey = (RSAPublicKey) keyPair.getPublic();
            RSAPrivateKey privateKey = (RSAPrivateKey) keyPair.getPrivate();

            // Convert to JWK
            String keyId = "test-key";
            RSAKey rsaJwk = KeyGeneratorUtil.convertToRsaJwk(publicKey, privateKey, keyId, KeyUse.SIGNATURE);

            // Verify JWK properties
            assertNotNull(rsaJwk);
            assertTrue(rsaJwk.isPrivate());
            assertEquals(keyId, rsaJwk.getKeyID());
            assertEquals(KeyUse.SIGNATURE, rsaJwk.getKeyUse());

            // Test public key extraction
            RSAKey publicJwk = KeyGeneratorUtil.extractPublicJwk(rsaJwk);
            assertFalse(publicJwk.isPrivate());
            assertEquals(keyId, publicJwk.getKeyID());
        }

        @Test
        @DisplayName("Should convert between JWK and PEM formats")
        void testPemConversion() throws Exception {
            // Generate initial RSA JWK
            RSAKey originalKey = KeyGeneratorUtil.generateRsaJwk("test-key", KeyUse.SIGNATURE);
            RsaKeyData keyData = new RsaKeyData(originalKey);

            // Get PEM formats
            String publicPem = keyData.getPublicKeyPem();
            String privatePem = keyData.getPrivateKeyPem();

            assertNotNull(publicPem);
            assertTrue(publicPem.contains("BEGIN PUBLIC KEY"));
            assertTrue(publicPem.contains("END PUBLIC KEY"));

            assertNotNull(privatePem);
            assertTrue(privatePem.contains("BEGIN PRIVATE KEY"));
            assertTrue(privatePem.contains("END PRIVATE KEY"));

            // Convert back to JWK
            RSAKey reconstructedKey = KeyGeneratorUtil.createRsaJwkFromPem(publicPem, privatePem, "test-key", KeyUse.SIGNATURE);

            // Verify reconstructed key
            assertNotNull(reconstructedKey);
            assertTrue(reconstructedKey.isPrivate());
            assertEquals("test-key", reconstructedKey.getKeyID());
            assertEquals(KeyUse.SIGNATURE, reconstructedKey.getKeyUse());
        }
    }

    @Nested
    @DisplayName("Error Handling Tests")
    class ErrorHandlingTests {

        @Test
        @DisplayName("Should handle invalid PEM format")
        void testInvalidPemHandling() {
            String invalidPem = "invalid-pem-content";

            assertThrows(IllegalStateException.class,
                         () -> KeyGeneratorUtil.createRsaJwkFromPem(invalidPem, invalidPem, "test-key", KeyUse.SIGNATURE));
        }

        @Test
        @DisplayName("Should handle invalid base64 key")
        void testInvalidBase64Handling() {
            String invalidBase64 = "invalid-base64";

            assertThrows(IllegalArgumentException.class, () -> KeyGeneratorUtil.base64ToSecretKey(invalidBase64));
        }

        @Test
        @DisplayName("Should handle invalid hex key")
        void testInvalidHexHandling() {
            String invalidHex = "invalid-hex";

            assertThrows(IllegalArgumentException.class, () -> KeyGeneratorUtil.hexToSecretKey(invalidHex));
        }
    }

    @Test
    @DisplayName("Should perform complete key lifecycle")
    void testCompleteKeyLifecycle() throws Exception {
        // Generate RSA key
        RSAKey originalKey = KeyGeneratorUtil.generateRsaJwk("lifecycle-test", KeyUse.SIGNATURE);
        RsaKeyData keyData = new RsaKeyData(originalKey);

        // Convert to PEM
        String publicPem = keyData.getPublicKeyPem();
        String privatePem = keyData.getPrivateKeyPem();

        // Convert back to JWK
        RSAKey reconstructedKey = KeyGeneratorUtil.createRsaJwkFromPem(publicPem, privatePem, "lifecycle-test", KeyUse.SIGNATURE);

        // Extract public keys
        RSAKey originalPublicKey = KeyGeneratorUtil.extractPublicJwk(originalKey);
        RSAKey reconstructedPublicKey = KeyGeneratorUtil.extractPublicJwk(reconstructedKey);

        // Verify public key equivalence
        assertEquals(originalPublicKey.toJSONString(), reconstructedPublicKey.toJSONString(), "Public keys should match after conversion cycle");

        log.info("Successfully completed key lifecycle test");
    }
}
