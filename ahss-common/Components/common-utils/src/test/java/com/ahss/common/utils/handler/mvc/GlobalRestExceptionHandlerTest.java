package com.ahss.common.utils.handler.mvc;

import com.ahss.common.api.AHSSResponseCode;
import com.ahss.common.api.ResponseStatus;
import com.ahss.common.domain.exception.ResourceNotFoundException;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.AutoConfigurationExcludeFilter;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.context.TypeExcludeFilter;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.server.LocalServerPort;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.ComponentScan.Filter;
import org.springframework.context.annotation.FilterType;
import org.springframework.stereotype.Controller;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ContextConfiguration(classes = {GlobalRestExceptionHandlerTest.TestController.class})
@EnableAutoConfiguration
@ComponentScan(
    excludeFilters = {@Filter(
        type = FilterType.CUSTOM,
        classes = {TypeExcludeFilter.class}
    ), @Filter(
        type = FilterType.CUSTOM,
        classes = {AutoConfigurationExcludeFilter.class}
    )}
)
@AutoConfigureMockMvc
class GlobalRestExceptionHandlerTest {

    private static final String BASE_PATH = "/v1/api/error";
    @LocalServerPort
    private int port;

    @Autowired
    private MockMvc mockMvc;

    @Test
    void resourceNotFoundExceptionTest() throws Exception {
        mockMvc.perform(get("http://localhost:" + port + BASE_PATH + "/notfound")).andDo(print()).andExpect(status().isNotFound())
               .andExpect(jsonPath("$.status").value(ResponseStatus.FAILED.name()))
               .andExpect(jsonPath("$.responseCode.code").value(AHSSResponseCode.RC_404_000.getCode()));
    }

    @Test
    void internalServerErrorTest() throws Exception {
        mockMvc.perform(get("http://localhost:" + port + BASE_PATH + "/500")).andDo(print()).andExpect(status().is5xxServerError())
               .andExpect(jsonPath("$.status").value(ResponseStatus.FAILED.name()))
               .andExpect(jsonPath("$.responseCode.code").value(AHSSResponseCode.RC_500_000.getCode()));
    }

    @Controller
    @RequestMapping(BASE_PATH)
    public static class TestController {

        @GetMapping("/notfound")
        public String error() {
            throw new ResourceNotFoundException();
        }

        @GetMapping("/500")
        public String error500() throws Exception {
            throw new Exception("Internal Server Error");
        }
    }
}