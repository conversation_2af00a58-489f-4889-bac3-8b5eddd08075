package com.ahss.common.utils.cryptography.model;

import lombok.Data;
import javax.crypto.SecretKey;
import java.util.Base64;
import java.util.HexFormat;

/**
 * Generates a secure random AES-256 key and returns it in different formats
 */
@Data
public final class SecretKeyData {
    private final SecretKey secretKey;
    private final String base64Encoded;
    private final String hexEncoded;
    private final byte[] rawBytes;

    public SecretKeyData(SecretKey secretKey) {
        this.secretKey = secretKey;
        this.rawBytes = secretKey.getEncoded();
        this.base64Encoded = Base64.getEncoder().encodeToString(rawBytes);
        this.hexEncoded = HexFormat.of().formatHex(rawBytes);
    }

    public byte[] getRawBytes() {
        return rawBytes.clone(); // Return a copy to prevent modification
    }

    @Override
    public String toString() {
        return """
                Generated AES-256 Key:
                Base64 format: %s
                Hex format: %s
                Key length: %d bits
                """.formatted(base64Encoded, hexEncoded, rawBytes.length * 8);
    }
}
