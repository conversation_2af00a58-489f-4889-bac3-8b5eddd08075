package com.ahss.common.utils.cryptography.model;

import com.ahss.common.utils.cryptography.KeyGeneratorUtil;
import com.nimbusds.jose.jwk.RSAKey;
import lombok.Data;

@Data
public class RsaKeyData {

    private final RSAKey fullKey;
    private final RSAKey publicKey;
    private final String publicKeyPem;
    private final String privateKeyPem;
    private final String kty;
    private final String alg;
    private final String kid;
    private final String use;

    public RsaKeyData(RSAKey rsaKey) {
        try {
            this.fullKey = rsaKey;
            this.publicKey = rsaKey.toPublicJWK();
            this.publicKeyPem = KeyGeneratorUtil.convertToPem(this.publicKey);
            this.privateKeyPem = rsaKey.isPrivate() ? KeyGeneratorUtil.convertToPem(rsaKey) : null;
            this.kty = rsaKey.getKeyType().toString();
            this.alg = rsaKey.getAlgorithm().toString();
            this.kid = rsaKey.getKeyID();
            this.use = rsaKey.getKeyUse().toString();
        } catch (Exception e) {
            throw new IllegalStateException("Failed to create RSA key data", e);
        }
    }

    public String getPublicJwkJson() {
        try {
            return publicKey.toJSONString();
        } catch (Exception e) {
            throw new IllegalStateException("Failed to convert public key to JSON", e);
        }
    }

    public String getFullJwkJson() {
        try {
            return fullKey.toJSONString();
        } catch (Exception e) {
            throw new IllegalStateException("Failed to convert full key to JSON", e);
        }
    }

    public boolean hasPrivateKey() {
        return fullKey.isPrivate();
    }

    @Override
    public String toString() {
        return "RsaKeyData{" +
            "kty='" + kty + '\'' +
            ", alg='" + alg + '\'' +
            ", kid='" + kid + '\'' +
            ", use='" + use + '\'' +
            ", publicKey=" + publicKey +
            '}';
    }
}
