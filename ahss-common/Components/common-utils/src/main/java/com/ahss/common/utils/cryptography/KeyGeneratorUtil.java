package com.ahss.common.utils.cryptography;

import com.ahss.common.utils.cryptography.model.SecretKeyData;
import com.nimbusds.jose.Algorithm;
import com.nimbusds.jose.JOSEException;
import com.nimbusds.jose.JWSAlgorithm;
import com.nimbusds.jose.jwk.KeyUse;
import com.nimbusds.jose.jwk.RSAKey;
import com.nimbusds.jose.jwk.gen.RSAKeyGenerator;
import java.security.KeyFactory;
import java.security.KeyPair;
import java.security.KeyPairGenerator;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.util.Base64;
import java.util.HexFormat;
import java.util.UUID;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class KeyGeneratorUtil {

    private static final int KEY_SIZE_BITS = 256;
    private static final int RSA_KEY_SIZE = 2048;
    private static final String KEY_ALGO = "AES";
    private static final String RSA_ALGO = "RSA";

    private KeyGeneratorUtil() {
    }

    /**
     * Generates a cryptographically secure random key for AES-256
     */
    public static SecretKeyData generateSecretKey() throws NoSuchAlgorithmException {
        KeyGenerator keyGen = KeyGenerator.getInstance(KEY_ALGO);
        keyGen.init(KEY_SIZE_BITS, SecureRandom.getInstanceStrong());
        SecretKey secretKey = keyGen.generateKey();
        return new SecretKeyData(secretKey);
    }

    /**
     * Converts a base64 encoded key string back to a SecretKey
     */
    public static SecretKey base64ToSecretKey(String base64Key) {
        byte[] decodedKey = Base64.getDecoder().decode(base64Key);
        return new SecretKeySpec(decodedKey, KEY_ALGO);
    }

    /**
     * Converts a hex encoded key string back to a SecretKey
     */
    public static SecretKey hexToSecretKey(String hexKey) {
        byte[] decodedKey = HexFormat.of().parseHex(hexKey);
        return new SecretKeySpec(decodedKey, KEY_ALGO);
    }

    /**
     * Generates a random password-friendly key string
     */
    public static String generateRandomPasswordKey(int length) {
        String allowedChars = "ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz23456789";
        SecureRandom random = new SecureRandom();
        StringBuilder key = new StringBuilder(length);

        for (int i = 0; i < length; i++) {
            int randomIndex = random.nextInt(allowedChars.length());
            key.append(allowedChars.charAt(randomIndex));
        }

        return key.toString();
    }

    /**
     * Generates an RSA key pair using the default RSA key size
     */
    public static KeyPair generateRsaKeyPair() throws NoSuchAlgorithmException {
        KeyPairGenerator keyPairGenerator = KeyPairGenerator.getInstance(RSA_ALGO);
        keyPairGenerator.initialize(RSA_KEY_SIZE, SecureRandom.getInstanceStrong());
        return keyPairGenerator.generateKeyPair();
    }

    /**
     * Generates an RSA JWK (JSON Web Key) with specified parameters
     */
    public static RSAKey generateRsaJwk(String keyId, KeyUse keyUse) throws JOSEException {
        String finalKeyId = keyId != null ? keyId : UUID.randomUUID().toString();

        RSAKey rsaKey = new RSAKeyGenerator(RSA_KEY_SIZE)
            .keyUse(keyUse)
            .keyID(finalKeyId)
            .algorithm(JWSAlgorithm.RS256)  // Explicitly set RS256
            .generate();

        log.debug("Generated RSA key with ID: {}, Algorithm: {}, Use: {}",
                  rsaKey.getKeyID(),
                  rsaKey.getAlgorithm(),
                  rsaKey.getKeyUse());

        return rsaKey;
    }

    /**
     * Converts RSA public and private keys to RSA JWK format
     */
    public static RSAKey convertToRsaJwk(RSAPublicKey publicKey, RSAPrivateKey privateKey, String keyId, KeyUse keyUse) {
        String finalKeyId = keyId != null ? keyId : UUID.randomUUID().toString();

        RSAKey.Builder builder = new RSAKey.Builder(publicKey)
            .keyID(finalKeyId)
            .keyUse(keyUse)
            .algorithm(JWSAlgorithm.RS256);  // Explicitly set RS256

        if (privateKey != null) {
            builder.privateKey(privateKey);
        }

        RSAKey rsaKey = builder.build();
        log.debug("Converted RSA key with ID: {}, Algorithm: {}, Use: {}",
                  rsaKey.getKeyID(),
                  rsaKey.getAlgorithm(),
                  rsaKey.getKeyUse());

        return rsaKey;
    }

    /**
     * Extracts the public key portion from an RSA JWK
     */
    public static RSAKey extractPublicJwk(RSAKey fullRsaKey) {
        return fullRsaKey.toPublicJWK();
    }

    /**
     * Converts RSA JWK to PEM format
     */
    public static String convertToPem(RSAKey rsaKey) {
        try {
            StringBuilder pem = new StringBuilder();
            if (rsaKey.isPrivate()) {
                pem.append("-----BEGIN PRIVATE KEY-----\n");
                pem.append(Base64.getEncoder().encodeToString(rsaKey.toRSAPrivateKey().getEncoded()));
                pem.append("\n-----END PRIVATE KEY-----");
            } else {
                pem.append("-----BEGIN PUBLIC KEY-----\n");
                pem.append(Base64.getEncoder().encodeToString(rsaKey.toRSAPublicKey().getEncoded()));
                pem.append("\n-----END PUBLIC KEY-----");
            }
            return pem.toString();
        } catch (JOSEException e) {
            log.error("Failed to convert RSA key to PEM format", e);
            throw new IllegalStateException("Failed to convert RSA key to PEM format", e);
        }
    }

    /**
     * Creates an RSA JWK from PEM formatted strings
     */
    public static RSAKey createRsaJwkFromPem(String publicKeyPem, String privateKeyPem, String keyId, KeyUse keyUse) {
        try {
            String finalKeyId = keyId != null ? keyId : UUID.randomUUID().toString();

            // Remove PEM headers and footers and decode
            String publicKeyB64 = publicKeyPem
                .replace("-----BEGIN PUBLIC KEY-----", "")
                .replace("-----END PUBLIC KEY-----", "")
                .replaceAll("\\s", "");

            KeyFactory keyFactory = KeyFactory.getInstance(RSA_ALGO);
            byte[] publicKeyBytes = Base64.getDecoder().decode(publicKeyB64);

            RSAPublicKey publicKey = (RSAPublicKey) keyFactory.generatePublic(
                new java.security.spec.X509EncodedKeySpec(publicKeyBytes)
                                                                             );

            RSAPrivateKey privateKey = null;
            if (privateKeyPem != null) {
                String privateKeyB64 = privateKeyPem
                    .replace("-----BEGIN PRIVATE KEY-----", "")
                    .replace("-----END PRIVATE KEY-----", "")
                    .replaceAll("\\s", "");
                byte[] privateKeyBytes = Base64.getDecoder().decode(privateKeyB64);
                privateKey = (RSAPrivateKey) keyFactory.generatePrivate(
                    new java.security.spec.PKCS8EncodedKeySpec(privateKeyBytes)
                                                                       );
            }

            RSAKey rsaKey = new RSAKey.Builder(publicKey)
                .keyID(finalKeyId)
                .keyUse(keyUse)
                .algorithm(JWSAlgorithm.RS256)  // Explicitly set RS256
                .privateKey(privateKey)
                .build();

            log.debug("Created RSA key from PEM with ID: {}, Algorithm: {}, Use: {}",
                      rsaKey.getKeyID(),
                      rsaKey.getAlgorithm(),
                      rsaKey.getKeyUse());

            return rsaKey;

        } catch (Exception e) {
            log.error("Failed to create RSA JWK from PEM", e);
            throw new IllegalStateException("Failed to create RSA JWK from PEM", e);
        }
    }
}
