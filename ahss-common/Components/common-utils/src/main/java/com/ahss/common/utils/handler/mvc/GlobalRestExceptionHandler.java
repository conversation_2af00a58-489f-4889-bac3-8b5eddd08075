package com.ahss.common.utils.handler.mvc;

import com.ahss.common.api.AHSSResponseCode;
import com.ahss.common.api.ApiResponse;
import com.ahss.common.api.ResponseStatus;
import com.ahss.common.domain.exception.AccessDeniedException;
import com.ahss.common.domain.exception.BadRequestException;
import com.ahss.common.domain.exception.ResourceNotFoundException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.security.authorization.AuthorizationDeniedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.context.request.WebRequest;
import org.springframework.web.method.annotation.HandlerMethodValidationException;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;

@Slf4j
@RestControllerAdvice
public class GlobalRestExceptionHandler {

    // Handle specific exceptions (e.g., ResourceNotFoundException)
    @ExceptionHandler(ResourceNotFoundException.class)
    @org.springframework.web.bind.annotation.ResponseStatus(HttpStatus.NOT_FOUND)
    public ResponseEntity<ApiResponse<String, Object>> handleResourceNotFoundException(ResourceNotFoundException ex, WebRequest request) {
        return ResponseEntity.status(HttpStatus.NOT_FOUND).body(
            ApiResponse.<String, Object>builder()
                       .status(ResponseStatus.FAILED)
                       .responseCode(ex.getCode())
                       .data(ex.getCode().getDetailMessage()).build());
    }

    @ExceptionHandler(HttpMessageNotReadableException.class)
    @org.springframework.web.bind.annotation.ResponseStatus(HttpStatus.BAD_REQUEST)
    public ResponseEntity<ApiResponse<String, Object>> handleHttpMessageNotReadableException(HttpMessageNotReadableException ex) {
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(
            ApiResponse.<String, Object>builder()
                       .status(ResponseStatus.FAILED)
                       .responseCode(AHSSResponseCode.RC_400_002)
                       .data(AHSSResponseCode.RC_400_002.getDetailMessage()).build());
    }

    @ExceptionHandler(BadRequestException.class)
    @org.springframework.web.bind.annotation.ResponseStatus(HttpStatus.BAD_REQUEST)
    public ResponseEntity<ApiResponse<String, Object>> handleBadRequestException(BadRequestException ex) {
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(
            ApiResponse.<String, Object>builder()
                       .status(ResponseStatus.FAILED)
                       .responseCode(ex.getCode())
                       .data(ex.getCode().getDetailMessage()).build());
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    @org.springframework.web.bind.annotation.ResponseStatus(HttpStatus.BAD_REQUEST)
    public ResponseEntity<ApiResponse<String, Object>> handleMethodArgumentNotValidException(MethodArgumentNotValidException ex) {
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(
            ApiResponse.<String, Object>builder()
                       .status(ResponseStatus.FAILED)
                       .responseCode(AHSSResponseCode.RC_400_002)
                       .data(AHSSResponseCode.RC_400_002.getDetailMessage()).build());
    }

    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    @org.springframework.web.bind.annotation.ResponseStatus(HttpStatus.BAD_REQUEST)
    public ResponseEntity<ApiResponse<String, Object>> handleMethodArgumentTypeMismatchException(MethodArgumentTypeMismatchException ex) {
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(
            ApiResponse.<String, Object>builder()
                       .status(ResponseStatus.FAILED)
                       .responseCode(AHSSResponseCode.RC_400_002)
                       .data(AHSSResponseCode.RC_400_002.getDetailMessage()).build());
    }

    @ExceptionHandler(HandlerMethodValidationException.class)
    @org.springframework.web.bind.annotation.ResponseStatus(HttpStatus.BAD_REQUEST)
    public ResponseEntity<ApiResponse<String, Object>> handleHandlerMethodValidationException(HandlerMethodValidationException ex) {
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(
            ApiResponse.<String, Object>builder()
                       .status(ResponseStatus.FAILED)
                       .responseCode(AHSSResponseCode.RC_400_002)
                       .data(AHSSResponseCode.RC_400_002.getDetailMessage()).build());
    }

    @ExceptionHandler(AuthorizationDeniedException.class)
    @org.springframework.web.bind.annotation.ResponseStatus(HttpStatus.FORBIDDEN)
    public ResponseEntity<ApiResponse<String, Object>> handleAuthorizationDeniedException(AuthorizationDeniedException ex, WebRequest request) {
        return ResponseEntity.status(HttpStatus.FORBIDDEN).body(
            ApiResponse.<String, Object>builder()
                       .status(ResponseStatus.FAILED)
                       .responseCode(AHSSResponseCode.RC_403_000)
                       .data(AHSSResponseCode.RC_403_000.getDetailMessage())
                       .build());
    }

    @ExceptionHandler(AccessDeniedException.class)
    @org.springframework.web.bind.annotation.ResponseStatus(HttpStatus.FORBIDDEN)
    public ResponseEntity<ApiResponse<String, Object>> handleAccessDeniedException(AccessDeniedException ex, WebRequest request) {
        return ResponseEntity.status(HttpStatus.FORBIDDEN).body(
            ApiResponse.<String, Object>builder()
                       .status(ResponseStatus.FAILED)
                       .responseCode(AHSSResponseCode.RC_403_000)
                       .data(AHSSResponseCode.RC_403_000.getDetailMessage())
                       .build());
    }


    // Handle all other exceptions
    @ExceptionHandler(Exception.class)
    @org.springframework.web.bind.annotation.ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ResponseEntity<ApiResponse<String, Object>> handleGlobalException(Exception ex, WebRequest request) {
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(
            ApiResponse.<String, Object>builder()
                       .status(ResponseStatus.FAILED)
                       .responseCode(AHSSResponseCode.RC_500_000)
                       .data(ex.getMessage())
                       .build());
    }
}
