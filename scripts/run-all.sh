#!/bin/bash

set -e  # Exit immediately if a command exits with a non-zero status
set -o pipefail  # Return the exit status of the last command in the pipe that failed

# Default values
SPRING_PROFILE="integration"
MAVEN_PROFILE="local-docker"
DOCKER_COMPOSE_FILE="./docker/all-in-one.yml"

# Function to print a header message
function print_header() {
  echo "---------------------------------------"
  echo "$1"
  echo "---------------------------------------"
}

# Parse command-line arguments
while [[ $# -gt 0 ]]; do
  case "$1" in
    -p|--profile)
      SPRING_PROFILE="$2"
      shift 2
      ;;
    -m|--maven-profile)
      MAVEN_PROFILE="$2"
      shift 2
      ;;
    -d|--docker-compose)
      DOCKER_COMPOSE_FILE="$2"
      shift 2
      ;;
    -h|--help)
      echo "Usage: $0 [-p|--profile <spring-profile>] [-m|--maven-profile <maven-profile>] [-d|--docker-compose <compose-file>]"
      exit 0
      ;;
    *)
      echo "Unknown argument: $1"
      exit 1
      ;;
  esac
done

print_header "Compiling the whole project and running integration tests"
mvn clean install surefire-report:report -Daggregate=true -Dspring.profiles.active="${SPRING_PROFILE}"

print_header "Building ahss-gateway-web..."
mvn -pl ahss-gateway/Interface/ahss-gateway-web compile jib:dockerBuild -P "${MAVEN_PROFILE}"

print_header "Building ahss-product-portal-web..."
mvn -pl ahss-product-portal/Interface/ahss-pp-web compile jib:dockerBuild -P "${MAVEN_PROFILE}"

print_header "Building ahss-uam-web..."
mvn -pl ahss-uam/Interface/ahss-uam-web compile jib:dockerBuild -P "${MAVEN_PROFILE}"

print_header "Running Docker Compose"
docker compose -f "${DOCKER_COMPOSE_FILE}" up