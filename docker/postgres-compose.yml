services:
  local-ahss-postgres:
    image: postgres:15.7
    container_name: local-ahss-postgres
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_PASSWORD=password
      - POSTGRES_USER=ahss
      - POSTGRES_DB=dev_ahss
      - PATH=/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/lib/postgresql/15/bin
      - GOSU_VERSION=1.17
      - LANG=en_US.utf8
      - PG_MAJOR=15
      - PG_VERSION=15
      - PGDATA=/var/lib/postgresql/data
    networks:
      - ahss-network

networks:
  ahss-network: {}