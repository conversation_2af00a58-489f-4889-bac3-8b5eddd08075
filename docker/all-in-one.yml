services:
  local-ahss-postgres:
    image: postgres:15.7
    container_name: local-ahss-postgres
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_PASSWORD=password
      - POSTGRES_USER=ahss
      - POSTGRES_DB=dev_ahss
      - PATH=/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/lib/postgresql/15/bin
      - GOSU_VERSION=1.17
      - LANG=en_US.utf8
      - PG_MAJOR=15
      - PG_VERSION=15
      - PGDATA=/var/lib/postgresql/data
    networks:
      - ahss-network
  ahss-gateway-web:
    image: ahss-gateway-web:0.0.1-SNAPSHOT
    container_name: ahss-gateway-web
    ports:
      - "8888:8888"
    environment:
      - AHPP_PATH=/v1/api/pp/**
      - AHPP_SCHEME=http
      - AHPP_HOST=ahss-pp-web
      - AHPP_PORT=8001
      - UAM_PATH=/v1/api/uam/**
      - UAM_SCHEME=http
      - UAM_HOST=ahss-uam-web
      - UAM_PORT=8011
      - METRICS_COLLECTOR_ENDPOINT=http://local-ahss-jaeger:9411/api/v2/spans
    networks:
      - ahss-network
    depends_on:
      - ahss-pp-web
      - local-ahss-jaeger
  ahss-pp-web:
    image: ahss-pp-web:0.0.1-SNAPSHOT
    container_name: ahss-pp-web
    ports:
      - "8001:8001"
    environment:
      - DB_URL=***************************************************
      - DB_USERNAME=ahss
      - DB_PASSWORD=password
      - METRICS_COLLECTOR_ENDPOINT=http://local-ahss-jaeger:9411/api/v2/spans
    networks:
      - ahss-network
    depends_on:
      - local-ahss-postgres
      - local-ahss-jaeger
  ahss-uam-web:
    image: ahss-uam-web:0.0.1-SNAPSHOT
    container_name: ahss-uam-web
    ports:
      - "8011:8011"
    environment:
      - DB_URL=***************************************************
      - DB_USERNAME=ahss
      - DB_PASSWORD=password
      - METRICS_COLLECTOR_ENDPOINT=http://local-ahss-jaeger:9411/api/v2/spans
    networks:
      - ahss-network
    depends_on:
      - local-ahss-postgres
      - local-ahss-jaeger
  local-ahss-jaeger:
    image: jaegertracing/all-in-one:latest
    container_name: local-ahss-jaeger
    ports:
      - "16686:16686" # the jaeger UI
      - "6831:6831/udp"
      - "6832:6832/udp"
      - "5778:5778"
      - "4317:4317" # the OpenTelemetry collector grpc
      - "4318:4318"
      - "14250:14250"
      - "14268:14268"
      - "14269:14269"
      - "9411:9411" # Zipkin
    environment:
      - COLLECTOR_ZIPKIN_HOST_PORT=:9411
    networks:
      - ahss-network
networks:
  ahss-network: {}