# Amplify Health Shared Services

## Description

## Services

- [Amplify Health Shared Services Gateway](./ahss-gateway/AHSSGW.md)
- [Amplify Health Product Portal](./ahss-product-portal/AHPP.md)
- [Amplify Health UAM](./ahss-uam/UAM.md)

## Local Setup

### Docker

### Build Tools

We use `maven` as our build tool. Please install you maven by following the instruction [here](https://maven.apache.org/index.html)

### Java SDK

- We use Java 21 and the minimum Java SDK is 17 as it is the minimum java version supported by Spring Boot 3

### Database

You can run the docker-compose file to bring up your local database.

```shell
docker compose -f ./docker/local-compose.yml up -d
```

### Flyway
Flyway `migrate` is run together with Spring Boot application at startup time. However, you can run your flyway with `maven`
```shell
# ahss-product-portal
mvn flyway:migrate -pl ahss-product-portal/Components/ahss-pp-db

# ahss-uam
mvn flyway:migrate -pl ahss-product-portal/Components/ahss-pp-db
```

### JOOQ
#### Generate sources from Database 
```shell
# ahss-product-portal
mvn generate-sources -P generate-jooq -pl ahss-product-portal/Components/ahss-pp-db -amd

# ahss-uam
mvn generate-sources -P generate-jooq -pl ahss-uam/Components/ahss-uam-db -amd
```

## How to Run

### Start Spring Boot App

#### Build with `profile`

- Profile `default` (without environment variable)
    ```shell
    mvn clean install -D spring.profiles.active=default
    ```
  Or you can set the enviroment variable `SPRING_PROFILES_ACTIVE` before running you command
    ```shell
    SPRING_PROFILES_ACTIVE=default mvn clean install
    ```


- Profile `integration`
  This test is expensive and only run with `pre-push`. This is default profile when you try to push code to your branch.
    ```shell
    mvn clean install -D spring.profiles.active=integration
    ```

- **Resume** from failed module/tests
    ```shell
    mvn clean install -rf :ahss-pp-web -D spring.profiles.active=integration
    ```

#### Build individual modules

- `default` profile is used by **default**
    ```shell
    mvn clean install -pl ahss-gateway,ahss-product-portal -amd
    ```

- **selected** profile
    ```shell
    mvn clean install -pl ahss-gateway,ahss-product-portal -amd -D spring.profiles.active=integration
    ```

#### Build with **cache**

```shell
mvn clean install -D spring.profiles.active=integration -D maven.build.cache.enabled=true
```

#### Build with `surefire-report`

Run sure-fire report command and aggregate report at the root module level

```shell
mvn clean install surefire-report:report -D aggregate=true
```

## Build Docker Image with `Google Jib Plugin`

### Local Docker

| Docker Service      | Container Name      | Version/Tag    | Port |
|---------------------|---------------------|----------------|------|
| Postgres            | ahss-local-postgres | 15.7           | 5432 |
| AHSS Gateway        | ahss-gateway-web    | 0.0.1-SNAPSHOT | 8888 |
| AHSS Product Portal | ahss-pp-web         | 0.0.1-SNAPSHOT | 8001 |
| AHSS UAM            | ahss-uam-web        | 0.0.1-SNAPSHOT | 8011 |

#### Profiles

We can set spring profile with:

- JVM Arg: `-D spring.profiles.active=default`
- System Env: `SPRING_PROFILES_ACTIVE=default`

| Spring Profile | Properties/Yaml               | Remarks                                                                                                                                       | |
|----------------|-------------------------------|-----------------------------------------------------------------------------------------------------------------------------------------------|-|
| default        | `application.yml`             |                                                                                                                                               | |
| dev            | `application-dev.yml`         |                                                                                                                                               | |
| sit            | `application-sit.yml`         |                                                                                                                                               | |
| uat            | `application-uat.yml`         |                                                                                                                                               | |
| prod           | `application-prod.yml`        |                                                                                                                                               | |
| integration    | `application-integration.yml` | This profile `MUST` be used before pushing any code to repo. `pre-push` script should be copied from `./script/` directory to `./.git/hooks/` | |

#### **ahss-gateway-web**

- Build module and create docker image

```shell
# build modules and dependencies
mvn clean install -pl ahss-gateway -amd -P local-docker -D spring.profiles.active=integration
# build image       
mvn -pl ahss-gateway/Interface/ahss-gateway-web compile jib:dockerBuild -P local-docker
```

- Run docker

```shell
# run docker container
docker run --name ahss-gateway-web -p 8888:8888 ahss-pp-web:0.0.1-SNAPSHOT
```

#### **ahss-pp-web**

- Build module and create docker image

```shell
# build modules and dependencies
mvn clean install -pl ahss-product-portal -amd -P local-docker -D spring.profiles.active=integration           
# build image
mvn -pl ahss-product-portal/Interface/ahss-pp-web compile jib:dockerBuild -P local-docker
```

- Run docker

```shell
# run docker container
docker run -e "DB_URL=****************************************************" \
-e "DB_USERNAME=ahss" \
-e "DB_PASSWORD=password" \
-p 8001:8001 --name ahss-pp-web ahss-pp-web:0.0.1-SNAPSHOT
```

#### **ahss-uam-web**

- Build module and create docker image

```shell
# build modules and dependencies
mvn clean install -pl ahss-uam -amd -P local-docker -D spring.profiles.active=integration           
# build image
mvn -pl ahss-uam/Interface/ahss-uam-web compile jib:dockerBuild -P local-docker
```

- Run docker

```shell
# run docker container
docker run -e "DB_URL=****************************************************" \
-e "DB_USERNAME=ahss" \
-e "DB_PASSWORD=password" \
-p 8011:8011 --name ahss-uam-web ahss-uam-web:0.0.1-SNAPSHOT
```

#### All-In-One docker compose

- Bring up the services

```shell
docker compose -f ./docker/all-in-one.yml up
```

- Bring down the services

```shell
docker compose -f ./docker/all-in-one.yml down
```

### Azure Container Registry

Build and push docker image to ACR with profile `azure-acr`

```shell
mvn -pl ahss-gateway/Interface/ahss-gateway-web compile jib:dockerBuild -P azure-acr
```

### All-In-One `run-all.sh` script

- Usage

```shell
./scripts/run-all.sh -h
# Usage: ./scripts/run-all.sh [-p|--profile <spring-profile>] [-m|--maven-profile <maven-profile>] [-d|--docker-compose <compose-file>]
```

- Example

```shell
./scripts/run-all.sh -p integration -m local-docker
```

## Git Hooks

### Pre-push

- Copy the `pre-push` script to your project `.git/hooks/` folder

```shell
cp ./scripts/pre-push ./.git/hooks/
```
