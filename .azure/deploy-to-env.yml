parameters:
  - name: environmentName
    type: string
  - name: namespace
    type: string
  - name: kubernetesServiceEndpoint
    type: string
  - name: conditionFlag
    type: string
  - name: serviceHelmPairs
    type: object

steps:
  - template: ensure-docker-running.yml

  - template: login-to-acr.yml

  # Loop through each serviceHelmPair and deploy each service
  - ${{ each pair in parameters.serviceHelmPairs }}:
      - task: HelmDeploy@0
        displayName: "Deploy ${{ pair.serviceName }} to ${{ parameters.environmentName }} Environment"
        inputs:
          connectionType: 'Kubernetes Service Connection'
          kubernetesServiceEndpoint: '${{ parameters.kubernetesServiceEndpoint }}'
          azureContainerRegistry: $(CONTAINER_REGISTRY_SERVICE_CONNECTION)
          namespace: "${{ parameters.namespace }}"
          command: 'upgrade'
          chartType: 'FilePath'
          chartPath: "helm/${{ pair.serviceName }}/app-template"
          valueFile: "${{ pair.helmValuesFile }}"
          releaseName: "${{ pair.serviceName }}"
          arguments: '--install --timeout 10m --atomic'
          overrideValues: "controllers.main.containers.main.image.tag=$(IMAGE_TAG)"
