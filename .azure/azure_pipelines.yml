trigger:
  batch: true
  branches:
    include:
      - 'main'
      - 'develop'
      - 'uat'
      - 'sit'
      - 'feature/*gateway*'
      - 'feature/*product-portal*'
      - 'feature/*uam*'
      - 'release/*'
      - 'hotfix/*'
      - 'feat/az-pipeline'
pr: none

pool:
  name: ah01_selfhosted
  demands:
    - docker
    - CONNECT_TO_KUBERNETES
    - CONNECT_TO_ACR
    - MEMORY_500M

variables:
  - group: ahss-common
  - name: PROJECT_NAME
    value: ahss
  - name: IMAGE_NAME
    value: ah-product-portal
#  - name: CONTAINER_REGISTRY_SERVICE_CONNECTION
#    value: 'ADO-ACR-SERVICE-CONNECTION'

stages:
  - stage: DetermineBranch
    displayName: Determine Branch
    jobs:
      - job: SetDeploymentFlags
        displayName: Determine Deployment Flags
        steps:
          - script: |
              echo "Determining branch type..."

              # Capture the source branch in a variable for easier debugging
              BRANCH_NAME=$(Build.SourceBranch)
              echo "Source Branch: $BRANCH_NAME"
              COMMIT_HASH=$(echo $(Build.SourceVersion) | cut -c1-7)
              
              # Determine and set deployment flags based on branch type
              if [[ "$BRANCH_NAME" == "refs/heads/develop" ]]; then
                echo "Branch is develop. Setting deployment flags for DEV environment."
                echo "##vso[task.setvariable variable=IMAGE_TAG;isOutput=true]develop-${COMMIT_HASH}"
                echo "##vso[task.setvariable variable=DEPLOY_DEV;isOutput=true]true"
                echo "##vso[task.setvariable variable=DEPLOY_GATEWAY;isOutput=true]true"
                echo "##vso[task.setvariable variable=DEPLOY_PP;isOutput=true]true"
                echo "##vso[task.setvariable variable=DEPLOY_UAM;isOutput=true]true"
                echo "##vso[task.setvariable variable=CONTAINER_REGISTRY_SERVICE_CONNECTION;isOutput=true]ADO-ACR-SERVICE-CONNECTION"
                echo "##vso[task.setvariable variable=ACR_AHSS_LOGIN_SERVER;isOutput=true]acrah01seadahss01.azurecr.io"
              elif [[ "$BRANCH_NAME" == "refs/heads/uat" ]]; then
                echo "Branch is uat. Setting deployment flags for UAT environment."
                echo "##vso[task.setvariable variable=IMAGE_TAG;isOutput=true]uat-${COMMIT_HASH}"
                echo "##vso[task.setvariable variable=DEPLOY_UAT;isOutput=true]true"   
                echo "##vso[task.setvariable variable=CONTAINER_REGISTRY_SERVICE_CONNECTION;isOutput=true]ADO-ACR-SERVICE-CONNECTION"
                echo "##vso[task.setvariable variable=ACR_AHSS_LOGIN_SERVER;isOutput=true]acrah01seadahss01.azurecr.io"                
              elif [[ "$BRANCH_NAME" == "refs/heads/sit" ]]; then
                echo "Branch is sit. Setting deployment flags for SIT environment."
                echo "##vso[task.setvariable variable=IMAGE_TAG;isOutput=true]sit-${COMMIT_HASH}"
                echo "##vso[task.setvariable variable=DEPLOY_SIT;isOutput=true]true"
                echo "##vso[task.setvariable variable=CONTAINER_REGISTRY_SERVICE_CONNECTION;isOutput=true]ADO-ACR-SERVICE-CONNECTION"
                echo "##vso[task.setvariable variable=ACR_AHSS_LOGIN_SERVER;isOutput=true]acrah01seadahss01.azurecr.io"              
              elif [[ "$BRANCH_NAME" == "refs/heads/main" ]]; then
                echo "Branch is main. Setting deployment flags for Production environment."
                echo "##vso[task.setvariable variable=IMAGE_TAG;isOutput=true]prod-${COMMIT_HASH}"
                echo "##vso[task.setvariable variable=DEPLOY_PROD;isOutput=true]true"   
                echo "##vso[task.setvariable variable=CONTAINER_REGISTRY_SERVICE_CONNECTION;isOutput=true]PROD-ACR-SERVICE-CONNECTION"
                echo "##vso[task.setvariable variable=ACR_AHSS_LOGIN_SERVER;isOutput=true]acrah01seapahss01.azurecr.io"
              elif [[ "$BRANCH_NAME" == refs/heads/release/* ]]; then
                echo "Branch is release. Setting deployment flag to deploy all."
                echo "##vso[task.setvariable variable=DEPLOY_ALL;isOutput=true]true"
              elif [[ "$BRANCH_NAME" == refs/heads/feature/* ]]; then
                echo "Branch is feature. Checking feature flags."
                if [[ "$BRANCH_NAME" == *"gateway"* ]]; then
                  echo "Feature branch contains 'gateway'. Setting DEPLOY_GATEWAY flag."
                  echo "##vso[task.setvariable variable=IMAGE_TAG;isOutput=true]develop-${COMMIT_HASH}"
                  echo "##vso[task.setvariable variable=DEPLOY_GATEWAY;isOutput=true]true"
                  echo "##vso[task.setvariable variable=CONTAINER_REGISTRY_SERVICE_CONNECTION;isOutput=true]ADO-ACR-SERVICE-CONNECTION"
                  echo "##vso[task.setvariable variable=ACR_AHSS_LOGIN_SERVER;isOutput=true]acrah01seadahss01.azurecr.io"              
                fi
                if [[ "$BRANCH_NAME" == *"product-portal"* ]]; then
                  echo "Feature branch contains 'product-portal'. Setting DEPLOY_PP flag."
                  echo "##vso[task.setvariable variable=IMAGE_TAG;isOutput=true]develop-${COMMIT_HASH}"
                  echo "##vso[task.setvariable variable=DEPLOY_PP;isOutput=true]true"
                  echo "##vso[task.setvariable variable=CONTAINER_REGISTRY_SERVICE_CONNECTION;isOutput=true]ADO-ACR-SERVICE-CONNECTION"
                  echo "##vso[task.setvariable variable=ACR_AHSS_LOGIN_SERVER;isOutput=true]acrah01seadahss01.azurecr.io"              
                fi
                if [[ "$BRANCH_NAME" == *"uam"* ]]; then
                  echo "Feature branch contains 'uam'. Setting DEPLOY_UAM flag."
                  echo "##vso[task.setvariable variable=IMAGE_TAG;isOutput=true]develop-${COMMIT_HASH}"
                  echo "##vso[task.setvariable variable=DEPLOY_UAM;isOutput=true]true"
                  echo "##vso[task.setvariable variable=CONTAINER_REGISTRY_SERVICE_CONNECTION;isOutput=true]ADO-ACR-SERVICE-CONNECTION"
                  echo "##vso[task.setvariable variable=ACR_AHSS_LOGIN_SERVER;isOutput=true]acrah01seadahss01.azurecr.io"            
                fi
              elif [[ "$BRANCH_NAME" == refs/heads/hotfix/* ]]; then
                echo "Branch is hotfix. Checking hotfix flags."
                if [[ "$BRANCH_NAME" == *"gateway"* ]]; then
                  echo "Hotfix branch contains 'gateway'. Setting DEPLOY_GATEWAY flag."
                  echo "##vso[task.setvariable variable=IMAGE_TAG;isOutput=true]develop-${COMMIT_HASH}"
                  echo "##vso[task.setvariable variable=DEPLOY_GATEWAY;isOutput=true]true"        
                fi
                if [[ "$BRANCH_NAME" == *"product-portal"* ]]; then
                  echo "Hotfix branch contains 'product-portal'. Setting DEPLOY_PP flag."
                  echo "##vso[task.setvariable variable=IMAGE_TAG;isOutput=true]develop-${COMMIT_HASH}"
                  echo "##vso[task.setvariable variable=DEPLOY_PP;isOutput=true]true"
                fi
                if [[ "$BRANCH_NAME" == *"uam"* ]]; then
                  echo "Hotfix branch contains 'uam'. Setting DEPLOY_UAM flag."
                  echo "##vso[task.setvariable variable=IMAGE_TAG;isOutput=true]develop-${COMMIT_HASH}"
                  echo "##vso[task.setvariable variable=DEPLOY_UAM;isOutput=true]true"
                fi
              else
                echo "Branch does not match any specific environment criteria."
              fi
            name: DeploymentFlags
            displayName: 'Determine Deployment Flags Based on Branch'

  - stage: Scan_SAST_SCA
    displayName: Scan_SAST_SCA
    condition: ne(variables['Build.SourceBranch'], 'refs/heads/main')
    jobs:
      - template: scan-sast-sca-veracode.yml
        parameters:
          projectName: $(PROJECT_NAME)
          agentMemory: 6G
          jdkVersionOption: 1.17
          projectFolder: ''
          excludeFolderSAST: ''
          excludeFolderSCA: ''

  - stage: Build
    displayName: Build
    variables:
    - name: IMAGE_TAG
      value: $[ stageDependencies.DetermineBranch.SetDeploymentFlags.outputs['DeploymentFlags.IMAGE_TAG'] ]
    - name: ACR_AHSS_LOGIN_SERVER
      value: $[ stageDependencies.DetermineBranch.SetDeploymentFlags.outputs['DeploymentFlags.ACR_AHSS_LOGIN_SERVER'] ]
    dependsOn: [DetermineBranch]
    jobs:
      - job: Build_Container_Image
        displayName: image_build
        pool:
          name: ah01_selfhosted
          demands:
            - docker
            - CONNECT_TO_KUBERNETES
            - CONNECT_TO_ACR
            - MEMORY_500M
        variables:
          IMAGE_NAME: ah-product-portal
          BRANCH_NAME: $(Build.SourceBranch)
        steps:
          - template: login-to-acr.yml
          - template: ensure-docker-running.yml
          - script: |
              docker pull acrah01seadahss01.azurecr.io/ci-image-base/veracode-prisma-java:latest
              cd $(Agent.BuildDirectory) && sudo rm -rf buildvolume
              mkdir $(Agent.BuildDirectory)/buildvolume
              cd $(System.DefaultWorkingDirectory)
              cp -r * $(Agent.BuildDirectory)/buildvolume
              sudo cp -r .* $(Agent.BuildDirectory)/buildvolume
              cd $(Agent.BuildDirectory)/buildvolume && pwd && ls -al
              printenv | grep -i java
            displayName: 'Preparing task'
          - task: CmdLine@2
            displayName: Build images
            inputs:
              script: |
                which docker
                docker run --rm \
                -v "/var/run/docker.sock:/var/run/docker.sock" \
                -v "/usr/bin/docker:/usr/bin/docker" \
                -v "$(Agent.BuildDirectory)/buildvolume:/buildvolume" \
                acrah01seadahss01.azurecr.io/ci-image-base/veracode-sast-upload-pdf-sca-java:latest \
                /bin/sh -c "ls -al /buildvolume && java --version \
                && cd /buildvolume \
                && mvn clean install -D spring.profiles.active=default \
                && mvn clean install -pl ahss-gateway -amd -P local-docker -D spring.profiles.active=integration \
                && mvn -pl ahss-gateway/Interface/ahss-gateway-web compile jib:dockerBuild -P local-docker \
                && mvn clean install -pl ahss-product-portal -amd -P local-docker -D spring.profiles.active=integration \
                && mvn -pl ahss-product-portal/Interface/ahss-pp-web compile jib:dockerBuild -P local-docker \
                && mvn clean install -pl ahss-uam -amd -P local-docker -D spring.profiles.active=integration \
                && mvn -pl ahss-uam/Interface/ahss-uam-web compile jib:dockerBuild -P local-docker; \
                rm -rf /buildvolume/*"
                sudo docker image ls | grep ahss
                exit 0
          - task: CmdLine@2
            displayName: 'Push images to acrah01seadahss01 ACR'
            inputs:
              script: |
                docker tag 'ahss-gateway-web:0.0.1-SNAPSHOT' '$(ACR_AHSS_LOGIN_SERVER)/$(PROJECT_NAME)/ahss-gateway-web:$(IMAGE_TAG)'
                docker tag 'ahss-pp-web:0.0.1-SNAPSHOT' '$(ACR_AHSS_LOGIN_SERVER)/$(PROJECT_NAME)/ahss-pp-web:$(IMAGE_TAG)'
                docker tag 'ahss-uam-web:0.0.1-SNAPSHOT' '$(ACR_AHSS_LOGIN_SERVER)/$(PROJECT_NAME)/ahss-uam-web:$(IMAGE_TAG)'
                docker image ls | head
                docker push '$(ACR_AHSS_LOGIN_SERVER)/$(PROJECT_NAME)/ahss-gateway-web:$(IMAGE_TAG)'
                docker push '$(ACR_AHSS_LOGIN_SERVER)/$(PROJECT_NAME)/ahss-pp-web:$(IMAGE_TAG)'
                docker push '$(ACR_AHSS_LOGIN_SERVER)/$(PROJECT_NAME)/ahss-uam-web:$(IMAGE_TAG)'

  - stage: Scan_Images
    displayName: Scan_Images
    dependsOn: [DetermineBranch, Build]
    condition: eq(variables['Build.SourceBranch'], 'refs/heads/develop')
    variables:
    - name: IMAGE_TAG
      value: $[ stageDependencies.DetermineBranch.SetDeploymentFlags.outputs['DeploymentFlags.IMAGE_TAG'] ]
    jobs:
      - template: scan-image-twistcli.yml
        parameters:
          PROJECT_NAME: 'ahss'
          VERSION: $(IMAGE_TAG)
          ACR_LOGIN_SERVER: 'acrah01seadahss01.azurecr.io'
          agentMemory: 6G
          PROJECT_TYPE: 'maven'

  - stage: Deploy
    displayName: Deploy
    dependsOn: [Build, DetermineBranch]
    variables:
    - name: IMAGE_TAG
      value: $[ stageDependencies.DetermineBranch.SetDeploymentFlags.outputs['DeploymentFlags.IMAGE_TAG'] ]
    jobs:
      # Dev Environment Deployments (Separate jobs for each service)
      - job: DeployGatewayDev
        displayName: "Deploy Gateway Service to Dev Environment"
        condition: eq(stageDependencies.DetermineBranch.SetDeploymentFlags.outputs['DeploymentFlags.DEPLOY_GATEWAY'], 'true')
        steps:
          - template: deploy-to-env.yml
            parameters:
              environmentName: 'Dev'
              namespace: 'dev'
              kubernetesServiceEndpoint: 'aks-ah01-sea-d-app01'
              conditionFlag: 'DEPLOY_DEV'
              serviceHelmPairs:
                - serviceName: 'ahss-gateway-web'
                  helmValuesFile: 'helm/ahss-gateway-web/values-dev.yaml'

      - job: DeployProductPortalDev
        displayName: "Deploy Product Portal Service to Dev Environment"
        condition: eq(stageDependencies.DetermineBranch.SetDeploymentFlags.outputs['DeploymentFlags.DEPLOY_PP'], 'true')
        steps:
          - template: deploy-to-env.yml
            parameters:
              environmentName: 'Dev'
              namespace: 'dev'
              kubernetesServiceEndpoint: 'aks-ah01-sea-d-app01'
              conditionFlag: 'DEPLOY_DEV'
              serviceHelmPairs:
                - serviceName: 'ahss-pp-web'
                  helmValuesFile: 'helm/ahss-pp-web/values-dev.yaml'

      - job: DeployUAMDev
        displayName: "Deploy UAM Service to Dev Environment"
        condition: eq(stageDependencies.DetermineBranch.SetDeploymentFlags.outputs['DeploymentFlags.DEPLOY_UAM'], 'true')
        steps:
          - template: deploy-to-env.yml
            parameters:
              environmentName: 'Dev'
              namespace: 'dev'
              kubernetesServiceEndpoint: 'aks-ah01-sea-d-app01'
              conditionFlag: 'DEPLOY_DEV'
              serviceHelmPairs:
                - serviceName: 'ahss-uam-web'
                  helmValuesFile: 'helm/ahss-uam-web/values-dev.yaml'

      # UAT Environment Deployment (Single job for all services)
      - job: DeployUAT
        displayName: "Deploy All Services to UAT Environment"
        condition: eq(stageDependencies.DetermineBranch.SetDeploymentFlags.outputs['DeploymentFlags.DEPLOY_UAT'], 'true')
        steps:
          - template: deploy-to-env.yml
            parameters:
              environmentName: 'UAT'
              namespace: 'uat'
              kubernetesServiceEndpoint: 'aks-ah01-sea-u-app01'
              conditionFlag: 'DEPLOY_UAT'
              serviceHelmPairs:
                - serviceName: 'ahss-gateway-web'
                  helmValuesFile: 'helm/ahss-gateway-web/values-uat.yaml'
                - serviceName: 'ahss-pp-web'
                  helmValuesFile: 'helm/ahss-pp-web/values-uat.yaml'
                - serviceName: 'ahss-uam-web'
                  helmValuesFile: 'helm/ahss-uam-web/values-uat.yaml'

      # SIT Environment Deployment (Single job for all services)
      - job: DeploySIT
        displayName: "Deploy All Services to SIT Environment"
        condition: eq(stageDependencies.DetermineBranch.SetDeploymentFlags.outputs['DeploymentFlags.DEPLOY_SIT'], 'true')
        steps:
          - template: deploy-to-env.yml
            parameters:
              environmentName: 'SIT'
              namespace: 'sit'
              kubernetesServiceEndpoint: 'aks-ah01-sea-d-app01'
              conditionFlag: 'DEPLOY_SIT'
              serviceHelmPairs:
                - serviceName: 'ahss-gateway-web'
                  helmValuesFile: 'helm/ahss-gateway-web/values-sit.yaml'
                - serviceName: 'ahss-pp-web'
                  helmValuesFile: 'helm/ahss-pp-web/values-sit.yaml'
                - serviceName: 'ahss-uam-web'
                  helmValuesFile: 'helm/ahss-uam-web/values-sit.yaml'

      # Production Environment Deployment (Single job for all services)
      - job: DeployProd
        displayName: "Deploy All Services to Production Environment"
        condition: eq(stageDependencies.DetermineBranch.SetDeploymentFlags.outputs['DeploymentFlags.DEPLOY_PROD'], 'true')
        steps:
          - template: deploy-to-env.yml
            parameters:
              environmentName: 'Prod'
              namespace: 'prod'
              kubernetesServiceEndpoint: 'aks-ah01-sea-p-app01'
              conditionFlag: 'DEPLOY_PROD'
              serviceHelmPairs:
                - serviceName: 'ahss-gateway-web'
                  helmValuesFile: 'helm/ahss-gateway-web/values-prod.yaml'
                - serviceName: 'ahss-pp-web'
                  helmValuesFile: 'helm/ahss-pp-web/values-prod.yaml'
                - serviceName: 'ahss-uam-web'
                  helmValuesFile: 'helm/ahss-uam-web/values-prod.yaml'



