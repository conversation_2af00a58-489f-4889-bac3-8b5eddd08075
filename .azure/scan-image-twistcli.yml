parameters:
# vvv COMMON BUILD PARAMETERS vvv #
- name: 'PROJECT_NAME'
  type: string
- name: 'PROJECT_TYPE'
  default: 'gradle'
  type: string
- name: 'VERSION'
  type: string
- name: 'ACR_LOGIN_SERVER'
  type: string
- name: 'poolName'
  default: ah01_selfhosted
  type: string
- name: 'vmImage'
  default: '$(BUILD_VM_IMAGE)'
  type: string
- name: 'agentMemory'
  displayName: 'Option to determine which agent to build on in terms of how much memory this build needs'
  default: 500M
  type: string
  values:
  - 500M
  - 1G
  - 2G
  - 6G

jobs:
- job: Scan_Image_Twistcli
  displayName: Scan_Image_Twistcli
  pool:
    name: ${{ parameters.poolName }}
    vmImage: ${{ parameters.vmImage }}
    demands:
    - docker
    - CONNECT_TO_ACR
    - MEMORY_${{ parameters.agentMemory }}
  variables:
  - group: ahss-common
#  - group: veracode_insight_studio
#  - group: twistcli_insight_studio
  steps:
  - template: login-to-acr.yml
  - task: CmdLine@2
    displayName: 'Pull baseci image'
    inputs:
      script: |
        sudo systemctl status docker
        sudo systemctl start docker
        docker pull acrah01seadahss01.azurecr.io/ci-image-base/veracode-sast-prisma:latest
#  - task: CmdLine@2
#    displayName: Twistcli scan Image
#    inputs:
#      script: |
#        if [ "${{ parameters.PROJECT_TYPE }}" = "gradle" ]
#        then
#          imageProject=$(find $(System.DefaultWorkingDirectory)/ -name build.gradle -exec grep \$\{dockerRepository\} {} \; | awk -F"/" '{OFS=FS;$1="";sub("^/","");print}' | awk -F":" '{print $1}')
#        elif [ "${{ parameters.PROJECT_TYPE }}" = "maven" ]
#        then
#          imageProject=$(find $(System.DefaultWorkingDirectory)/ -name pom.xml -exec grep \<service.name\> {} \; | grep -v unknown-service | awk -F">" '{print $2}' | awk -F"<" '{print $1}')
#        fi
#        for image in $imageProject; do
#          docker pull ${{ parameters.ACR_LOGIN_SERVER }}/${{ parameters.PROJECT_NAME }}/$image:$(VERSION) && \
#          docker run --rm \
#          -v "/var/run/docker.sock:/var/run/docker.sock" \
#          -v "$(Agent.BuildDirectory):/tmp/" \
#          -e TWISTCLI_URL_CONSOLE='$(TWISTCLI_URL_CONSOLE)' \
#          -e TWISTCLI_ACCESS_KEY_ID='$(TWISTCLI_ACCESS_KEY_ID)' \
#          -e TWISTCLI_SECRET_KEY='$(TWISTCLI_SECRET_KEY)' \
#          -e ACR_LOGIN_SERVER='${{ parameters.ACR_LOGIN_SERVER }}' \
#          -e PROJECT_NAME='${{ parameters.PROJECT_NAME }}' \
#          -e IMAGE=$image \
#          -e VERSION='$(VERSION)' \
#          acrah01seadahss01.azurecr.io/ci-image-base/veracode-sast-prisma:latest \
#          /bin/sh -c 'cd /security_tool && \
#          ./twistcli images scan --address $(TWISTCLI_URL_CONSOLE) --user $(TWISTCLI_ACCESS_KEY_ID) --password $(TWISTCLI_SECRET_KEY) \
#          --details --ci $ACR_LOGIN_SERVER/$PROJECT_NAME/$IMAGE:$(VERSION) 2>&1 | tee -a /tmp/raw_image_scan_output.txt \
#          && cat /tmp/raw_image_scan_output.txt | grep -v "Link to the results" | grep -v "===" > /tmp/image_scan_output.txt \
#          && chmod a+rwx /tmp/*'
#        done
#        exit 0

  - task: CmdLine@2
    displayName: Twistcli scan Image
    inputs:
      script: |
        # Define a static list of images to pull and scan
        imageList=(
          "ahss-gateway-web"
          "ahss-pp-web"
          "ahss-uam-web"
        )
        
        # Loop through each image in the list
        for image in "${imageList[@]}"; do
          echo "Pulling image: ${{ parameters.ACR_LOGIN_SERVER }}/${{ parameters.PROJECT_NAME }}/$image:${{ parameters.VERSION }}"
          docker pull ${{ parameters.ACR_LOGIN_SERVER }}/${{ parameters.PROJECT_NAME }}/$image:${{ parameters.VERSION }} && \
          docker run --rm \
          -v "/var/run/docker.sock:/var/run/docker.sock" \
          -v "$(Agent.BuildDirectory):/tmp/" \
          -e TWISTCLI_URL_CONSOLE='$(TWISTCLI_URL_CONSOLE)' \
          -e TWISTCLI_ACCESS_KEY_ID='$(TWISTCLI_ACCESS_KEY_ID)' \
          -e TWISTCLI_SECRET_KEY='$(TWISTCLI_SECRET_KEY)' \
          -e ACR_LOGIN_SERVER='${{ parameters.ACR_LOGIN_SERVER }}' \
          -e PROJECT_NAME='${{ parameters.PROJECT_NAME }}' \
          -e IMAGE=$image \
          -e VERSION='${{ parameters.VERSION }}' \
          acrah01seadahss01.azurecr.io/ci-image-base/veracode-sast-prisma:latest \
          /bin/sh -c 'cd /security_tool && \
          ./twistcli images scan --address $(TWISTCLI_URL_CONSOLE) --user $(TWISTCLI_ACCESS_KEY_ID) --password $(TWISTCLI_SECRET_KEY) \
          --details --ci $ACR_LOGIN_SERVER/$PROJECT_NAME/$IMAGE:$VERSION 2>&1 | tee -a /tmp/raw_image_scan_output.txt \
          && cat /tmp/raw_image_scan_output.txt | grep -v "Link to the results" | grep -v "===" > /tmp/image_scan_output.txt \
          && chmod a+rwx /tmp/*'
        done
        exit 0
  - task: CmdLine@2
    displayName: Sumary result to pulish artifact
    inputs:
      script: |
        cd $(Agent.BuildDirectory)
        sudo rm -rf Result; sudo mkdir Result && sudo mv image*.json image*.txt Result
        ls -al Result
  - task: PublishBuildArtifacts@1
    displayName: Pulish image scan result to artifact
    inputs:
      PathtoPublish: $(Agent.BuildDirectory)/Result
      ArtifactName: 'Security_Scan_Result'
      publishLocation: 'Container'
  - task: CmdLine@2
    displayName: Clean result
    inputs:
      script: |
        cd $(Agent.BuildDirectory)
        sudo rm -rf Result raw_image_scan_output.txt
