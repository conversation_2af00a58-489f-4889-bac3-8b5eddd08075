---
{{- include "bjw-s.common.loader.init" . }}

{{- define "app-template.hardcodedValues" -}}
# Set the nameOverride based on the release name if no override has been set
{{ if not .Values.global.nameOverride }}
global:
  nameOverride: "{{ .Release.Name }}"
{{ end }}
{{- end -}}
{{- $_ := mergeOverwrite .Values (include "app-template.hardcodedValues" . | fromYaml) -}}

{{/* Render the templates */}}
{{ include "bjw-s.common.loader.generate" . }}
