annotations:
  artifacthub.io/changes: |-
    - kind: changed
      description: |
        Updated library version to 3.2.1.
      links:
        - name: Upgrade instructions from v2.x
          url: https://bjw-s.github.io/helm-charts/docs/app-template/#from-2xx-to-30x
        - name: Common library chart definition
          url: https://github.com/bjw-s/helm-charts/blob/main/charts/library/common/Chart.yaml
apiVersion: v2
dependencies:
- name: common
  repository: https://bjw-s.github.io/helm-charts
  version: 3.2.1
description: A common powered chart template. This can be useful for small projects
  that don't have their own chart.
kubeVersion: '>=1.22.0-0'
maintainers:
- email: <EMAIL>
  name: bjw-s
name: app-template
version: 3.2.1
