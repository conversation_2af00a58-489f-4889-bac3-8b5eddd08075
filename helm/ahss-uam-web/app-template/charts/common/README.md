# common

![Version: 3.2.1](https://img.shields.io/badge/Version-3.2.1-informational?style=flat-square) ![Type: library](https://img.shields.io/badge/Type-library-informational?style=flat-square)

Function library for Helm charts

## Requirements

Kubernetes: `>=1.22.0-0`

## Dependencies

| Repository | Name | Version |
|------------|------|---------|

## Installing the Chart

This is a [Helm Library Chart](https://helm.sh/docs/topics/library_charts/#helm).

**🚨 WARNING: THIS CHART IS NOT MEANT TO BE INSTALLED DIRECTLY**

## Using this library

Include this chart as a dependency in your `Chart.yaml` e.g.

```yaml
# Chart.yaml
dependencies:
  - name: common
    version: 3.2.1
    repository: https://bjw-s.github.io/helm-charts/
```

For more information, take a look at the [Docs](http://bjw-s.github.io/helm-charts/docs/common-library/introduction/).

## Configuration

Read through the [values.yaml](./values.yaml) file. It has several commented out suggested values.

## Values

**Important**: When deploying an application Helm chart you can add more values from the common library chart [here](https://github.com/bjw-s/helm-charts/tree/main/charts/library/common/values.yaml)

These values will be validated by a JSON schema which can be found [here](https://github.com/bjw-s/helm-charts/tree/main/charts/library/common/values.schema.json)

## Support

- See the [Docs](http://bjw-s.github.io/helm-charts/docs/)
- Open an [issue](https://github.com/bjw-s/helm-charts/issues/new/choose)
- Join the home-operations [Discord](https://discord.gg/home-operations) community

----------------------------------------------
Autogenerated from chart metadata using [helm-docs v1.13.1](https://github.com/norwoodj/helm-docs/releases/v1.13.1)
