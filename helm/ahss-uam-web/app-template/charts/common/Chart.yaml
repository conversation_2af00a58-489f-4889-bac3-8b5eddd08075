annotations:
  artifacthub.io/changes: |-
    - kind: added
      description: |-
        Support templating in annotation and label values for all objects (fixed in v3.2.1)
    - kind: added
      description: |-
        Support activeDeadlineSeconds field on CronJobs and Jobs
    - kind: added
      description: |-
        Support excluding secrets and configMaps from pod checksum annotation
    - kind: fixed
      description: |-
        Fixed failing ServiceMonitor test
    - kind: fixed
      description: |-
        Fixed networkPolicies not rendering correctly
apiVersion: v2
description: Function library for Helm charts
home: https://github.com/bjw-s/helm-charts/tree/main/charts/library/common
keywords:
- common
- library
kubeVersion: '>=1.22.0-0'
maintainers:
- email: <EMAIL>
  name: bjw-s
name: common
type: library
version: 3.2.1
