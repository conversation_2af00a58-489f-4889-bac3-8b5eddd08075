{"core.v1.HostAlias": {"type": "object", "description": "HostAlias holds the mapping between IP and hostnames that will be injected as an entry in the pod's hosts file.", "additionalProperties": false, "properties": {"hostnames": {"description": "Hostnames for the above IP address.", "items": {"type": "string"}, "type": "array"}, "ip": {"description": "IP address of the host file entry.", "type": "string"}}}, "core.v1.LocalObjectReference": {"type": "object", "description": "LocalObjectReference contains enough information to let you locate the referenced object inside the same namespace.", "properties": {"name": {"description": "Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names", "type": "string"}}}, "core.v1.NodeSelectorRequirement": {"type": "object", "description": "A node selector requirement is a selector that contains values, a key, and an operator that relates the key and values.", "additionalProperties": false, "properties": {"key": {"description": "The label key that the selector applies to.", "type": "string"}, "operator": {"description": "Represents a key's relationship to a set of values. Valid operators are <PERSON>, Not<PERSON>n, Exists, DoesNotExist. Gt, and Lt.", "type": "string"}, "values": {"description": "An array of string values. If the operator is In or NotIn, the values array must be non-empty. If the operator is Exists or DoesNotExist, the values array must be empty. If the operator is Gt or Lt, the values array must have a single element, which will be interpreted as an integer. This array is replaced during a strategic merge patch.", "items": {"type": "string"}, "type": "array"}}, "required": ["key", "operator"]}, "core.v1.NodeSelectorTerm": {"type": "object", "description": "A null or empty node selector term matches no objects. The requirements of them are ANDed. The TopologySelectorTerm type implements a subset of the NodeSelectorTerm.", "additionalProperties": false, "properties": {"matchExpressions": {"description": "A list of node selector requirements by node's labels.", "items": {"$ref": "#/core.v1.NodeSelectorRequirement"}, "type": "array"}, "matchFields": {"description": "A list of node selector requirements by node's fields.", "items": {"$ref": "#/core.v1.NodeSelectorRequirement"}, "type": "array"}}}, "core.v1.NodeSelector": {"type": "object", "description": "A node selector represents the union of the results of one or more label queries over a set of nodes; that is, it represents the OR of the selectors represented by the node selector terms.", "additionalProperties": false, "properties": {"nodeSelectorTerms": {"description": "Required. A list of node selector terms. The terms are ORed.", "items": {"$ref": "#/core.v1.NodeSelectorTerm"}, "type": "array"}}, "required": ["nodeSelectorTerms"]}, "core.v1.Affinity": {"type": "object", "description": "Affinity is a group of affinity scheduling rules.", "additionalProperties": false, "properties": {"nodeAffinity": {"$ref": "#/core.v1.NodeAffinity", "description": "Describes node affinity scheduling rules for the pod."}, "podAffinity": {"$ref": "#/core.v1.PodAffinity", "description": "Describes pod affinity scheduling rules (e.g. co-locate this pod in the same node, zone, etc. as some other pod(s))."}, "podAntiAffinity": {"$ref": "#/core.v1.PodAntiAffinity", "description": "Describes pod anti-affinity scheduling rules (e.g. avoid putting this pod in the same node, zone, etc. as some other pod(s))."}}}, "core.v1.NodeAffinity": {"type": "object", "description": "Node affinity is a group of node affinity scheduling rules.", "additionalProperties": false, "properties": {"preferredDuringSchedulingIgnoredDuringExecution": {"description": "The scheduler will prefer to schedule pods to nodes that satisfy the affinity expressions specified by this field, but it may choose a node that violates one or more of the expressions. The node that is most preferred is the one with the greatest sum of weights, i.e. for each node that meets all of the scheduling requirements (resource request, requiredDuringScheduling affinity expressions, etc.), compute a sum by iterating through the elements of this field and adding \"weight\" to the sum if the node matches the corresponding matchExpressions; the node(s) with the highest sum are the most preferred.", "items": {"$ref": "#/core.v1.PreferredSchedulingTerm"}, "type": "array"}, "requiredDuringSchedulingIgnoredDuringExecution": {"$ref": "#/core.v1.NodeSelector", "description": "If the affinity requirements specified by this field are not met at scheduling time, the pod will not be scheduled onto the node. If the affinity requirements specified by this field cease to be met at some point during pod execution (e.g. due to an update), the system may or may not try to eventually evict the pod from its node."}}}, "core.v1.PodAffinity": {"type": "object", "description": "Pod affinity is a group of inter pod affinity scheduling rules.", "additionalProperties": false, "properties": {"preferredDuringSchedulingIgnoredDuringExecution": {"description": "The scheduler will prefer to schedule pods to nodes that satisfy the affinity expressions specified by this field, but it may choose a node that violates one or more of the expressions. The node that is most preferred is the one with the greatest sum of weights, i.e. for each node that meets all of the scheduling requirements (resource request, requiredDuringScheduling affinity expressions, etc.), compute a sum by iterating through the elements of this field and adding \"weight\" to the sum if the node has pods which matches the corresponding podAffinityTerm; the node(s) with the highest sum are the most preferred.", "items": {"$ref": "#/core.v1.WeightedPodAffinityTerm"}, "type": "array"}, "requiredDuringSchedulingIgnoredDuringExecution": {"description": "If the affinity requirements specified by this field are not met at scheduling time, the pod will not be scheduled onto the node. If the affinity requirements specified by this field cease to be met at some point during pod execution (e.g. due to a pod label update), the system may or may not try to eventually evict the pod from its node. When there are multiple elements, the lists of nodes corresponding to each podAffinityTerm are intersected, i.e. all terms must be satisfied.", "items": {"$ref": "#/core.v1.PodAffinityTerm"}, "type": "array"}}}, "core.v1.WeightedPodAffinityTerm": {"type": "object", "description": "The weights of all of the matched WeightedPodAffinityTerm fields are added per-node to find the most preferred node(s)", "additionalProperties": false, "properties": {"podAffinityTerm": {"$ref": "#/core.v1.PodAffinityTerm", "description": "Required. A pod affinity term, associated with the corresponding weight."}, "weight": {"description": "weight associated with matching the corresponding podAffinityTerm, in the range 1-100.", "format": "int32", "type": "integer"}}, "required": ["weight", "podAffinityTerm"]}, "core.v1.PodAffinityTerm": {"type": "object", "description": "Defines a set of pods (namely those matching the labelSelector relative to the given namespace(s)) that this pod should be co-located (affinity) or not co-located (anti-affinity) with, where co-located is defined as running on a node whose value of the label with key <topologyKey> matches that of any node on which a pod of the set of pods is running", "additionalProperties": false, "properties": {"labelSelector": {"$ref": "#/meta.v1.LabelSelector", "description": "A label query over a set of resources, in this case pods. If it's null, this PodAffinityTerm matches with no Pods."}, "matchLabelKeys": {"description": "MatchLabelKeys is a set of pod label keys to select which pods will be taken into consideration. The keys are used to lookup values from the incoming pod labels, those key-value labels are merged with `LabelSelector` as `key in (value)` to select the group of existing pods which pods will be taken into consideration for the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming pod labels will be ignored. The default value is empty. The same key is forbidden to exist in both MatchLabelKeys and LabelSelector. Also, MatchLabelKeys cannot be set when LabelSelector isn't set. This is an alpha field and requires enabling MatchLabelKeysInPodAffinity feature gate.", "items": {"type": "string"}, "type": "array", "x-kubernetes-list-type": "atomic"}, "mismatchLabelKeys": {"description": "MismatchLabelKeys is a set of pod label keys to select which pods will be taken into consideration. The keys are used to lookup values from the incoming pod labels, those key-value labels are merged with `LabelSelector` as `key notin (value)` to select the group of existing pods which pods will be taken into consideration for the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming pod labels will be ignored. The default value is empty. The same key is forbidden to exist in both MismatchLabelKeys and LabelSelector. Also, MismatchLabelKeys cannot be set when LabelSelector isn't set. This is an alpha field and requires enabling MatchLabelKeysInPodAffinity feature gate.", "items": {"type": "string"}, "type": "array", "x-kubernetes-list-type": "atomic"}, "namespaceSelector": {"$ref": "#/meta.v1.LabelSelector", "description": "A label query over the set of namespaces that the term applies to. The term is applied to the union of the namespaces selected by this field and the ones listed in the namespaces field. null selector and null or empty namespaces list means \"this pod's namespace\". An empty selector ({}) matches all namespaces."}, "namespaces": {"description": "namespaces specifies a static list of namespace names that the term applies to. The term is applied to the union of the namespaces listed in this field and the ones selected by namespaceSelector. null or empty namespaces list and null namespaceSelector means \"this pod's namespace\".", "items": {"type": "string"}, "type": "array"}, "topologyKey": {"description": "This pod should be co-located (affinity) or not co-located (anti-affinity) with the pods matching the labelSelector in the specified namespaces, where co-located is defined as running on a node whose value of the label with key topologyKey matches that of any node on which any of the selected pods is running. Empty topologyKey is not allowed.", "type": "string"}}, "required": ["<PERSON><PERSON><PERSON>"]}, "core.v1.PodAntiAffinity": {"type": "object", "description": "Pod anti affinity is a group of inter pod anti affinity scheduling rules.", "additionalProperties": false, "properties": {"preferredDuringSchedulingIgnoredDuringExecution": {"description": "The scheduler will prefer to schedule pods to nodes that satisfy the anti-affinity expressions specified by this field, but it may choose a node that violates one or more of the expressions. The node that is most preferred is the one with the greatest sum of weights, i.e. for each node that meets all of the scheduling requirements (resource request, requiredDuringScheduling anti-affinity expressions, etc.), compute a sum by iterating through the elements of this field and adding \"weight\" to the sum if the node has pods which matches the corresponding podAffinityTerm; the node(s) with the highest sum are the most preferred.", "items": {"$ref": "#/core.v1.WeightedPodAffinityTerm"}, "type": "array"}, "requiredDuringSchedulingIgnoredDuringExecution": {"description": "If the anti-affinity requirements specified by this field are not met at scheduling time, the pod will not be scheduled onto the node. If the anti-affinity requirements specified by this field cease to be met at some point during pod execution (e.g. due to a pod label update), the system may or may not try to eventually evict the pod from its node. When there are multiple elements, the lists of nodes corresponding to each podAffinityTerm are intersected, i.e. all terms must be satisfied.", "items": {"$ref": "#/core.v1.PodAffinityTerm"}, "type": "array"}}}, "core.v1.PreferredSchedulingTerm": {"type": "object", "description": "An empty preferred scheduling term matches all objects with implicit weight 0 (i.e. it's a no-op). A null preferred scheduling term matches no objects (i.e. is also a no-op).", "additionalProperties": false, "properties": {"preference": {"$ref": "#/core.v1.NodeSelectorTerm", "description": "A node selector term, associated with the corresponding weight."}, "weight": {"description": "Weight associated with matching the corresponding nodeSelectorTerm, in the range 1-100.", "format": "int32", "type": "integer"}}, "required": ["weight", "preference"]}, "core.v1.PodDNSConfig": {"type": "object", "description": "PodDNSConfig defines the DNS parameters of a pod in addition to those generated from DNSPolicy.", "additionalProperties": false, "properties": {"nameservers": {"description": "A list of DNS name server IP addresses. This will be appended to the base nameservers generated from DNSPolicy. Duplicated nameservers will be removed.", "items": {"type": "string"}, "type": "array"}, "options": {"description": "A list of DNS resolver options. This will be merged with the base options generated from DNSPolicy. Duplicated entries will be removed. Resolution options given in Options will override those that appear in the base DNSPolicy.", "items": {"$ref": "#/core.v1.PodDNSConfigOption"}, "type": "array"}, "searches": {"description": "A list of DNS search domains for host-name lookup. This will be appended to the base search paths generated from DNSPolicy. Duplicated search paths will be removed.", "items": {"type": "string"}, "type": "array"}}}, "core.v1.PodDNSConfigOption": {"type": "object", "description": "PodDNSConfigOption defines DNS resolver options of a pod.", "additionalProperties": false, "properties": {"name": {"description": "Required.", "type": "string"}, "value": {"type": "string"}}}, "core.v1.SeccompProfile": {"type": "object", "description": "SeccompProfile defines a pod/container's seccomp profile settings. Only one profile source may be set.", "additionalProperties": false, "properties": {"localhostProfile": {"description": "localhostProfile indicates a profile defined in a file on the node should be used. The profile must be preconfigured on the node to work. Must be a descending path, relative to the kubelet's configured seccomp profile location. Must be set if type is \"Localhost\". Must NOT be set for any other type.", "type": "string"}, "type": {"description": "type indicates which kind of seccomp profile will be applied. Valid options are:\n\nLocalhost - a profile defined in a file on the node should be used. RuntimeDefault - the container runtime default profile should be used. Unconfined - no profile should be applied.", "type": "string"}}, "required": ["type"]}, "core.v1.SELinuxOptions": {"type": "object", "description": "SELinuxOptions are the labels to be applied to the container", "additionalProperties": false, "properties": {"level": {"description": "Level is SELinux level label that applies to the container.", "type": "string"}, "role": {"description": "Role is a SELinux role label that applies to the container.", "type": "string"}, "type": {"description": "Type is a SELinux type label that applies to the container.", "type": "string"}, "user": {"description": "User is a SELinux user label that applies to the container.", "type": "string"}}}, "core.v1.Sysctl": {"type": "object", "description": "Sysctl defines a kernel parameter to be set", "additionalProperties": false, "properties": {"name": {"description": "Name of a property to set", "type": "string"}, "value": {"description": "Value of a property to set", "type": "string"}}, "required": ["name", "value"]}, "core.v1.PodSecurityContext": {"type": "object", "description": "PodSecurityContext holds pod-level security attributes and common container settings. Some fields are also present in container.securityContext.  Field values of container.securityContext take precedence over field values of PodSecurityContext.", "additionalProperties": false, "properties": {"fsGroup": {"description": "A special supplemental group that applies to all containers in a pod. Some volume types allow the Kubelet to change the ownership of that volume to be owned by the pod:\n\n1. The owning GID will be the FSGroup 2. The setgid bit is set (new files created in the volume will be owned by FSGroup) 3. The permission bits are OR'd with rw-rw----\n\nIf unset, the Kubelet will not modify the ownership and permissions of any volume. Note that this field cannot be set when spec.os.name is windows.", "format": "int64", "type": "integer"}, "fsGroupChangePolicy": {"description": "fsGroupChangePolicy defines behavior of changing ownership and permission of the volume before being exposed inside Pod. This field will only apply to volume types which support fsGroup based ownership(and permissions). It will have no effect on ephemeral volume types such as: secret, configmaps and emptydir. Valid values are \"OnRootMismatch\" and \"Always\". If not specified, \"Always\" is used. Note that this field cannot be set when spec.os.name is windows.", "type": "string"}, "runAsGroup": {"description": "The GID to run the entrypoint of the container process. Uses runtime default if unset. May also be set in SecurityContext.  If set in both SecurityContext and PodSecurityContext, the value specified in SecurityContext takes precedence for that container. Note that this field cannot be set when spec.os.name is windows.", "format": "int64", "type": "integer"}, "runAsNonRoot": {"description": "Indicates that the container must run as a non-root user. If true, the Kubelet will validate the image at runtime to ensure that it does not run as UID 0 (root) and fail to start the container if it does. If unset or false, no such validation will be performed. May also be set in SecurityContext.  If set in both SecurityContext and PodSecurityContext, the value specified in SecurityContext takes precedence.", "type": "boolean"}, "runAsUser": {"description": "The UID to run the entrypoint of the container process. Defaults to user specified in image metadata if unspecified. May also be set in SecurityContext.  If set in both SecurityContext and PodSecurityContext, the value specified in SecurityContext takes precedence for that container. Note that this field cannot be set when spec.os.name is windows.", "format": "int64", "type": "integer"}, "seLinuxOptions": {"$ref": "#/core.v1.SELinuxOptions", "description": "The SELinux context to be applied to all containers. If unspecified, the container runtime will allocate a random SELinux context for each container.  May also be set in SecurityContext.  If set in both SecurityContext and PodSecurityContext, the value specified in SecurityContext takes precedence for that container. Note that this field cannot be set when spec.os.name is windows."}, "seccompProfile": {"$ref": "#/core.v1.SeccompProfile", "description": "The seccomp options to use by the containers in this pod. Note that this field cannot be set when spec.os.name is windows."}, "supplementalGroups": {"description": "A list of groups applied to the first process run in each container, in addition to the container's primary GID, the fsGroup (if specified), and group memberships defined in the container image for the uid of the container process. If unspecified, no additional groups are added to any container. Note that group memberships defined in the container image for the uid of the container process are still effective, even if they are not included in this list. Note that this field cannot be set when spec.os.name is windows.", "items": {"format": "int64", "type": "integer"}, "type": "array"}, "sysctls": {"description": "Sysctls hold a list of namespaced sysctls used for the pod. Pods with unsupported sysctls (by the container runtime) might fail to launch. Note that this field cannot be set when spec.os.name is windows.", "items": {"$ref": "#/core.v1.Sysctl"}, "type": "array"}}}, "core.v1.Toleration": {"type": "object", "description": "The pod this Toleration is attached to tolerates any taint that matches the triple <key,value,effect> using the matching operator <operator>.", "additionalProperties": false, "properties": {"effect": {"description": "Effect indicates the taint effect to match. Empty means match all taint effects. When specified, allowed values are NoSchedule, PreferNoSchedule and NoExecute.", "type": "string"}, "key": {"description": "Key is the taint key that the toleration applies to. Empty means match all taint keys. If the key is empty, operator must be Exists; this combination means to match all values and all keys.", "type": "string"}, "operator": {"description": "Operator represents a key's relationship to the value. Valid operators are Exists and Equal. Defaults to Equal. Exists is equivalent to wildcard for value, so that a pod can tolerate all taints of a particular category.", "type": "string"}, "tolerationSeconds": {"description": "TolerationSeconds represents the period of time the toleration (which must be of effect NoExecute, otherwise this field is ignored) tolerates the taint. By default, it is not set, which means tolerate the taint forever (do not evict). Zero and negative values will be treated as 0 (evict immediately) by the system.", "format": "int64", "type": "integer"}, "value": {"description": "Value is the taint value the toleration matches to. If the operator is Exists, the value should be empty, otherwise just a regular string.", "type": "string"}}}, "core.v1.TopologySpreadConstraint": {"type": "object", "description": "TopologySpreadConstraint specifies how to spread matching pods among the given topology.", "additionalProperties": false, "properties": {"labelSelector": {"$ref": "#/meta.v1.LabelSelector", "description": "LabelSelector is used to find matching pods. Pods that match this label selector are counted to determine the number of pods in their corresponding topology domain."}, "matchLabelKeys": {"description": "MatchLabelKeys is a set of pod label keys to select the pods over which spreading will be calculated. The keys are used to lookup values from the incoming pod labels, those key-value labels are ANDed with labelSelector to select the group of existing pods over which spreading will be calculated for the incoming pod. The same key is forbidden to exist in both MatchLabelKeys and LabelSelector. MatchLabelKeys cannot be set when LabelSelector isn't set. Keys that don't exist in the incoming pod labels will be ignored. A null or empty list means only match against labelSelector.\n\nThis is a beta field and requires the MatchLabelKeysInPodTopologySpread feature gate to be enabled (enabled by default).", "items": {"type": "string"}, "type": "array", "x-kubernetes-list-type": "atomic"}, "maxSkew": {"description": "MaxSkew describes the degree to which pods may be unevenly distributed. When `whenUnsatisfiable=DoNotSchedule`, it is the maximum permitted difference between the number of matching pods in the target topology and the global minimum. The global minimum is the minimum number of matching pods in an eligible domain or zero if the number of eligible domains is less than MinDomains. For example, in a 3-zone cluster, MaxSkew is set to 1, and pods with the same labelSelector spread as 2/2/1: In this case, the global minimum is 1. | zone1 | zone2 | zone3 | |  P P  |  P P  |   P   | - if MaxSkew is 1, incoming pod can only be scheduled to zone3 to become 2/2/2; scheduling it onto zone1(zone2) would make the ActualSkew(3-1) on zone1(zone2) violate MaxSkew(1). - if MaxSkew is 2, incoming pod can be scheduled onto any zone. When `whenUnsatisfiable=ScheduleAnyway`, it is used to give higher precedence to topologies that satisfy it. It's a required field. Default value is 1 and 0 is not allowed.", "format": "int32", "type": "integer"}, "minDomains": {"description": "MinDomains indicates a minimum number of eligible domains. When the number of eligible domains with matching topology keys is less than minDomains, Pod Topology Spread treats \"global minimum\" as 0, and then the calculation of Skew is performed. And when the number of eligible domains with matching topology keys equals or greater than minDomains, this value has no effect on scheduling. As a result, when the number of eligible domains is less than minDomains, scheduler won't schedule more than maxSkew Pods to those domains. If value is nil, the constraint behaves as if MinDomains is equal to 1. Valid values are integers greater than 0. When value is not nil, WhenUnsatisfiable must be DoNotSchedule.\n\nFor example, in a 3-zone cluster, MaxSkew is set to 2, MinDomains is set to 5 and pods with the same labelSelector spread as 2/2/2: | zone1 | zone2 | zone3 | |  P P  |  P P  |  P P  | The number of domains is less than 5(MinDomains), so \"global minimum\" is treated as 0. In this situation, new pod with the same labelSelector cannot be scheduled, because computed skew will be 3(3 - 0) if new Pod is scheduled to any of the three zones, it will violate MaxSkew.\n\nThis is a beta field and requires the MinDomainsInPodTopologySpread feature gate to be enabled (enabled by default).", "format": "int32", "type": "integer"}, "nodeAffinityPolicy": {"description": "NodeAffinityPolicy indicates how we will treat Pod's nodeAffinity/nodeSelector when calculating pod topology spread skew. Options are: - Honor: only nodes matching nodeAffinity/nodeSelector are included in the calculations. - Ignore: nodeAffinity/nodeSelector are ignored. All nodes are included in the calculations.\n\nIf this value is nil, the behavior is equivalent to the Honor policy. This is a beta-level feature default enabled by the NodeInclusionPolicyInPodTopologySpread feature flag.", "type": "string"}, "nodeTaintsPolicy": {"description": "NodeTaintsPolicy indicates how we will treat node taints when calculating pod topology spread skew. Options are: - Honor: nodes without taints, along with tainted nodes for which the incoming pod has a toleration, are included. - Ignore: node taints are ignored. All nodes are included.\n\nIf this value is nil, the behavior is equivalent to the Ignore policy. This is a beta-level feature default enabled by the NodeInclusionPolicyInPodTopologySpread feature flag.", "type": "string"}, "topologyKey": {"description": "TopologyKey is the key of node labels. Nodes that have a label with this key and identical values are considered to be in the same topology. We consider each <key, value> as a \"bucket\", and try to put balanced number of pods into each bucket. We define a domain as a particular instance of a topology. Also, we define an eligible domain as a domain whose nodes meet the requirements of nodeAffinityPolicy and nodeTaintsPolicy. e.g. If TopologyKey is \"kubernetes.io/hostname\", each Node is a domain of that topology. And, if TopologyKey is \"topology.kubernetes.io/zone\", each zone is a domain of that topology. It's a required field.", "type": "string"}, "whenUnsatisfiable": {"description": "WhenUnsatisfiable indicates how to deal with a pod if it doesn't satisfy the spread constraint. - DoNotSchedule (default) tells the scheduler not to schedule it. - ScheduleAnyway tells the scheduler to schedule the pod in any location,\n  but giving higher precedence to topologies that would help reduce the\n  skew.\nA constraint is considered \"Unsatisfiable\" for an incoming pod if and only if every possible node assignment for that pod would violate \"MaxSkew\" on some topology. For example, in a 3-zone cluster, MaxSkew is set to 1, and pods with the same labelSelector spread as 3/1/1: | zone1 | zone2 | zone3 | | P P P |   P   |   P   | If WhenUnsatisfiable is set to DoNotSchedule, incoming pod can only be scheduled to zone2(zone3) to become 3/2/1(3/1/2) as ActualSkew(2-1) on zone2(zone3) satisfies MaxSkew(1). In other words, the cluster can still be imbalanced, but scheduler won't make it *more* imbalanced. It's a required field.", "type": "string"}}, "required": ["maxSkew", "<PERSON><PERSON><PERSON>", "whenUnsatisfiable"]}, "meta.v1.LabelSelector": {"type": "object", "description": "A label selector is a label query over a set of resources. The result of matchLabels and matchExpressions are ANDed. An empty label selector matches all objects. A null label selector matches no objects.", "additionalProperties": false, "properties": {"matchExpressions": {"description": "matchExpressions is a list of label selector requirements. The requirements are ANDed.", "items": {"$ref": "#/meta.v1.LabelSelectorRequirement"}, "type": "array"}, "matchLabels": {"additionalProperties": {"type": "string"}, "description": "matchLabels is a map of {key,value} pairs. A single {key,value} in the match<PERSON>abe<PERSON> map is equivalent to an element of matchExpressions, whose key field is \"key\", the operator is \"In\", and the values array contains only \"value\". The requirements are ANDed.", "type": "object"}}}, "meta.v1.LabelSelectorRequirement": {"type": "object", "description": "A label selector requirement is a selector that contains values, a key, and an operator that relates the key and values.", "additionalProperties": false, "properties": {"key": {"description": "key is the label key that the selector applies to.", "type": "string"}, "operator": {"description": "operator represents a key's relationship to a set of values. Valid operators are In, NotIn, Exists and DoesNotExist.", "type": "string"}, "values": {"description": "values is an array of string values. If the operator is In or NotIn, the values array must be non-empty. If the operator is Exists or DoesNotExist, the values array must be empty. This array is replaced during a strategic merge patch.", "items": {"type": "string"}, "type": "array"}}, "required": ["key", "operator"]}, "core.v1.ContainerPort": {"type": "object", "additionalProperties": false, "properties": {"containerPort": {"description": "Number of port to expose on the pod's IP address. This must be a valid port number, 0 < x < 65536.", "format": "int32", "type": ["integer", "null"]}, "hostIP": {"description": "What host IP to bind the external port to.", "type": ["string", "null"]}, "hostPort": {"description": "Number of port to expose on the host. If specified, this must be a valid port number, 0 < x < 65536. If HostNetwork is specified, this must match ContainerPort. Most containers do not need this.", "format": "int32", "type": ["integer", "null"]}, "name": {"description": "If specified, this must be an IANA_SVC_NAME and unique within the pod. Each named port in a pod must have a unique name. Name for the port that can be referred to by services.", "type": ["string", "null"]}, "protocol": {"description": "Protocol for port. Must be UDP, TCP, or SCTP. Defaults to \"TCP\".", "type": ["string", "null"]}}, "required": ["containerPort"]}, "core.v1.ExecAction": {"description": "ExecAction describes a \"run in container\" action.", "properties": {"command": {"description": "Command is the command line to execute inside the container, the working directory for the command  is root ('/') in the container's filesystem. The command is simply exec'd, it is not run inside a shell, so traditional shell instructions ('|', etc) won't work. To use a shell, you need to explicitly call out to that shell. Exit status of 0 is treated as live/healthy and non-zero is unhealthy.", "items": {"type": ["string", "null"]}, "type": ["array", "null"]}}, "type": "object"}, "core.v1.GRPCAction": {"properties": {"port": {"description": "Port number of the gRPC service. Number must be in the range 1 to 65535.", "format": "int32", "type": ["integer", "null"]}, "service": {"description": "Service is the name of the service to place in the gRPC HealthCheckRequest (see https://github.com/grpc/grpc/blob/master/doc/health-checking.md).\n\nIf this is not specified, the default behavior is defined by gRPC.", "type": ["string", "null"]}}, "required": ["port"], "type": "object"}, "core.v1.HTTPHeader": {"description": "HTTPHeader describes a custom header to be used in HTTP probes", "properties": {"name": {"description": "The header field name. This will be canonicalized upon output, so case-variant names will be understood as the same header.", "type": ["string", "null"]}, "value": {"description": "The header field value", "type": ["string", "null"]}}, "required": ["name", "value"], "type": "object"}, "core.v1.HTTPGetAction": {"description": "HTTPGetAction describes an action based on HTTP Get requests.", "properties": {"host": {"description": "Host name to connect to, defaults to the pod IP. You probably want to set \"Host\" in httpHeaders instead.", "type": ["string", "null"]}, "httpHeaders": {"description": "Custom headers to set in the request. HTTP allows repeated headers.", "items": {"$ref": "#/core.v1.HTTPHeader"}, "type": ["array", "null"]}, "path": {"description": "Path to access on the HTTP server.", "type": ["string", "null"]}, "port": {"type": ["string", "integer"], "description": "Name or number of the port to access on the container. Number must be in the range 1 to 65535. Name must be an IANA_SVC_NAME."}, "scheme": {"description": "Scheme to use for connecting to the host. Defaults to HTTP.", "type": ["string", "null"]}}, "required": ["port"], "type": "object"}, "core.v1.SleepAction": {"description": "SleepAction describes a \"sleep\" action.", "properties": {"seconds": {"description": "Seconds is the number of seconds to sleep.", "format": "int64", "type": "integer"}}, "required": ["seconds"], "type": "object"}, "core.v1.TCPSocketAction": {"description": "TCPSocketAction describes an action based on opening a socket", "properties": {"host": {"description": "Optional: Host name to connect to, defaults to the pod IP.", "type": ["string", "null"]}, "port": {"type": ["string", "integer"], "description": "Number or name of the port to access on the container. Number must be in the range 1 to 65535. Name must be an IANA_SVC_NAME."}}, "required": ["port"], "type": "object"}, "core.v1.Probe": {"description": "Probe describes a health check to be performed against a container to determine whether it is alive or ready to receive traffic.", "properties": {"exec": {"$ref": "#/core.v1.ExecAction", "description": "Exec specifies the action to take."}, "failureThreshold": {"description": "Minimum consecutive failures for the probe to be considered failed after having succeeded. Defaults to 3. Minimum value is 1.", "format": "int32", "type": ["integer", "null"]}, "grpc": {"$ref": "#/core.v1.GRPCAction", "description": "GRPC specifies an action involving a GRPC port."}, "httpGet": {"$ref": "#/core.v1.HTTPGetAction", "description": "HTTPGet specifies the http request to perform."}, "initialDelaySeconds": {"description": "Number of seconds after the container has started before liveness probes are initiated. More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes", "format": "int32", "type": ["integer", "null"]}, "periodSeconds": {"description": "How often (in seconds) to perform the probe. Default to 10 seconds. Minimum value is 1.", "format": "int32", "type": ["integer", "null"]}, "successThreshold": {"description": "Minimum consecutive successes for the probe to be considered successful after having failed. Defaults to 1. Must be 1 for liveness and startup. Minimum value is 1.", "format": "int32", "type": ["integer", "null"]}, "tcpSocket": {"$ref": "#/core.v1.TCPSocketAction", "description": "TCPSocket specifies an action involving a TCP port."}, "terminationGracePeriodSeconds": {"description": "Optional duration in seconds the pod needs to terminate gracefully upon probe failure. The grace period is the duration in seconds after the processes running in the pod are sent a termination signal and the time when the processes are forcibly halted with a kill signal. Set this value longer than the expected cleanup time for your process. If this value is nil, the pod's terminationGracePeriodSeconds will be used. Otherwise, this value overrides the value provided by the pod spec. Value must be non-negative integer. The value zero indicates stop immediately via the kill signal (no opportunity to shut down). This is a beta field and requires enabling ProbeTerminationGracePeriod feature gate. Minimum value is 1. spec.terminationGracePeriodSeconds is used if unset.", "format": "int64", "type": ["integer", "null"]}, "timeoutSeconds": {"description": "Number of seconds after which the probe times out. Defaults to 1 second. Minimum value is 1. More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes", "format": "int32", "type": ["integer", "null"]}}, "type": "object"}, "core.v1.ResourceRequirements": {"description": "ResourceRequirements describes the compute resource requirements.", "additionalProperties": false, "properties": {"claims": {"description": "Claims lists the names of resources, defined in spec.resourceClaims, that are used by this container.\n\nThis is an alpha field and requires enabling the DynamicResourceAllocation feature gate.\n\nThis field is immutable. It can only be set for containers.", "items": {"properties": {"name": {"description": "Name must match the name of one entry in pod.spec.resourceClaims of the Pod where this field is used. It makes that resource available inside a container.", "type": "string"}}, "required": ["name"]}, "type": ["array", "null"]}, "limits": {"additionalProperties": {"$ref": "#/core.v1.ResourceQuantity"}, "description": "Limits describes the maximum amount of compute resources allowed. More info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/", "type": ["object", "null"]}, "requests": {"additionalProperties": {"$ref": "#/core.v1.ResourceQuantity"}, "description": "Requests describes the minimum amount of compute resources required. If Requests is omitted for a container, it defaults to Limits if that is explicitly specified, otherwise to an implementation-defined value. Requests cannot exceed Limits. More info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/", "type": ["object", "null"]}}, "type": "object"}, "core.v1.ResourceQuantity": {"oneOf": [{"type": "string"}, {"type": "number"}]}, "core.v1.Capabilities": {"description": "Adds and removes POSIX capabilities from running containers.", "properties": {"add": {"description": "Added capabilities", "items": {"type": "string"}, "type": "array"}, "drop": {"description": "Removed capabilities", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "core.v1.ContainerSecurityContext": {"io.k8s.api.core.v1.SecurityContext": {"description": "SecurityContext holds security configuration that will be applied to a container. Some fields are present in both SecurityContext and PodSecurityContext.  When both are set, the values in SecurityContext take precedence.", "properties": {"allowPrivilegeEscalation": {"description": "AllowPrivilegeEscalation controls whether a process can gain more privileges than its parent process. This bool directly controls if the no_new_privs flag will be set on the container process. AllowPrivilegeEscalation is true always when the container is: 1) run as Privileged 2) has CAP_SYS_ADMIN Note that this field cannot be set when spec.os.name is windows.", "type": "boolean"}, "capabilities": {"$ref": "#/core.v1.Capabilities", "description": "The capabilities to add/drop when running containers. Defaults to the default set of capabilities granted by the container runtime. Note that this field cannot be set when spec.os.name is windows."}, "privileged": {"description": "Run container in privileged mode. Processes in privileged containers are essentially equivalent to root on the host. Defaults to false. Note that this field cannot be set when spec.os.name is windows.", "type": "boolean"}, "procMount": {"description": "procMount denotes the type of proc mount to use for the containers. The default is DefaultProcMount which uses the container runtime defaults for readonly paths and masked paths. This requires the ProcMountType feature flag to be enabled. Note that this field cannot be set when spec.os.name is windows.", "type": "string"}, "readOnlyRootFilesystem": {"description": "Whether this container has a read-only root filesystem. Default is false. Note that this field cannot be set when spec.os.name is windows.", "type": "boolean"}, "runAsGroup": {"description": "The GID to run the entrypoint of the container process. Uses runtime default if unset. May also be set in PodSecurityContext.  If set in both SecurityContext and PodSecurityContext, the value specified in SecurityContext takes precedence. Note that this field cannot be set when spec.os.name is windows.", "format": "int64", "type": "integer"}, "runAsNonRoot": {"description": "Indicates that the container must run as a non-root user. If true, the Kubelet will validate the image at runtime to ensure that it does not run as UID 0 (root) and fail to start the container if it does. If unset or false, no such validation will be performed. May also be set in PodSecurityContext.  If set in both SecurityContext and PodSecurityContext, the value specified in SecurityContext takes precedence.", "type": "boolean"}, "runAsUser": {"description": "The UID to run the entrypoint of the container process. Defaults to user specified in image metadata if unspecified. May also be set in PodSecurityContext.  If set in both SecurityContext and PodSecurityContext, the value specified in SecurityContext takes precedence. Note that this field cannot be set when spec.os.name is windows.", "format": "int64", "type": "integer"}, "seLinuxOptions": {"$ref": "#/core.v1.SELinuxOptions", "description": "The SELinux context to be applied to the container. If unspecified, the container runtime will allocate a random SELinux context for each container.  May also be set in PodSecurityContext.  If set in both SecurityContext and PodSecurityContext, the value specified in SecurityContext takes precedence. Note that this field cannot be set when spec.os.name is windows."}, "seccompProfile": {"$ref": "#/core.v1.SeccompProfile", "description": "The seccomp options to use by this container. If seccomp options are provided at both the pod & container level, the container options override the pod options. Note that this field cannot be set when spec.os.name is windows."}}, "type": "object"}}, "core.v1.Lifecycle": {"description": "Lifecycle describes actions that the management system should take in response to container lifecycle events. For the PostStart and PreStop lifecycle handlers, management of the container blocks until the action is complete, unless the container process fails, in which case the handler is aborted.", "properties": {"postStart": {"$ref": "#/core.v1.LifecycleHandler", "description": "PostStart is called immediately after a container is created. If the handler fails, the container is terminated and restarted according to its restart policy. Other management of the container blocks until the hook completes. More info: https://kubernetes.io/docs/concepts/containers/container-lifecycle-hooks/#container-hooks"}, "preStop": {"$ref": "#/core.v1.LifecycleHandler", "description": "PreStop is called immediately before a container is terminated due to an API request or management event such as liveness/startup probe failure, preemption, resource contention, etc. The handler is not called if the container crashes or exits. The Pod's termination grace period countdown begins before the PreStop hook is executed. Regardless of the outcome of the handler, the container will eventually terminate within the Pod's termination grace period (unless delayed by finalizers). Other management of the container blocks until the hook completes or until the termination grace period is reached. More info: https://kubernetes.io/docs/concepts/containers/container-lifecycle-hooks/#container-hooks"}}, "type": "object"}, "core.v1.LifecycleHandler": {"description": "LifecycleHandler defines a specific action that should be taken in a lifecycle hook. One and only one of the fields, except TCPSocket must be specified.", "properties": {"exec": {"$ref": "#/core.v1.ExecAction", "description": "Exec specifies the action to take."}, "httpGet": {"$ref": "#/core.v1.HTTPGetAction", "description": "HTTPGet specifies the http request to perform."}, "sleep": {"$ref": "#/core.v1.SleepAction", "description": "Sleep represents the duration that the container should sleep before being terminated."}, "tcpSocket": {"$ref": "#/core.v1.TCPSocketAction", "description": "Deprecated. TCPSocket is NOT supported as a LifecycleHandler and kept for the backward compatibility. There are no validation of this field and lifecycle hooks will fail in runtime when tcp handler is specified."}}, "type": "object"}, "networking.v1.IPBlock": {"description": "IPBlock describes a particular CIDR (Ex. \"***********/24\",\"2001:db8::/64\") that is allowed to the pods matched by a NetworkPolicySpec's podSelector. The except entry describes CIDRs that should not be included within this rule.", "properties": {"cidr": {"description": "cidr is a string representing the IPBlock Valid examples are \"***********/24\" or \"2001:db8::/64\"", "type": "string"}, "except": {"description": "except is a slice of CIDRs that should not be included within an IPBlock Valid examples are \"***********/24\" or \"2001:db8::/64\" Except values will be rejected if they are outside the cidr range", "items": {"type": "string"}, "type": "array"}}, "required": ["cidr"], "type": "object"}, "networking.v1.NetworkPolicyPeer": {"description": "NetworkPolicyPeer describes a peer to allow traffic to/from. Only certain combinations of fields are allowed", "properties": {"ipBlock": {"$ref": "#/networking.v1.IPBlock", "description": "ipBlock defines policy on a particular IPBlock. If this field is set then neither of the other fields can be."}, "namespaceSelector": {"$ref": "#/meta.v1.LabelSelector", "description": "namespaceSelector selects namespaces using cluster-scoped labels. This field follows standard label selector semantics; if present but empty, it selects all namespaces.\n\nIf podSelector is also set, then the NetworkPolicyPeer as a whole selects the pods matching podSelector in the namespaces selected by namespaceSelector. Otherwise it selects all pods in the namespaces selected by namespaceSelector."}, "podSelector": {"$ref": "#/meta.v1.LabelSelector", "description": "podSelector is a label selector which selects pods. This field follows standard label selector semantics; if present but empty, it selects all pods.\n\nIf namespaceSelector is also set, then the NetworkPolicyPeer as a whole selects the pods matching podSelector in the Namespaces selected by NamespaceSelector. Otherwise it selects the pods matching podSelector in the policy's own namespace."}}, "type": "object"}, "networking.v1.NetworkPolicyPort": {"description": "NetworkPolicyPort describes a port to allow traffic on", "properties": {"endPort": {"description": "endPort indicates that the range of ports from port to endPort if set, inclusive, should be allowed by the policy. This field cannot be defined if the port field is not defined or if the port field is defined as a named (string) port. The endPort must be equal or greater than port.", "format": "int32", "type": "integer"}, "port": {"type": ["string", "integer"], "description": "port represents the port on the given protocol. This can either be a numerical or named port on a pod. If this field is not provided, this matches all port names and numbers. If present, only traffic on the specified protocol AND port will be matched."}, "protocol": {"description": "protocol represents the protocol (TCP, UDP, or SCTP) which traffic must match. If not specified, this field defaults to TCP.", "type": "string"}}, "type": "object"}, "networking.v1.NetworkPolicyEgressRule": {"description": "NetworkPolicyEgressRule describes a particular set of traffic that is allowed out of pods matched by a NetworkPolicySpec's podSelector. The traffic must match both ports and to. This type is beta-level in 1.8", "properties": {"ports": {"description": "ports is a list of destination ports for outgoing traffic. Each item in this list is combined using a logical OR. If this field is empty or missing, this rule matches all ports (traffic not restricted by port). If this field is present and contains at least one item, then this rule allows traffic only if the traffic matches at least one port in the list.", "items": {"$ref": "#/networking.v1.NetworkPolicyPort"}, "type": "array"}, "to": {"description": "to is a list of destinations for outgoing traffic of pods selected for this rule. Items in this list are combined using a logical OR operation. If this field is empty or missing, this rule matches all destinations (traffic not restricted by destination). If this field is present and contains at least one item, this rule allows traffic only if the traffic matches at least one item in the to list.", "items": {"$ref": "#/networking.v1.NetworkPolicyPeer"}, "type": "array"}}, "type": "object"}, "networking.v1.NetworkPolicyIngressRule": {"description": "NetworkPolicyIngressRule describes a particular set of traffic that is allowed to the pods matched by a NetworkPolicySpec's podSelector. The traffic must match both ports and from.", "properties": {"from": {"description": "from is a list of sources which should be able to access the pods selected for this rule. Items in this list are combined using a logical OR operation. If this field is empty or missing, this rule matches all sources (traffic not restricted by source). If this field is present and contains at least one item, this rule allows traffic only if the traffic matches at least one item in the from list.", "items": {"$ref": "#/networking.v1.NetworkPolicyPeer"}, "type": "array"}, "ports": {"description": "ports is a list of ports which should be made accessible on the pods selected for this rule. Each item in this list is combined using a logical OR. If this field is empty or missing, this rule matches all ports (traffic not restricted by port). If this field is present and contains at least one item, then this rule allows traffic only if the traffic matches at least one port in the list.", "items": {"$ref": "#/networking.v1.NetworkPolicyPort"}, "type": "array"}}, "type": "object"}}