controllers:
  main:
    strategy: Recreate
    type: deployment
    replicas: 1
    containers:
      main:
        image:
          repository: acrah01seadahss01.azurecr.io/ahss/ahss-uam-web
          tag: latest
          pullPolicy: Always
        envFrom:
          - secret: secret
          - secretRef:
              name: ahss-database-credentials
service:
  main:
    controller: main
    ports:
      http:
        port: 8011
        protocol: HTTP

ingress:
  main:
    enabled: true
    className: azure-application-gateway
    annotations:
      appgw.ingress.kubernetes.io/backend-path-prefix: "/"
      appgw.ingress.kubernetes.io/use-private-ip: "true"
      appgw.ingress.kubernetes.io/health-probe-port: "8011"
      appgw.ingress.kubernetes.io/health-probe-path: "/actuator/health"
    hosts:
      - host: uat.api.ahpp.amplifyhealth.com
        paths:
          - path: /ahss-uam/*
            pathType: Prefix
            service:
              identifier: main
    tls:
      - hosts:
          - uat.api.ahpp.amplifyhealth.com
        secretName: uatapiahpp-tls

secrets:
  secret:
    enabled: true
    stringData:
      DB_URL: *************************************************************************
      SPRING_PROFILES_ACTIVE: "uat"
      APPLICATIONINSIGHTS_CONNECTION_STRING: "InstrumentationKey=a15905d2-b857-4086-be5e-98035dad263b;IngestionEndpoint=https://southeastasia-1.in.applicationinsights.azure.com/;LiveEndpoint=https://southeastasia.livediagnostics.monitor.azure.com/;ApplicationId=e607651e-4eed-431a-98f4-0dba785aa1a7"


