{"item": {"oneOf": [{"$ref": "#/persistentVolumeClaimItem"}, {"$ref": "#/persistentVolumeClaimWithExistingClaimItem"}, {"$ref": "#/configMapItem"}, {"$ref": "#/secretItem"}, {"$ref": "#/nfsItem"}, {"$ref": "#/emptyDirItem"}, {"$ref": "#/hostPathItem"}, {"$ref": "#/customItem"}]}, "persistentVolumeClaimItem": {"type": "object", "additionalProperties": false, "properties": {"type": {"const": "persistentVolumeClaim"}, "enabled": {"type": "boolean", "default": true}, "accessMode": {"type": "string"}, "annotations": {"$ref": "definitions.json#/annotations"}, "dataSource": {"$ref": "#/dataSourceReference"}, "dataSourceRef": {"$ref": "#/dataSourceReference"}, "labels": {"$ref": "definitions.json#/labels"}, "nameOverride": {"type": "string"}, "retain": {"type": "boolean"}, "size": {"type": "string"}, "storageClass": {"type": "string"}, "volumeName": {"type": "string"}, "advancedMounts": {"$ref": "#/advancedMounts"}, "globalMounts": {"$ref": "#/globalMounts"}}, "required": ["accessMode", "size"]}, "persistentVolumeClaimWithExistingClaimItem": {"type": "object", "additionalProperties": false, "properties": {"type": {"const": "persistentVolumeClaim"}, "enabled": {"type": "boolean", "default": true}, "existingClaim": {"type": "string"}, "advancedMounts": {"$ref": "#/advancedMounts"}, "globalMounts": {"$ref": "#/globalMounts"}}, "required": ["existingClaim"]}, "configMapItem": {"type": "object", "additionalProperties": false, "properties": {"type": {"const": "configMap"}, "enabled": {"type": "boolean", "default": true}, "name": {"type": "string"}, "identifier": {"type": "string"}, "defaultMode": {"type": "integer", "format": "int32"}, "items": {"type": "array", "items": {"type": "object", "properties": {"key": {"type": "string"}, "mode": {"format": "int32", "type": "integer"}, "path": {"type": "string"}}}}, "advancedMounts": {"$ref": "#/advancedMounts"}, "globalMounts": {"$ref": "#/globalMounts"}}, "oneOf": [{"required": ["name"]}, {"required": ["identifier"]}], "dependencies": {"name": {"not": {"required": ["identifier"]}}, "identifier": {"not": {"required": ["name"]}}}}, "secretItem": {"type": "object", "additionalProperties": false, "properties": {"type": {"const": "secret"}, "enabled": {"type": "boolean", "default": true}, "name": {"type": "string"}, "identifier": {"type": "string"}, "defaultMode": {"type": "integer", "format": "int32"}, "items": {"type": "array", "items": {"type": "object", "properties": {"key": {"type": "string"}, "mode": {"format": "int32", "type": "integer"}, "path": {"type": "string"}}}}, "advancedMounts": {"$ref": "#/advancedMounts"}, "globalMounts": {"$ref": "#/globalMounts"}}, "oneOf": [{"required": ["name"]}, {"required": ["identifier"]}], "dependencies": {"name": {"not": {"required": ["identifier"]}}, "identifier": {"not": {"required": ["name"]}}}}, "nfsItem": {"type": "object", "additionalProperties": false, "properties": {"type": {"const": "nfs"}, "enabled": {"type": "boolean", "default": true}, "path": {"type": "string"}, "server": {"type": "string"}, "advancedMounts": {"$ref": "#/advancedMounts"}, "globalMounts": {"$ref": "#/globalMounts"}}, "required": ["server", "path"]}, "emptyDirItem": {"type": "object", "additionalProperties": false, "properties": {"type": {"const": "emptyDir"}, "enabled": {"type": "boolean", "default": true}, "medium": {"type": "string"}, "sizeLimit": {"type": "string"}, "advancedMounts": {"$ref": "#/advancedMounts"}, "globalMounts": {"$ref": "#/globalMounts"}}}, "hostPathItem": {"type": "object", "additionalProperties": false, "properties": {"type": {"const": "hostPath"}, "enabled": {"type": "boolean", "default": true}, "hostPath": {"type": "string"}, "hostPathType": {"type": "string"}, "advancedMounts": {"$ref": "#/advancedMounts"}, "globalMounts": {"$ref": "#/globalMounts"}}}, "customItem": {"type": "object", "additionalProperties": false, "properties": {"type": {"const": "custom"}, "enabled": {"type": "boolean", "default": true}, "volumeSpec": {"type": "object"}, "globalMounts": {"$ref": "#/globalMounts"}, "advancedMounts": {"$ref": "#/advancedMounts"}}, "required": ["volumeSpec"]}, "dataSourceReference": {"type": "object", "additionalProperties": false, "properties": {"apiGroup": {"type": "string"}, "kind": {"type": "string"}, "name": {"type": "string"}}, "required": ["kind", "name"]}, "globalMounts": {"type": "array", "items": {"$ref": "#/mountPath"}}, "advancedMounts": {"additionalProperties": {"type": "object", "additionalProperties": {"$ref": "#/mountPathCollection"}}}, "mountPathCollection": {"type": "array", "items": {"$ref": "#/mountPath"}}, "mountPath": {"additionalProperties": false, "properties": {"path": {"type": "string"}, "readOnly": {"type": "boolean"}, "subPath": {"type": "string"}, "mountPropagation": {"type": "string"}}}}