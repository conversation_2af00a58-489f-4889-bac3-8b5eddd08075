controllers:
  main:
    strategy: Recreate
    type: deployment
    replicas: 1
    containers:
      main:
        image:
          repository: acrah01seadahss01.azurecr.io/ahss/ahss-gateway-web
          tag: latest
          pullPolicy: Always
        envFrom:
          - secret: secret

service:
  main:
    controller: main
    ports:
      http:
        port: 8888
        protocol: HTTP

ingress:
  main:
    enabled: true
    className: azure-application-gateway
    annotations:
      appgw.ingress.kubernetes.io/backend-path-prefix: "/"
      appgw.ingress.kubernetes.io/use-private-ip: "true"
      appgw.ingress.kubernetes.io/health-probe-port: "8888"
      appgw.ingress.kubernetes.io/health-probe-path: "/actuator/health"
    hosts:
      - host: sit.api.ahpp.amplifyhealth.com
        paths:
          - path: /ahss-gateway/*
            pathType: Prefix
            service:
              identifier: main
    tls:
      - hosts:
          - sit.api.ahpp.amplifyhealth.com
        secretName: sitapiahpp-tls

secrets:
  secret:
    enabled: true
    stringData:
      DB_URL: "*************************************************************************"
      AHPP_PATH: "/ahss-pp/**"
      AHPP_SCHEME: "http"
      AHPP_HOST: "ahss-pp-web"
      AHPP_PORT: "8001"
      UAM_PATH: "/ahss-uam/**"
      UAM_SCHEME: "http"
      UAM_HOST: "ahss-uam-web"
      UAM_PORT: "8011"
      SPRING_PROFILES_ACTIVE: "sit"
      APPLICATIONINSIGHTS_CONNECTION_STRING: "InstrumentationKey=a15905d2-b857-4086-be5e-98035dad263b;IngestionEndpoint=https://southeastasia-1.in.applicationinsights.azure.com/;LiveEndpoint=https://southeastasia.livediagnostics.monitor.azure.com/;ApplicationId=e607651e-4eed-431a-98f4-0dba785aa1a7"


