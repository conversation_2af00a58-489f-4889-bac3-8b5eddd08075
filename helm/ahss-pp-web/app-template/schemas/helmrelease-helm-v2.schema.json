{"description": "HelmRelease is the Schema for the helmreleases API", "properties": {"apiVersion": {"description": "APIVersion defines the versioned schema of this representation of an object.\nServers should convert recognized schemas to the latest internal value, and\nmay reject unrecognized values.\nMore info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources", "type": "string"}, "kind": {"description": "Kind is a string value representing the REST resource this object represents.\nServers may infer this from the endpoint the client submits requests to.\nCannot be updated.\nIn CamelCase.\nMore info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds", "type": "string"}, "metadata": {"type": "object"}, "spec": {"description": "HelmReleaseSpec defines the desired state of a Helm release.", "properties": {"chart": {"description": "Chart defines the template of the v1.HelmChart that should be created\nfor this HelmRelease.", "properties": {"metadata": {"description": "ObjectMeta holds the template for metadata like labels and annotations.", "properties": {"annotations": {"additionalProperties": {"type": "string"}, "description": "Annotations is an unstructured key value map stored with a resource that may be\nset by external tools to store and retrieve arbitrary metadata. They are not\nqueryable and should be preserved when modifying objects.\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/annotations/", "type": "object"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Map of string keys and values that can be used to organize and categorize\n(scope and select) objects.\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/labels/", "type": "object"}}, "type": "object", "additionalProperties": false}, "spec": {"description": "Spec holds the template for the v1.HelmChartSpec for this HelmRelease.", "properties": {"chart": {"description": "The name or path the Helm chart is available at in the SourceRef.", "maxLength": 2048, "minLength": 1, "type": "string"}, "ignoreMissingValuesFiles": {"description": "IgnoreMissingValuesFiles controls whether to silently ignore missing values files rather than failing.", "type": "boolean"}, "interval": {"description": "Interval at which to check the v1.Source for updates. Defaults to\n'HelmReleaseSpec.Interval'.", "pattern": "^([0-9]+(\\.[0-9]+)?(ms|s|m|h))+$", "type": "string"}, "reconcileStrategy": {"default": "ChartVersion", "description": "Determines what enables the creation of a new artifact. Valid values are\n('ChartVersion', 'Revision').\nSee the documentation of the values for an explanation on their behavior.\nDefaults to ChartVersion when omitted.", "enum": ["ChartVersion", "Revision"], "type": "string"}, "sourceRef": {"description": "The name and namespace of the v1.Source the chart is available at.", "properties": {"apiVersion": {"description": "APIVersion of the referent.", "type": "string"}, "kind": {"description": "Kind of the referent.", "enum": ["HelmRepository", "GitRepository", "Bucket"], "type": "string"}, "name": {"description": "Name of the referent.", "maxLength": 253, "minLength": 1, "type": "string"}, "namespace": {"description": "Namespace of the referent.", "maxLength": 63, "minLength": 1, "type": "string"}}, "required": ["name"], "type": "object", "additionalProperties": false}, "valuesFiles": {"description": "Alternative list of values files to use as the chart values (values.yaml\nis not included by default), expected to be a relative path in the SourceRef.\nValues files are merged in the order of this list with the last file overriding\nthe first. Ignored when omitted.", "items": {"type": "string"}, "type": "array"}, "verify": {"description": "Verify contains the secret name containing the trusted public keys\nused to verify the signature and specifies which provider to use to check\nwhether OCI image is authentic.\nThis field is only supported for OCI sources.\nChart dependencies, which are not bundled in the umbrella chart artifact,\nare not verified.", "properties": {"provider": {"default": "cosign", "description": "Provider specifies the technology used to sign the OCI Helm chart.", "enum": ["cosign", "notation"], "type": "string"}, "secretRef": {"description": "SecretRef specifies the Kubernetes Secret containing the\ntrusted public keys.", "properties": {"name": {"description": "Name of the referent.", "type": "string"}}, "required": ["name"], "type": "object", "additionalProperties": false}}, "required": ["provider"], "type": "object", "additionalProperties": false}, "version": {"default": "*", "description": "Version semver expression, ignored for charts from v1.GitRepository and\nv1beta2.Bucket sources. Defaults to latest when omitted.", "type": "string"}}, "required": ["chart", "sourceRef"], "type": "object", "additionalProperties": false}}, "required": ["spec"], "type": "object", "additionalProperties": false}, "chartRef": {"description": "ChartRef holds a reference to a source controller resource containing the\nHelm chart artifact.", "properties": {"apiVersion": {"description": "APIVersion of the referent.", "type": "string"}, "kind": {"description": "Kind of the referent.", "enum": ["OCIRepository", "<PERSON><PERSON><PERSON><PERSON>"], "type": "string"}, "name": {"description": "Name of the referent.", "maxLength": 253, "minLength": 1, "type": "string"}, "namespace": {"description": "Namespace of the referent, defaults to the namespace of the Kubernetes\nresource object that contains the reference.", "maxLength": 63, "minLength": 1, "type": "string"}}, "required": ["kind", "name"], "type": "object", "additionalProperties": false}, "dependsOn": {"description": "DependsOn may contain a meta.NamespacedObjectReference slice with\nreferences to HelmRelease resources that must be ready before this HelmRelease\ncan be reconciled.", "items": {"description": "NamespacedObjectReference contains enough information to locate the referenced Kubernetes resource object in any\nnamespace.", "properties": {"name": {"description": "Name of the referent.", "type": "string"}, "namespace": {"description": "Namespace of the referent, when not specified it acts as LocalObjectReference.", "type": "string"}}, "required": ["name"], "type": "object", "additionalProperties": false}, "type": "array"}, "driftDetection": {"description": "DriftDetection holds the configuration for detecting and handling\ndifferences between the manifest in the Helm storage and the resources\ncurrently existing in the cluster.", "properties": {"ignore": {"description": "Ignore contains a list of rules for specifying which changes to ignore\nduring diffing.", "items": {"description": "IgnoreRule defines a rule to selectively disregard specific changes during\nthe drift detection process.", "properties": {"paths": {"description": "Paths is a list of JSON Pointer (RFC 6901) paths to be excluded from\nconsideration in a Kubernetes object.", "items": {"type": "string"}, "type": "array"}, "target": {"description": "Target is a selector for specifying Kubernetes objects to which this\nrule applies.\nIf Target is not set, the Paths will be ignored for all Kubernetes\nobjects within the manifest of the Helm release.", "properties": {"annotationSelector": {"description": "AnnotationSelector is a string that follows the label selection expression\nhttps://kubernetes.io/docs/concepts/overview/working-with-objects/labels/#api\nIt matches with the resource annotations.", "type": "string"}, "group": {"description": "Group is the API group to select resources from.\nTogether with Version and Kind it is capable of unambiguously identifying and/or selecting resources.\nhttps://github.com/kubernetes/community/blob/master/contributors/design-proposals/api-machinery/api-group.md", "type": "string"}, "kind": {"description": "Kind of the API Group to select resources from.\nTogether with Group and Version it is capable of unambiguously\nidentifying and/or selecting resources.\nhttps://github.com/kubernetes/community/blob/master/contributors/design-proposals/api-machinery/api-group.md", "type": "string"}, "labelSelector": {"description": "LabelSelector is a string that follows the label selection expression\nhttps://kubernetes.io/docs/concepts/overview/working-with-objects/labels/#api\nIt matches with the resource labels.", "type": "string"}, "name": {"description": "Name to match resources with.", "type": "string"}, "namespace": {"description": "Namespace to select resources from.", "type": "string"}, "version": {"description": "Version of the API Group to select resources from.\nTogether with Group and Kind it is capable of unambiguously identifying and/or selecting resources.\nhttps://github.com/kubernetes/community/blob/master/contributors/design-proposals/api-machinery/api-group.md", "type": "string"}}, "type": "object", "additionalProperties": false}}, "required": ["paths"], "type": "object", "additionalProperties": false}, "type": "array"}, "mode": {"description": "Mode defines how differences should be handled between the Helm manifest\nand the manifest currently applied to the cluster.\nIf not explicitly set, it defaults to DiffModeDisabled.", "enum": ["enabled", "warn", "disabled"], "type": "string"}}, "type": "object", "additionalProperties": false}, "install": {"description": "Install holds the configuration for Helm install actions for this HelmRelease.", "properties": {"crds": {"description": "CRDs upgrade CRDs from the Helm Chart's crds directory according\nto the CRD upgrade policy provided here. Valid values are `Skip`,\n`Create` or `CreateReplace`. Default is `Create` and if omitted\nCRDs are installed but not updated.\n\n\nSkip: do neither install nor replace (update) any CRDs.\n\n\nCreate: new CRDs are created, existing CRDs are neither updated nor deleted.\n\n\nCreateReplace: new CRDs are created, existing CRDs are updated (replaced)\nbut not deleted.\n\n\nBy default, CRDs are applied (installed) during Helm install action.\nWith this option users can opt in to CRD replace existing CRDs on Helm\ninstall actions, which is not (yet) natively supported by Helm.\nhttps://helm.sh/docs/chart_best_practices/custom_resource_definitions.", "enum": ["<PERSON><PERSON>", "Create", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "type": "string"}, "createNamespace": {"description": "CreateNamespace tells the Helm install action to create the\nHelmReleaseSpec.TargetNamespace if it does not exist yet.\nOn uninstall, the namespace will not be garbage collected.", "type": "boolean"}, "disableHooks": {"description": "DisableHooks prevents hooks from running during the Helm install action.", "type": "boolean"}, "disableOpenAPIValidation": {"description": "DisableOpenAPIValidation prevents the Helm install action from validating\nrendered templates against the Kubernetes OpenAPI Schema.", "type": "boolean"}, "disableWait": {"description": "Disable<PERSON><PERSON> disables the waiting for resources to be ready after a Helm\ninstall has been performed.", "type": "boolean"}, "disableWaitForJobs": {"description": "Disable<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> disables waiting for jobs to complete after a Helm\ninstall has been performed.", "type": "boolean"}, "remediation": {"description": "Remediation holds the remediation configuration for when the Helm install\naction for the HelmRelease fails. The default is to not perform any action.", "properties": {"ignoreTestFailures": {"description": "IgnoreTestFailures tells the controller to skip remediation when the Helm\ntests are run after an install action but fail. Defaults to\n'Test.IgnoreFailures'.", "type": "boolean"}, "remediateLastFailure": {"description": "RemediateLastFailure tells the controller to remediate the last failure, when\nno retries remain. Defaults to 'false'.", "type": "boolean"}, "retries": {"description": "Retries is the number of retries that should be attempted on failures before\nbailing. Remediation, using an uninstall, is performed between each attempt.\nDefaults to '0', a negative integer equals to unlimited retries.", "type": "integer"}}, "type": "object", "additionalProperties": false}, "replace": {"description": "<PERSON><PERSON> tells the Helm install action to re-use the 'ReleaseName', but only\nif that name is a deleted release which remains in the history.", "type": "boolean"}, "skipCRDs": {"description": "SkipCRDs tells the Helm install action to not install any CRDs. By default,\nCRDs are installed if not already present.\n\n\nDeprecated use CRD policy (`crds`) attribute with value `Skip` instead.", "type": "boolean"}, "timeout": {"description": "Timeout is the time to wait for any individual Kubernetes operation (like\n<PERSON><PERSON> for hooks) during the performance of a Helm install action. Defaults to\n'HelmReleaseSpec.Timeout'.", "pattern": "^([0-9]+(\\.[0-9]+)?(ms|s|m|h))+$", "type": "string"}}, "type": "object", "additionalProperties": false}, "interval": {"description": "Interval at which to reconcile the <PERSON><PERSON> release.", "pattern": "^([0-9]+(\\.[0-9]+)?(ms|s|m|h))+$", "type": "string"}, "kubeConfig": {"description": "KubeConfig for reconciling the HelmRelease on a remote cluster.\nWhen used in combination with HelmReleaseSpec.ServiceAccountName,\nforces the controller to act on behalf of that Service Account at the\ntarget cluster.\nIf the --default-service-account flag is set, its value will be used as\na controller level fallback for when HelmReleaseSpec.ServiceAccountName\nis empty.", "properties": {"secretRef": {"description": "SecretRef holds the name of a secret that contains a key with\nthe kubeconfig file as the value. If no key is set, the key will default\nto 'value'.\nIt is recommended that the kubeconfig is self-contained, and the secret\nis regularly updated if credentials such as a cloud-access-token expire.\nCloud specific `cmd-path` auth helpers will not function without adding\nbinaries and credentials to the Pod that is responsible for reconciling\nKubernetes resources.", "properties": {"key": {"description": "Key in the Secret, when not specified an implementation-specific default key is used.", "type": "string"}, "name": {"description": "Name of the Secret.", "type": "string"}}, "required": ["name"], "type": "object", "additionalProperties": false}}, "required": ["secretRef"], "type": "object", "additionalProperties": false}, "maxHistory": {"description": "MaxHistory is the number of revisions saved by <PERSON><PERSON> for this HelmRelease.\nUse '0' for an unlimited number of revisions; defaults to '5'.", "type": "integer"}, "persistentClient": {"description": "PersistentClient tells the controller to use a persistent Kubernetes\nclient for this release. When enabled, the client will be reused for the\nduration of the reconciliation, instead of being created and destroyed\nfor each (step of a) Helm action.\n\n\nThis can improve performance, but may cause issues with some Helm charts\nthat for example do create Custom Resource Definitions during installation\noutside Helm's CRD lifecycle hooks, which are then not observed to be\navailable by e.g. post-install hooks.\n\n\nIf not set, it defaults to true.", "type": "boolean"}, "postRenderers": {"description": "PostRenderers holds an array of Helm <PERSON>enderers, which will be applied in order\nof their definition.", "items": {"description": "PostRenderer contains a <PERSON><PERSON>er specification.", "properties": {"kustomize": {"description": "Kustomization to apply as Post<PERSON><PERSON>er.", "properties": {"images": {"description": "Images is a list of (image name, new name, new tag or digest)\nfor changing image names, tags or digests. This can also be achieved with a\npatch, but this operator is simpler to specify.", "items": {"description": "Image contains an image name, a new name, a new tag or digest, which will replace the original name and tag.", "properties": {"digest": {"description": "Digest is the value used to replace the original image tag.\nIf digest is present NewTag value is ignored.", "type": "string"}, "name": {"description": "Name is a tag-less image name.", "type": "string"}, "newName": {"description": "NewName is the value used to replace the original name.", "type": "string"}, "newTag": {"description": "NewTag is the value used to replace the original tag.", "type": "string"}}, "required": ["name"], "type": "object", "additionalProperties": false}, "type": "array"}, "patches": {"description": "Strategic merge and JSON patches, defined as inline YAML objects,\ncapable of targeting objects based on kind, label and annotation selectors.", "items": {"description": "Patch contains an inline StrategicMerge or JSON6902 patch, and the target the patch should\nbe applied to.", "properties": {"patch": {"description": "Patch contains an inline StrategicMerge patch or an inline JSON6902 patch with\nan array of operation objects.", "type": "string"}, "target": {"description": "Target points to the resources that the patch document should be applied to.", "properties": {"annotationSelector": {"description": "AnnotationSelector is a string that follows the label selection expression\nhttps://kubernetes.io/docs/concepts/overview/working-with-objects/labels/#api\nIt matches with the resource annotations.", "type": "string"}, "group": {"description": "Group is the API group to select resources from.\nTogether with Version and Kind it is capable of unambiguously identifying and/or selecting resources.\nhttps://github.com/kubernetes/community/blob/master/contributors/design-proposals/api-machinery/api-group.md", "type": "string"}, "kind": {"description": "Kind of the API Group to select resources from.\nTogether with Group and Version it is capable of unambiguously\nidentifying and/or selecting resources.\nhttps://github.com/kubernetes/community/blob/master/contributors/design-proposals/api-machinery/api-group.md", "type": "string"}, "labelSelector": {"description": "LabelSelector is a string that follows the label selection expression\nhttps://kubernetes.io/docs/concepts/overview/working-with-objects/labels/#api\nIt matches with the resource labels.", "type": "string"}, "name": {"description": "Name to match resources with.", "type": "string"}, "namespace": {"description": "Namespace to select resources from.", "type": "string"}, "version": {"description": "Version of the API Group to select resources from.\nTogether with Group and Kind it is capable of unambiguously identifying and/or selecting resources.\nhttps://github.com/kubernetes/community/blob/master/contributors/design-proposals/api-machinery/api-group.md", "type": "string"}}, "type": "object", "additionalProperties": false}}, "required": ["patch"], "type": "object", "additionalProperties": false}, "type": "array"}}, "type": "object", "additionalProperties": false}}, "type": "object", "additionalProperties": false}, "type": "array"}, "releaseName": {"description": "ReleaseName used for the Helm release. Defaults to a composition of\n'[TargetNamespace-]Name'.", "maxLength": 53, "minLength": 1, "type": "string"}, "rollback": {"description": "Rollback holds the configuration for <PERSON><PERSON> rollback actions for this HelmRelease.", "properties": {"cleanupOnFail": {"description": "CleanupOnFail allows deletion of new resources created during the Helm\nrollback action when it fails.", "type": "boolean"}, "disableHooks": {"description": "Disable<PERSON><PERSON><PERSON> prevents hooks from running during the <PERSON><PERSON> rollback action.", "type": "boolean"}, "disableWait": {"description": "Disable<PERSON><PERSON> disables the waiting for resources to be ready after a Helm\nrollback has been performed.", "type": "boolean"}, "disableWaitForJobs": {"description": "Disable<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> disables waiting for jobs to complete after a <PERSON><PERSON>\nrollback has been performed.", "type": "boolean"}, "force": {"description": "Force forces resource updates through a replacement strategy.", "type": "boolean"}, "recreate": {"description": "Recreate performs pod restarts for the resource if applicable.", "type": "boolean"}, "timeout": {"description": "Timeout is the time to wait for any individual Kubernetes operation (like\n<PERSON><PERSON> for hooks) during the performance of a Helm rollback action. Defaults to\n'HelmReleaseSpec.Timeout'.", "pattern": "^([0-9]+(\\.[0-9]+)?(ms|s|m|h))+$", "type": "string"}}, "type": "object", "additionalProperties": false}, "serviceAccountName": {"description": "The name of the Kubernetes service account to impersonate\nwhen reconciling this HelmRelease.", "maxLength": 253, "minLength": 1, "type": "string"}, "storageNamespace": {"description": "StorageNamespace used for the Helm storage.\nDefaults to the namespace of the HelmRelease.", "maxLength": 63, "minLength": 1, "type": "string"}, "suspend": {"description": "Suspend tells the controller to suspend reconciliation for this HelmRelease,\nit does not apply to already started reconciliations. Defaults to false.", "type": "boolean"}, "targetNamespace": {"description": "TargetNamespace to target when performing operations for the HelmRelease.\nDefaults to the namespace of the HelmRelease.", "maxLength": 63, "minLength": 1, "type": "string"}, "test": {"description": "Test holds the configuration for <PERSON><PERSON> test actions for this HelmRelease.", "properties": {"enable": {"description": "Enable enables Helm test actions for this HelmRelease after an Helm install\nor upgrade action has been performed.", "type": "boolean"}, "filters": {"description": "Filters is a list of tests to run or exclude from running.", "items": {"description": "Filter holds the configuration for individual <PERSON><PERSON> test filters.", "properties": {"exclude": {"description": "Exclude specifies whether the named test should be excluded.", "type": "boolean"}, "name": {"description": "Name is the name of the test.", "maxLength": 253, "minLength": 1, "type": "string"}}, "required": ["name"], "type": "object", "additionalProperties": false}, "type": "array"}, "ignoreFailures": {"description": "IgnoreFailures tells the controller to skip remediation when the Helm tests\nare run but fail. Can be overwritten for tests run after install or upgrade\nactions in 'Install.IgnoreTestFailures' and 'Upgrade.IgnoreTestFailures'.", "type": "boolean"}, "timeout": {"description": "Timeout is the time to wait for any individual Kubernetes operation during\nthe performance of a Helm test action. Defaults to 'HelmReleaseSpec.Timeout'.", "pattern": "^([0-9]+(\\.[0-9]+)?(ms|s|m|h))+$", "type": "string"}}, "type": "object", "additionalProperties": false}, "timeout": {"description": "Timeout is the time to wait for any individual Kubernetes operation (like <PERSON><PERSON>\nfor hooks) during the performance of a Helm action. Defaults to '5m0s'.", "pattern": "^([0-9]+(\\.[0-9]+)?(ms|s|m|h))+$", "type": "string"}, "uninstall": {"description": "Uninstall holds the configuration for Helm uninstall actions for this HelmRelease.", "properties": {"deletionPropagation": {"default": "background", "description": "DeletionPropagation specifies the deletion propagation policy when\na Helm uninstall is performed.", "enum": ["background", "foreground", "orphan"], "type": "string"}, "disableHooks": {"description": "Disable<PERSON><PERSON><PERSON> prevents hooks from running during the <PERSON><PERSON> rollback action.", "type": "boolean"}, "disableWait": {"description": "Disable<PERSON><PERSON> disables waiting for all the resources to be deleted after\na Helm uninstall is performed.", "type": "boolean"}, "keepHistory": {"description": "KeepHistory tells <PERSON><PERSON> to remove all associated resources and mark the\nrelease as deleted, but retain the release history.", "type": "boolean"}, "timeout": {"description": "Timeout is the time to wait for any individual Kubernetes operation (like\n<PERSON><PERSON> for hooks) during the performance of a Helm uninstall action. Defaults\nto 'HelmReleaseSpec.Timeout'.", "pattern": "^([0-9]+(\\.[0-9]+)?(ms|s|m|h))+$", "type": "string"}}, "type": "object", "additionalProperties": false}, "upgrade": {"description": "Upgrade holds the configuration for Helm upgrade actions for this HelmRelease.", "properties": {"cleanupOnFail": {"description": "CleanupOnFail allows deletion of new resources created during the Helm\nupgrade action when it fails.", "type": "boolean"}, "crds": {"description": "CRDs upgrade CRDs from the Helm Chart's crds directory according\nto the CRD upgrade policy provided here. Valid values are `Skip`,\n`Create` or `CreateReplace`. Default is `Skip` and if omitted\nCRDs are neither installed nor upgraded.\n\n\nSkip: do neither install nor replace (update) any CRDs.\n\n\nCreate: new CRDs are created, existing CRDs are neither updated nor deleted.\n\n\nCreateReplace: new CRDs are created, existing CRDs are updated (replaced)\nbut not deleted.\n\n\nBy default, CRDs are not applied during Helm upgrade action. With this\noption users can opt-in to CRD upgrade, which is not (yet) natively supported by Helm.\nhttps://helm.sh/docs/chart_best_practices/custom_resource_definitions.", "enum": ["<PERSON><PERSON>", "Create", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "type": "string"}, "disableHooks": {"description": "Disable<PERSON>ooks prevents hooks from running during the Helm upgrade action.", "type": "boolean"}, "disableOpenAPIValidation": {"description": "DisableOpenAPIValidation prevents the Helm upgrade action from validating\nrendered templates against the Kubernetes OpenAPI Schema.", "type": "boolean"}, "disableWait": {"description": "Disable<PERSON>ait disables the waiting for resources to be ready after a Helm\nupgrade has been performed.", "type": "boolean"}, "disableWaitForJobs": {"description": "Disable<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> disables waiting for jobs to complete after a Helm\nupgrade has been performed.", "type": "boolean"}, "force": {"description": "Force forces resource updates through a replacement strategy.", "type": "boolean"}, "preserveValues": {"description": "PreserveValues will make <PERSON><PERSON> reuse the last release's values and merge in\noverrides from 'Values'. Setting this flag makes the HelmRelease\nnon-declarative.", "type": "boolean"}, "remediation": {"description": "Remediation holds the remediation configuration for when the Helm upgrade\naction for the HelmRelease fails. The default is to not perform any action.", "properties": {"ignoreTestFailures": {"description": "IgnoreTestFailures tells the controller to skip remediation when the Helm\ntests are run after an upgrade action but fail.\nDefaults to 'Test.IgnoreFailures'.", "type": "boolean"}, "remediateLastFailure": {"description": "RemediateLastFailure tells the controller to remediate the last failure, when\nno retries remain. Defaults to 'false' unless 'Retries' is greater than 0.", "type": "boolean"}, "retries": {"description": "Retries is the number of retries that should be attempted on failures before\nbailing. Remediation, using 'Strategy', is performed between each attempt.\nDefaults to '0', a negative integer equals to unlimited retries.", "type": "integer"}, "strategy": {"description": "Strategy to use for failure remediation. Defaults to 'rollback'.", "enum": ["rollback", "uninstall"], "type": "string"}}, "type": "object", "additionalProperties": false}, "timeout": {"description": "Timeout is the time to wait for any individual Kubernetes operation (like\n<PERSON><PERSON> for hooks) during the performance of a Helm upgrade action. Defaults to\n'HelmReleaseSpec.Timeout'.", "pattern": "^([0-9]+(\\.[0-9]+)?(ms|s|m|h))+$", "type": "string"}}, "type": "object", "additionalProperties": false}, "values": {"description": "Values holds the values for this Helm release.", "x-kubernetes-preserve-unknown-fields": true, "$ref": "https://raw.githubusercontent.com/bjw-s/helm-charts/common-3.2.1/charts/library/common/values.schema.json"}, "valuesFrom": {"description": "ValuesFrom holds references to resources containing Helm values for this HelmRelease,\nand information about how they should be merged.", "items": {"description": "ValuesReference contains a reference to a resource containing Helm values,\nand optionally the key they can be found at.", "properties": {"kind": {"description": "Kind of the values referent, valid values are ('Secret', 'ConfigMap').", "enum": ["Secret", "ConfigMap"], "type": "string"}, "name": {"description": "Name of the values referent. Should reside in the same namespace as the\nreferring resource.", "maxLength": 253, "minLength": 1, "type": "string"}, "optional": {"description": "Optional marks this ValuesReference as optional. When set, a not found error\nfor the values reference is ignored, but any ValuesKey, TargetPath or\ntransient error will still result in a reconciliation failure.", "type": "boolean"}, "targetPath": {"description": "TargetPath is the YAML dot notation path the value should be merged at. When\nset, the ValuesKey is expected to be a single flat value. Defaults to 'None',\nwhich results in the values getting merged at the root.", "maxLength": 250, "pattern": "^([a-zA-Z0-9_\\-.\\\\\\/]|\\[[0-9]{1,5}\\])+$", "type": "string"}, "valuesKey": {"description": "ValuesKey is the data key where the values.yaml or a specific value can be\nfound at. Defaults to 'values.yaml'.", "maxLength": 253, "pattern": "^[\\-._a-zA-Z0-9]+$", "type": "string"}}, "required": ["kind", "name"], "type": "object", "additionalProperties": false}, "type": "array"}}, "required": ["interval"], "type": "object", "x-kubernetes-validations": [{"message": "either chart or chartRef must be set", "rule": "(has(self.chart) && !has(self.chartRef)) || (!has(self.chart) && has(self.chartRef))"}], "additionalProperties": false}, "status": {"default": {"observedGeneration": -1}, "description": "HelmReleaseStatus defines the observed state of a HelmRelease.", "properties": {"conditions": {"description": "Conditions holds the conditions for the HelmRelease.", "items": {"description": "Condition contains details for one aspect of the current state of this API Resource.\n---\nThis struct is intended for direct use as an array at the field path .status.conditions.  For example,\n\n\n\ttype FooStatus struct{\n\t    // Represents the observations of a foo's current state.\n\t    // Known .status.conditions.type are: \"Available\", \"Progressing\", and \"Degraded\"\n\t    // +patchMergeKey=type\n\t    // +patchStrategy=merge\n\t    // +listType=map\n\t    // +listMapKey=type\n\t    Conditions []metav1.Condition `json:\"conditions,omitempty\" patchStrategy:\"merge\" patchMergeKey:\"type\" protobuf:\"bytes,1,rep,name=conditions\"`\n\n\n\t    // other fields\n\t}", "properties": {"lastTransitionTime": {"description": "lastTransitionTime is the last time the condition transitioned from one status to another.\nThis should be when the underlying condition changed.  If that is not known, then using the time when the API field changed is acceptable.", "format": "date-time", "type": "string"}, "message": {"description": "message is a human readable message indicating details about the transition.\nThis may be an empty string.", "maxLength": 32768, "type": "string"}, "observedGeneration": {"description": "observedGeneration represents the .metadata.generation that the condition was set based upon.\nFor instance, if .metadata.generation is currently 12, but the .status.conditions[x].observedGeneration is 9, the condition is out of date\nwith respect to the current state of the instance.", "format": "int64", "minimum": 0, "type": "integer"}, "reason": {"description": "reason contains a programmatic identifier indicating the reason for the condition's last transition.\nProducers of specific condition types may define expected values and meanings for this field,\nand whether the values are considered a guaranteed API.\nThe value should be a CamelCase string.\nThis field may not be empty.", "maxLength": 1024, "minLength": 1, "pattern": "^[A-Za-z]([A-Za-z0-9_,:]*[A-Za-z0-9_])?$", "type": "string"}, "status": {"description": "status of the condition, one of True, False, Unknown.", "enum": ["True", "False", "Unknown"], "type": "string"}, "type": {"description": "type of condition in CamelCase or in foo.example.com/CamelCase.\n---\nMany .condition.type values are consistent across resources like Available, but because arbitrary conditions can be\nuseful (see .node.status.conditions), the ability to deconflict is important.\nThe regex it matches is (dns1123SubdomainFmt/)?(qualifiedNameFmt)", "maxLength": 316, "pattern": "^([a-z0-9]([-a-z0-9]*[a-z0-9])?(\\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*/)?(([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9])$", "type": "string"}}, "required": ["lastTransitionTime", "message", "reason", "status", "type"], "type": "object", "additionalProperties": false}, "type": "array"}, "failures": {"description": "Failures is the reconciliation failure count against the latest desired\nstate. It is reset after a successful reconciliation.", "format": "int64", "type": "integer"}, "helmChart": {"description": "HelmChart is the namespaced name of the HelmChart resource created by\nthe controller for the HelmRelease.", "type": "string"}, "history": {"description": "History holds the history of Helm releases performed for this HelmRelease\nup to the last successfully completed release.", "items": {"description": "Snapshot captures a point-in-time copy of the status information for a Helm release,\nas managed by the controller.", "properties": {"apiVersion": {"description": "APIVersion is the API version of the Snapshot.\nProvisional: when the calculation method of the Digest field is changed,\nthis field will be used to distinguish between the old and new methods.", "type": "string"}, "appVersion": {"description": "AppVersion is the chart app version of the release object in storage.", "type": "string"}, "chartName": {"description": "ChartName is the chart name of the release object in storage.", "type": "string"}, "chartVersion": {"description": "ChartVersion is the chart version of the release object in\nstorage.", "type": "string"}, "configDigest": {"description": "ConfigDigest is the checksum of the config (better known as\n\"values\") of the release object in storage.\nIt has the format of `<algo>:<checksum>`.", "type": "string"}, "deleted": {"description": "Deleted is when the release was deleted.", "format": "date-time", "type": "string"}, "digest": {"description": "Digest is the checksum of the release object in storage.\nIt has the format of `<algo>:<checksum>`.", "type": "string"}, "firstDeployed": {"description": "FirstDeployed is when the release was first deployed.", "format": "date-time", "type": "string"}, "lastDeployed": {"description": "LastDeployed is when the release was last deployed.", "format": "date-time", "type": "string"}, "name": {"description": "Name is the name of the release.", "type": "string"}, "namespace": {"description": "Namespace is the namespace the release is deployed to.", "type": "string"}, "ociDigest": {"description": "OCIDigest is the digest of the OCI artifact associated with the release.", "type": "string"}, "status": {"description": "Status is the current state of the release.", "type": "string"}, "testHooks": {"additionalProperties": {"description": "TestHookStatus holds the status information for a test hook as observed\nto be run by the controller.", "properties": {"lastCompleted": {"description": "LastCompleted is the time the test hook last completed.", "format": "date-time", "type": "string"}, "lastStarted": {"description": "LastStarted is the time the test hook was last started.", "format": "date-time", "type": "string"}, "phase": {"description": "Phase the test hook was observed to be in.", "type": "string"}}, "type": "object", "additionalProperties": false}, "description": "TestHooks is the list of test hooks for the release as observed to be\nrun by the controller.", "type": "object"}, "version": {"description": "Version is the version of the release object in storage.", "type": "integer"}}, "required": ["chartName", "chartVersion", "configDigest", "digest", "firstDeployed", "lastDeployed", "name", "namespace", "status", "version"], "type": "object", "additionalProperties": false}, "type": "array"}, "installFailures": {"description": "InstallFailures is the install failure count against the latest desired\nstate. It is reset after a successful reconciliation.", "format": "int64", "type": "integer"}, "lastAttemptedConfigDigest": {"description": "LastAttemptedConfigDigest is the digest for the config (better known as\n\"values\") of the last reconciliation attempt.", "type": "string"}, "lastAttemptedGeneration": {"description": "LastAttemptedGeneration is the last generation the controller attempted\nto reconcile.", "format": "int64", "type": "integer"}, "lastAttemptedReleaseAction": {"description": "LastAttemptedReleaseAction is the last release action performed for this\nHelmRelease. It is used to determine the active remediation strategy.", "enum": ["install", "upgrade"], "type": "string"}, "lastAttemptedRevision": {"description": "LastAttemptedRevision is the Source revision of the last reconciliation\nattempt. For OCIRepository  sources, the 12 first characters of the digest are\nappended to the chart version e.g. \"1.2.3+1234567890ab\".", "type": "string"}, "lastAttemptedRevisionDigest": {"description": "LastAttemptedRevisionDigest is the digest of the last reconciliation attempt.\nThis is only set for OCIRepository sources.", "type": "string"}, "lastAttemptedValuesChecksum": {"description": "LastAttemptedValuesChecksum is the SHA1 checksum for the values of the last\nreconciliation attempt.\nDeprecated: Use LastAttemptedConfigDigest instead.", "type": "string"}, "lastHandledForceAt": {"description": "LastHandledForceAt holds the value of the most recent force request\nvalue, so a change of the annotation value can be detected.", "type": "string"}, "lastHandledReconcileAt": {"description": "LastHandledReconcileAt holds the value of the most recent\nreconcile request value, so a change of the annotation value\ncan be detected.", "type": "string"}, "lastHandledResetAt": {"description": "LastHandledResetAt holds the value of the most recent reset request\nvalue, so a change of the annotation value can be detected.", "type": "string"}, "lastReleaseRevision": {"description": "LastReleaseRevision is the revision of the last successful Helm release.\nDeprecated: Use History instead.", "type": "integer"}, "observedGeneration": {"description": "ObservedGeneration is the last observed generation.", "format": "int64", "type": "integer"}, "observedPostRenderersDigest": {"description": "ObservedPostRenderersDigest is the digest for the post-renderers of\nthe last successful reconciliation attempt.", "type": "string"}, "storageNamespace": {"description": "StorageNamespace is the namespace of the Helm release storage for the\ncurrent release.", "maxLength": 63, "minLength": 1, "type": "string"}, "upgradeFailures": {"description": "UpgradeFailures is the upgrade failure count against the latest desired\nstate. It is reset after a successful reconciliation.", "format": "int64", "type": "integer"}}, "type": "object", "additionalProperties": false}}, "type": "object"}