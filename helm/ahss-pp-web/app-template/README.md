# app-template

![Version: 3.2.1](https://img.shields.io/badge/Version-3.2.1-informational?style=flat-square)

A common powered chart template. This can be useful for small projects that don't have their own chart.

## Requirements

Kubernetes: `>=1.22.0-0`

## Dependencies

| Repository | Name | Version |
|------------|------|---------|
| https://bjw-s.github.io/helm-charts | common | 3.2.1 |

## Installing the Chart

```bash
# Add the repository
helm repo add bjw-s https://bjw-s.github.io/helm-charts/

# Install the chart
helm install bjw-s app-template -f values.yaml
```

## Configuration

Read through the [values.yaml](../../library/common/values.yaml) file of the [common library](../../library/common/) chart. It has several commented out suggested values.
The [CI tests](../../library/common-test/ci) contain a number of scenarios that may prove useful as well.

## Upgrade instructions

Upgrade instructions can be found in the [documentation](https://bjw-s.github.io/helm-charts/docs/app-template/#upgrade-instructions).

## Support

- See the [Docs](http://bjw-s.github.io/helm-charts/docs/)
- Open an [issue](https://github.com/bjw-s/helm-charts/issues/new/choose)
- Join the k8s-at-home [Discord](https://discord.gg/k8s-at-home) community

----------------------------------------------
Autogenerated from chart metadata using [helm-docs v1.13.1](https://github.com/norwoodj/helm-docs/releases/v1.13.1)
