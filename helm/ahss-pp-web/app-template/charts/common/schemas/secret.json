{"instance": {"type": "object", "additionalProperties": false, "properties": {"enabled": {"type": "boolean", "default": true}, "includeInChecksum": {"type": "boolean", "default": true}, "annotations": {"$ref": "definitions.json#/annotations"}, "labels": {"$ref": "definitions.json#/labels"}, "nameOverride": {"type": "string"}, "type": {"type": "string"}, "stringData": {"type": "object", "additionalProperties": {"type": "string"}}}, "required": ["stringData"]}}