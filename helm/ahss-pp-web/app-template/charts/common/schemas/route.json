{"instance": {"type": "object", "additionalProperties": false, "properties": {"enabled": {"type": "boolean", "default": true}, "annotations": {"$ref": "definitions.json#/annotations"}, "hostnames": {"type": "array", "items": {"type": "string"}}, "kind": {"type": "string", "enum": ["GRPCRoute", "HTTPRoute", "TCPRoute", "TLSRoute", "UDPRoute"]}, "labels": {"$ref": "definitions.json#/labels"}, "nameOverride": {"type": "string"}, "parentRefs": {"type": "array", "items": {"type": "object", "additionalProperties": false, "properties": {"group": {"type": "string"}, "kind": {"type": "string"}, "namespace": {"type": "string"}, "name": {"type": "string"}, "sectionName": {"type": "string"}}, "required": ["name"]}}, "rules": {"type": "array", "items": {"type": "object", "additionalProperties": false, "properties": {"backendRefs": {"type": "array", "items": {"type": "object", "additionalProperties": false, "properties": {"group": {"type": "string"}, "kind": {"type": "string"}, "namespace": {"type": "string"}, "name": {"type": "string"}, "port": {"type": ["string", "integer"]}, "weight": {"type": "integer"}}}}, "matches": {"type": "array", "items": {"type": "object", "additionalProperties": false, "properties": {"path": {"type": "object", "additionalProperties": false, "properties": {"type": {"type": "string"}, "value": {"type": "string"}}}}}}, "filters": {"type": "array", "items": {}}, "timeouts": {"type": "object"}}}}}}}