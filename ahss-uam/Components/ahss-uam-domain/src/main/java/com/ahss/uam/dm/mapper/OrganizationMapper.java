package com.ahss.uam.dm.mapper;

import com.ahss.uam.db.model.Organization;
import com.ahss.uam.dm.dto.OrganizationDTO;
import org.mapstruct.BeanMapping;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = MappingConstants.ComponentModel.SPRING)
public interface OrganizationMapper {

    Organization toEntity(OrganizationDTO organizationDTO);

    OrganizationDTO toDto(Organization organization);

    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    Organization partialUpdate(OrganizationDTO organizationDTO, @MappingTarget Organization organization);
}
