package com.ahss.uam.dm.mapper;

import com.ahss.uam.db.model.UserEntity;
import com.ahss.uam.dm.dto.EntityDTO;
import com.ahss.uam.dm.dto.UserDetailsDTO;
import java.util.List;
import org.mapstruct.BeanMapping;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = MappingConstants.ComponentModel.SPRING)
public interface UserEntityMapper {

    UserEntity toEntity(EntityDTO entityDTO);

    EntityDTO toDto(UserEntity userEntity);

    List<EntityDTO> toDtos(List<UserEntity> userEntity);

    UserDetailsDTO toDetailDto(UserEntity userEntity);

    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    UserEntity partialUpdate(EntityDTO entityDTO, @MappingTarget UserEntity entity);
}
