package com.ahss.uam.dm.dto;

import com.ahss.uam.db.model.enums.TenantStatus;
import com.ahss.uam.db.model.enums.TenantType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
@Data
public class TenantDTO {

    private Integer tenantId;
    private String name;
    private String tenantCode;
    private TenantType type;
    private TenantStatus tenantStatus;
    private String path;
    private Integer parentTenantId;
}
