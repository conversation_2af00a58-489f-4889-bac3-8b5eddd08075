package com.ahss.uam.dm.dto;

import com.ahss.uam.db.model.enums.UserStatus;
import java.util.List;
import lombok.Data;

@Data
public class UserDetailsDTO {

    private Long entityId;
    private Integer tenantId;
    private String name;
    private Long parentEntityId;
    private UserStatus status;
    private String path;
    private List<ProfileDTO> profiles;
    private List<EntityMappingDTO> entityMappings;
}
