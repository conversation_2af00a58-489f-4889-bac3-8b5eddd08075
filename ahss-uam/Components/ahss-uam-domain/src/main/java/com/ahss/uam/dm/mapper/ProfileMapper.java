package com.ahss.uam.dm.mapper;

import com.ahss.uam.db.model.Profile;
import com.ahss.uam.dm.dto.ProfileDTO;
import org.mapstruct.BeanMapping;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = MappingConstants.ComponentModel.SPRING)
public interface ProfileMapper {

    Profile toEntity(ProfileDTO entityDTO);

    ProfileDTO toDto(Profile userEntity);

    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    Profile partialUpdate(ProfileDTO profileDTO, @MappingTarget Profile profile);
}
