package com.ahss.uam.dm.builder;

import lombok.Builder;
import lombok.Data;
import java.util.List;
import java.util.Map;

@Data
@Builder
public class JwtTokenBuilder {

    @Builder.Default
    private String algo = "HS256";

    @Builder.Default
    private String issuer = "test-issuer";

    @Builder.Default
    private String subject = "test-subject";

    @Builder.Default
    private List<String> audience = List.of("test-audience");

    @Builder.Default
    private String email = "<EMAIL>";

    @Builder.Default
    private String name = "Test User";

    @Builder.Default
    private List<String> memberOf = List.of("test-group");

    @Builder.Default
    private List<String> scopes = List.of("read", "write");

    @Builder.Default
    private long expirationSeconds = 3600;

    @Builder.Default
    private Map<String, Object> additionalClaims = new java.util.HashMap<>();
}
