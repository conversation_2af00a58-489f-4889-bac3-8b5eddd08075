package com.ahss.uam.dm.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
@Builder
@Data
public class StateTokenDTO {

    private String state;
    private String signature;
    private String timestamp;

    /**
     * This is to allow the current test to pass.
     * @param state
     */
    public StateTokenDTO(String state) {
        this.state = state;
    }
}
