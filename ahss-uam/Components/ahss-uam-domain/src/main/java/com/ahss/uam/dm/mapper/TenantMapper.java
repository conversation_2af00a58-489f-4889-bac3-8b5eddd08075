package com.ahss.uam.dm.mapper;

import com.ahss.uam.db.model.Organization;
import com.ahss.uam.db.model.Tenant;
import com.ahss.uam.dm.dto.CreateOrganizationDTO;
import com.ahss.uam.dm.dto.CreateTenantDTO;
import com.ahss.uam.dm.dto.TenantDTO;
import org.mapstruct.BeanMapping;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = MappingConstants.ComponentModel.SPRING)
public interface TenantMapper {

    Tenant toEntity(TenantDTO tenantDTO);

    Tenant toEntityFromCreateDTO(CreateTenantDTO tenantDTO);

    TenantDTO toDto(Tenant tenant);

    CreateTenantDTO toCreateDto(Tenant tenantDTO);

    Organization toOrganizationEntity(CreateOrganizationDTO organizationDTO);

    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    Tenant partialUpdate(TenantDTO tenantDTO, @MappingTarget Tenant tenant);
}
