package com.ahss.uam.dm.builder;

import lombok.Builder;
import lombok.Data;
import java.util.List;
import java.util.Map;

@Builder
@Data
public class OktaTokenBuilder {

    @Builder.Default
    private String sub = "00ugr4e35mHYJpV6T1d7";

    @Builder.Default
    private String email = "<EMAIL>";

    @Builder.Default
    private String name = "Test User";

    @Builder.Default
    private String givenName = "Test";

    @Builder.Default
    private String familyName = "User";

    @Builder.Default
    private List<String> memberOf = List.of("AHSS_ADMIN", "AHIS_AIA_HK_ADMIN");

    @Builder.Default
    private List<String> scopes = List.of("openid", "profile", "email");

    @Builder.Default
    private long expirationSeconds = 3600;

    @Builder.Default
    private Map<String, Object> additionalClaims = Map.of();
}
