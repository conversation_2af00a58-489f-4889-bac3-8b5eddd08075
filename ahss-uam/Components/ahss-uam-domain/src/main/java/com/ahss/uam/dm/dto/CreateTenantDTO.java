package com.ahss.uam.dm.dto;

import com.ahss.uam.db.model.enums.TenantStatus;
import com.ahss.uam.db.model.enums.TenantType;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
@Setter
@Getter
public class CreateTenantDTO extends TenantDTO {

    @Valid
    private CreateOrganizationDTO organization;
    @NotNull
    private String tenantCode;
    @NotNull
    private TenantType type;
    @NotNull
    private TenantStatus tenantStatus;
    @NotNull
    private String path;

}
