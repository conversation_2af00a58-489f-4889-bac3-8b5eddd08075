package com.ahss.uam.dm.mapper;

import com.ahss.uam.db.model.SsoProvider;
import com.ahss.uam.dm.dto.CreateSsoProviderDTO;
import org.mapstruct.BeanMapping;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = MappingConstants.ComponentModel.SPRING)
public interface SsoProviderMapper {

    SsoProvider toEntity(CreateSsoProviderDTO ssoProviderDTO);

    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    SsoProvider partialUpdate(CreateSsoProviderDTO ssoProviderDTO, @MappingTarget SsoProvider ssoProvider);
}
