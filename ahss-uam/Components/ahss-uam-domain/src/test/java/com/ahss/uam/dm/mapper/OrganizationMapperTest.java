package com.ahss.uam.dm.mapper;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;

import com.ahss.uam.db.model.Organization;
import com.ahss.uam.dm.dto.OrganizationDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@ExtendWith(SpringExtension.class)
@ContextConfiguration(classes = {OrganizationMapperImpl.class})
class OrganizationMapperTest {

    @Autowired
    private OrganizationMapper organizationMapper;

    private Organization organization;
    private OrganizationDTO organizationDTO;

    @BeforeEach
    void setUp() {
        organization = new Organization();
        organization.setOrgId(1);
        organization.setName("Test Organization");
        organization.setCountry("Test Country");

        organizationDTO = new OrganizationDTO();
        organizationDTO.setOrgId(1);
        organizationDTO.setName("Test Organization DTO");
        organizationDTO.setCountry("Test Country DTO");

    }

    @Test
    void testToEntityFromOrganizationDTO() {
        Organization entity = organizationMapper.toEntity(organizationDTO);
        assertNotNull(entity);
        assertEquals(organizationDTO.getOrgId(), entity.getOrgId());
        assertEquals(organizationDTO.getName(), entity.getName());
        assertEquals(organizationDTO.getCountry(), entity.getCountry());

        assertNull(organizationMapper.toEntity(null));
    }

    @Test
    void testToEntityFromOrganizationDTO_WithTenant() {
        Organization entity = organizationMapper.toEntity(organizationDTO);
        assertNotNull(entity);
        assertEquals(organizationDTO.getOrgId(), entity.getOrgId());
        assertEquals(organizationDTO.getName(), entity.getName());
        assertEquals(organizationDTO.getCountry(), entity.getCountry());

        assertNull(organizationMapper.toEntity(null));
    }

    @Test
    void testToDto() {
        OrganizationDTO dto = organizationMapper.toDto(organization);
        assertNotNull(dto);
        assertEquals(organization.getOrgId(), dto.getOrgId());
        assertEquals(organization.getName(), dto.getName());
        assertEquals(organization.getCountry(), dto.getCountry());

        assertNull(organizationMapper.toDto(null));
    }

    @Test
    void testPartialUpdate() {
        Organization updatedOrganization = organizationMapper.partialUpdate(organizationDTO, organization);
        assertNotNull(updatedOrganization);
        assertEquals(organizationDTO.getOrgId(), updatedOrganization.getOrgId());
        assertEquals(organizationDTO.getName(), updatedOrganization.getName());
        assertEquals(organizationDTO.getCountry(), updatedOrganization.getCountry());
    }

    @Test
    void testPartialUpdate_NullValue() {
        organizationDTO = new OrganizationDTO();
        Organization updatedOrganization = organizationMapper.partialUpdate(organizationDTO, organization);
        assertNotNull(updatedOrganization);
        assertEquals("Test Organization", updatedOrganization.getName());
        assertEquals("Test Country", updatedOrganization.getCountry());

        assertNull(organizationMapper.partialUpdate(null, null));

        updatedOrganization = organizationMapper.partialUpdate(null, organization);
        assertNotNull(updatedOrganization);
        assertEquals("Test Organization", updatedOrganization.getName());
        assertEquals("Test Country", updatedOrganization.getCountry());
    }


}
