package com.ahss.uam.dm.mapper;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;

import com.ahss.uam.db.model.Organization;
import com.ahss.uam.db.model.Tenant;
import com.ahss.uam.db.model.enums.TenantStatus;
import com.ahss.uam.db.model.enums.TenantType;
import com.ahss.uam.dm.dto.CreateOrganizationDTO;
import com.ahss.uam.dm.dto.CreateTenantDTO;
import com.ahss.uam.dm.dto.TenantDTO;
import java.time.ZonedDateTime;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@ExtendWith(SpringExtension.class)
@ContextConfiguration(classes = {TenantMapperImpl.class})
class TenantMapperTest {

    @Autowired
    private TenantMapper tenantMapper;

    @Test
    void toEntity() {
        TenantDTO tenantDTO = TenantDTO.builder().tenantId(1).name("AIA Hong Kong").build();
        var result = tenantMapper.toEntity(tenantDTO);
        assertEquals(tenantDTO.getTenantId(), result.getTenantId());
        assertEquals(tenantDTO.getName(), result.getName());
    }

    @Test
    void toDto() {
        Tenant tenant = Tenant.builder().tenantId(1).tenantCode("AIAHK").name("AIA Hong Kong").tenantStatus(TenantStatus.ACTIVE)
                              .createdAt(ZonedDateTime.now()).updatedAt(ZonedDateTime.now()).createdBy("SYSTEM").updatedBy("SYSTEM").build();
        var result = tenantMapper.toDto(tenant);
        assertEquals(tenant.getTenantId(), result.getTenantId());
        assertEquals(tenant.getName(), result.getName());
    }

    @Test
    void partialUpdate() {
        TenantDTO tenantDTO = TenantDTO.builder().tenantId(1).build();
        var result = tenantMapper.toEntity(tenantDTO);
        assertEquals(tenantDTO.getTenantId(), result.getTenantId());
        assertNull(result.getName());
    }

    @Test
    void testToEntityFromTenantDTO() {
        TenantDTO tenantDTO = new TenantDTO();
        tenantDTO.setTenantId(1);
        tenantDTO.setTenantCode("CODE123");
        tenantDTO.setName("Test Tenant");

        Tenant tenant = tenantMapper.toEntity(tenantDTO);

        assertNotNull(tenant);
        assertEquals(tenantDTO.getTenantId(), tenant.getTenantId());
        assertEquals(tenantDTO.getTenantCode(), tenant.getTenantCode());
        assertEquals(tenantDTO.getName(), tenant.getName());

        assertNull(tenantMapper.toEntity((TenantDTO) null));
    }

    @Test
    void testToEntityFromCreateTenantDTO() {
        CreateTenantDTO createTenantDTO = new CreateTenantDTO();
        createTenantDTO.setTenantCode("CODE123");
        createTenantDTO.setName("New Tenant");

        Tenant tenant = tenantMapper.toEntityFromCreateDTO(createTenantDTO);

        assertNotNull(tenant);
        assertEquals(createTenantDTO.getTenantCode(), tenant.getTenantCode());
        assertEquals(createTenantDTO.getName(), tenant.getName());

        assertNull(tenantMapper.toEntityFromCreateDTO(null));
    }

    @Test
    void testToEntityFromCreateTenantDTOWithOrganization() {
        CreateTenantDTO createTenantDTO = new CreateTenantDTO();
        createTenantDTO.setTenantCode("CODE123");
        createTenantDTO.setName("New Tenant");
        CreateOrganizationDTO organizationDTO = new CreateOrganizationDTO();
        organizationDTO.setOrgId(1);
        organizationDTO.setName("Test Organization");
        organizationDTO.setCountry("Test Country");
        createTenantDTO.setOrganization(organizationDTO);
        Tenant tenant = tenantMapper.toEntityFromCreateDTO(createTenantDTO);

        assertNotNull(tenant);
        assertNotNull(tenant.getOrganization());
        assertEquals(createTenantDTO.getTenantCode(), tenant.getTenantCode());
        assertEquals(createTenantDTO.getName(), tenant.getName());

        assertNull(tenantMapper.toEntityFromCreateDTO(null));
    }

    @Test
    void testToDto() {
        Tenant tenant = new Tenant();
        tenant.setTenantId(1);
        tenant.setTenantCode("CODE123");
        tenant.setName("Test Tenant");

        TenantDTO tenantDTO = tenantMapper.toDto(tenant);

        assertNotNull(tenantDTO);
        assertEquals(tenant.getTenantId(), tenantDTO.getTenantId());
        assertEquals(tenant.getTenantCode(), tenantDTO.getTenantCode());
        assertEquals(tenant.getName(), tenantDTO.getName());

        // Test null input
        assertNull(tenantMapper.toDto(null));
    }

    @Test
    void testToCreateDto() {
        Tenant tenant = new Tenant();
        tenant.setTenantCode("CODE123");
        tenant.setName("Test Tenant");

        CreateTenantDTO createTenantDTO = tenantMapper.toCreateDto(tenant);

        assertNotNull(createTenantDTO);
        assertEquals(tenant.getTenantCode(), createTenantDTO.getTenantCode());
        assertEquals(tenant.getName(), createTenantDTO.getName());

        // Test null input
        assertNull(tenantMapper.toCreateDto(null));
    }

    @Test
    void testToCreateDtoWithOrganization() {
        Tenant tenant = new Tenant();
        tenant.setTenantCode("CODE123");
        tenant.setName("Test Tenant");
        tenant.setPath("/test/path");
        Organization organization = new Organization();
        organization.setOrgId(1);
        organization.setName("Test Organization");
        organization.setCountry("Test Country");

        tenant.setOrganization(organization);

        CreateTenantDTO createTenantDTO = tenantMapper.toCreateDto(tenant);

        assertNotNull(createTenantDTO);
        assertNotNull(createTenantDTO.getOrganization());
        assertEquals(tenant.getTenantCode(), createTenantDTO.getTenantCode());
        assertEquals(tenant.getName(), createTenantDTO.getName());

    }

    @Test
    void testPartialUpdate() {
        Tenant tenant = new Tenant();
        tenant.setTenantId(1);
        tenant.setTenantCode("CODE123");
        tenant.setName("Original Tenant");
        tenant.setType(TenantType.BUSINESS_OUT);

        TenantDTO tenantDTO = new TenantDTO();
        tenantDTO.setTenantCode("UPDATED_CODE");
        tenantDTO.setTenantStatus(TenantStatus.ACTIVE);
        tenantDTO.setType(TenantType.BUSINESS_IN);
        tenantDTO.setName("New Name");

        Tenant updatedTenant = tenantMapper.partialUpdate(tenantDTO, tenant);

        assertNotNull(updatedTenant);
        assertEquals(tenant.getTenantId(), updatedTenant.getTenantId());
        assertEquals("UPDATED_CODE", updatedTenant.getTenantCode());
        assertEquals("New Name", updatedTenant.getName());
        assertEquals(TenantType.BUSINESS_IN, updatedTenant.getType());
        assertEquals(TenantStatus.ACTIVE, updatedTenant.getTenantStatus());

        assertNull(tenantMapper.partialUpdate(null, null));

        Tenant result = tenantMapper.partialUpdate(null, tenant);
        assertNotNull(result);
        assertEquals(tenant.getTenantId(), result.getTenantId());

    }
}
