package com.ahss.uam.core.service.impl;

import com.ahss.common.domain.exception.ResourceNotFoundException;
import com.ahss.uam.core.service.UamOrganizationService;
import com.ahss.uam.db.model.Organization;
import com.ahss.uam.db.model.Tenant;
import com.ahss.uam.db.repository.OrganizationRepository;
import com.ahss.uam.db.repository.TenantRepository;
import com.ahss.uam.dm.dto.OrganizationDTO;
import com.ahss.uam.dm.mapper.OrganizationMapper;
import java.util.List;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Data
@Slf4j
public class UamOrganizationServiceImpl implements UamOrganizationService {

    private final OrganizationRepository organizationRepository;
    private final TenantRepository tenantRepository;
    private final OrganizationMapper organizationMapper;

    @Override
    public List<Organization> getAllOrganization() {
        return organizationRepository.findAll();
    }

    @Override
    public Organization updateOrganization(OrganizationDTO organizationDTO) {
        Organization organization = organizationRepository.findByOrgId(organizationDTO.getOrgId()).orElseThrow(ResourceNotFoundException::new);
        organizationMapper.partialUpdate(organizationDTO, organization);
        organizationRepository.save(organization);
        return organization;
    }

    @Override
    public void deleteOrganization(Integer id) {
        Organization organization = organizationRepository.findByOrgId(id).orElseThrow(ResourceNotFoundException::new);
        Tenant tenant = organization.getTenant();
        tenant.setOrganization(null);
        tenantRepository.save(tenant);
        organizationRepository.delete(organization);
    }

    @Override
    public Organization getOrganization(Integer id) {
        return organizationRepository.findByOrgId(id).orElseThrow(ResourceNotFoundException::new);
    }
}
