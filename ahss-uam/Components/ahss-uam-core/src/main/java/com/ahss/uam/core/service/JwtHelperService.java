package com.ahss.uam.core.service;


import com.ahss.common.utils.cryptography.SignerType;
import com.ahss.uam.dm.builder.JwtTokenBuilder;
import com.ahss.uam.dm.builder.OktaTokenBuilder;
import com.nimbusds.jose.jwk.RSAKey;

public interface JwtHelperService {

    String generateToken(JwtTokenBuilder builder, SignerType signerType);

    // New Okta token generation methods
    String generateOktaToken(OktaTokenBuilder builder);

    String getPublicJwk();

    String getSecretKey();

    RSAKey getRsaKey();
}
