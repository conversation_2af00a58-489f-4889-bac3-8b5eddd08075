package com.ahss.uam.core.service;

import com.ahss.uam.db.model.EntityMapping;
import com.ahss.uam.dm.dto.EntityMappingDTO;
import java.util.List;
import org.springframework.stereotype.Service;

@Service
public interface UamEntityMappingService {

    EntityMapping createEntityMapping(Long entityId, EntityMappingDTO entityMapping);

    EntityMapping updateEntityMapping(Long entityId, Long entityMappingId, EntityMappingDTO entityMapping);

    List<EntityMapping> updateEntityMappings(Long entityId, List<EntityMappingDTO> entityMapping);

    void deleteEntityMapping(Long id);

}
