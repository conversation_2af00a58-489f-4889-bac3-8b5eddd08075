package com.ahss.uam.core.service;

import com.ahss.uam.db.model.SsoProvider;
import com.ahss.uam.dm.dto.ClientConfigDTO;
import com.ahss.uam.dm.dto.CreateSsoProviderDTO;
import com.ahss.uam.dm.dto.ServerKeyDTO;
import org.springframework.stereotype.Service;

@Service
public interface UamAdminTokenService {

    ServerKeyDTO createClientConfig(ClientConfigDTO clientConfig);

    ServerKeyDTO updateClientConfig(ClientConfigDTO clientConfig);

    ServerKeyDTO getApiKeyByClientId(String clientId);

    void deleteClientConfig(String clientId);

    String getSecretKeyByApiKey(String apiKey, String token);

    SsoProvider createSsoUrl(CreateSsoProviderDTO ssoProvider);

}
