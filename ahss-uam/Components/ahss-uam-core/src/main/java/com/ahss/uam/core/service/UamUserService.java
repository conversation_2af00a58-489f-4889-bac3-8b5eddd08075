package com.ahss.uam.core.service;

import com.ahss.common.api.PageSupport;
import com.ahss.uam.db.model.UserEntity;
import com.ahss.uam.dm.dto.EntityDTO;
import com.ahss.uam.dm.dto.StateTokenDTO;
import java.security.InvalidAlgorithmParameterException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.security.spec.InvalidKeySpecException;
import java.util.List;
import org.springframework.stereotype.Service;
import javax.crypto.BadPaddingException;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;

@Service
public interface UamUserService {

    List<UserEntity> getAllUsers(Integer page, Integer size);

    PageSupport<UserEntity> searchUsers(String search, Integer tenantId, Integer page, Integer size, String sortBy, String sortDirection);

    UserEntity createUser(EntityDTO userDTO);

    UserEntity updateUser(EntityDTO userDTO);

    void deleteUser(Long id);

    UserEntity getUser(Long id);

    UserEntity createOrUpdateUserByTokenInfo();

    String getAppRedirectUrl(String appId, String redirectUrl);

    String getAccessToken(StateTokenDTO token);

    void revokeAccessToken();
}
