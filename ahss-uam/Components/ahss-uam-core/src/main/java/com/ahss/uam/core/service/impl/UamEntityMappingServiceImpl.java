package com.ahss.uam.core.service.impl;

import com.ahss.common.api.AHSSResponseCode;
import com.ahss.common.domain.exception.BadRequestException;
import com.ahss.common.domain.exception.ResourceNotFoundException;
import com.ahss.uam.core.service.UamEntityMappingService;
import com.ahss.uam.db.model.EntityMapping;
import com.ahss.uam.db.model.UserEntity;
import com.ahss.uam.db.repository.EntityMappingRepository;
import com.ahss.uam.db.repository.UserEntityRepository;
import com.ahss.uam.dm.dto.EntityMappingDTO;
import com.ahss.uam.dm.mapper.EntityMappingMapper;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@RequiredArgsConstructor
@Service
public class UamEntityMappingServiceImpl implements UamEntityMappingService {

    private final EntityMappingRepository entityMappingRepository;
    private final UserEntityRepository userEntityRepository;
    private final EntityMappingMapper entityMappingMapper;

    @Override
    public EntityMapping createEntityMapping(Long entityId, EntityMappingDTO entityMappingDTO) {
        UserEntity entity = userEntityRepository.findById(entityId).orElseThrow(() -> new ResourceNotFoundException(AHSSResponseCode.RC_404_001));
        EntityMapping entityMapping = entityMappingMapper.toEntity(entityMappingDTO);
        entityMapping.setUserEntity(entity);
        return entityMappingRepository.save(entityMapping);
    }

    @Override
    public EntityMapping updateEntityMapping(Long entityId, Long entityMappingId, EntityMappingDTO entityMappingDTO) {
        entityMappingDTO.setEntityMappingsId(entityMappingId);
        EntityMapping entityMapping = entityMappingRepository.findById(entityMappingId).orElseThrow(ResourceNotFoundException::new);
        if (!Objects.equals(entityMapping.getUserEntity().getEntityId(), entityId)) {
            throw new BadRequestException(AHSSResponseCode.RC_400_008);
        }
        entityMapping = entityMappingMapper.partialUpdate(entityMappingDTO, entityMapping);
        return entityMappingRepository.save(entityMapping);
    }

    @Override
    public List<EntityMapping> updateEntityMappings(Long entityId, List<EntityMappingDTO> entityMappingDTOList) {
        UserEntity userEntity = userEntityRepository.findById(entityId).orElseThrow(ResourceNotFoundException::new);
        List<EntityMapping> entityMappings = entityMappingMapper.toEntities(entityMappingDTOList);
        entityMappings.forEach(entityMapping -> entityMapping.setUserEntity(userEntity));
        ArrayList<EntityMapping> entityMappings1 = new ArrayList<>();
        entityMappings1.addAll(entityMappings);
        userEntity.setEntityMappings(entityMappings1);
        userEntityRepository.save(userEntity);
        return entityMappings;
    }

    @Override
    public void deleteEntityMapping(Long id) {
        EntityMapping entityMapping = entityMappingRepository.findById(id)
                                                             .orElseThrow(() -> new ResourceNotFoundException(AHSSResponseCode.RC_404_001));
        entityMappingRepository.delete(entityMapping);
    }

}
