package com.ahss.uam.core.service;

import com.ahss.uam.db.model.Tenant;
import com.ahss.uam.dm.dto.CreateTenantDTO;
import com.ahss.uam.dm.dto.TenantDTO;
import java.util.List;

public interface UamTenantService {

    List<Tenant> getAllTenants();

    Tenant createTenant(CreateTenantDTO tenantDTO);

    Tenant updateTenant(TenantDTO tenantDTO);

    void deleteTenant(Integer id);

    Tenant getTenant(Integer id);
}
