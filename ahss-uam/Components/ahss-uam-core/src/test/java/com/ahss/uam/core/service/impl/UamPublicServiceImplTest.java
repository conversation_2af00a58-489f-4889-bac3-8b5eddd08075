package com.ahss.uam.core.service.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.when;

import com.ahss.common.domain.exception.ResourceNotFoundException;
import com.ahss.uam.core.service.UamPublicService;
import com.ahss.uam.db.model.SsoProvider;
import com.ahss.uam.db.model.enums.TenantStatus;
import com.ahss.uam.db.repository.SsoProviderRepository;
import java.util.Collections;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@ExtendWith(SpringExtension.class)
@ContextConfiguration(classes = {UamPublicServiceImpl.class})
class UamPublicServiceImplTest {

    @MockBean
    private SsoProviderRepository ssoProviderRepository;

    @Autowired
    private UamPublicService uamPublicService;

    @Test
    void getSSOUrl_Success() {
        SsoProvider provider = new SsoProvider();
        provider.setDiscoveryUrl("https://example.com/discovery");
        provider.setClientId("client-id");
        when(ssoProviderRepository.findByClientDomainAndTenant_TenantStatus("example.com", TenantStatus.ACTIVE))
            .thenReturn(Collections.singletonList(provider));

        SsoProvider result = uamPublicService.getSSOUrl("<EMAIL>");

        assertEquals("https://example.com/discovery", result.getDiscoveryUrl());
        assertEquals("client-id", result.getClientId());
    }

    @Test
    void getSSOUrl_ResourceNotFound() {
        when(ssoProviderRepository.findByClientDomainAndTenant_TenantStatus("example1.com", TenantStatus.ACTIVE))
            .thenReturn(Collections.emptyList());

        assertThrows(ResourceNotFoundException.class, () -> uamPublicService.getSSOUrl("<EMAIL>"));
    }

}
