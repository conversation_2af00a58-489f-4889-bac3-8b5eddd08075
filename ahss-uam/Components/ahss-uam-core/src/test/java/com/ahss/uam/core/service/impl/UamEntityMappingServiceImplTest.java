package com.ahss.uam.core.service.impl;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.ahss.common.api.AHSSResponseCode;
import com.ahss.common.domain.exception.ResourceNotFoundException;
import com.ahss.uam.core.service.UamEntityMappingService;
import com.ahss.uam.db.model.EntityMapping;
import com.ahss.uam.db.model.UserEntity;
import com.ahss.uam.db.repository.EntityMappingRepository;
import com.ahss.uam.db.repository.UserEntityRepository;
import com.ahss.uam.dm.dto.EntityMappingDTO;
import com.ahss.uam.dm.mapper.EntityMappingMapper;
import com.ahss.uam.dm.mapper.EntityMappingMapperImpl;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@ExtendWith(SpringExtension.class)
@ContextConfiguration(classes = {UamEntityMappingServiceImpl.class, EntityMappingMapperImpl.class})
class UamEntityMappingServiceImplTest {

    @MockBean
    private EntityMappingRepository entityMappingRepository;

    @MockBean
    private UserEntityRepository userEntityRepository;

    @Autowired
    private EntityMappingMapper entityMappingMapper;

    @Autowired
    private UamEntityMappingService uamEntityMappingService;

    private EntityMappingDTO entityMappingDTO;
    private EntityMapping entityMapping;
    private UserEntity userEntity;

    @BeforeEach
    void setUp() {
        entityMappingDTO = EntityMappingDTO
            .builder()
            .build();
        entityMapping = new EntityMapping();
        userEntity = new UserEntity();
        // set value for useEntity
        ArrayList<EntityMapping> entityMappingArrayList = new ArrayList<>();
        entityMappingArrayList.add(entityMapping);
        userEntity.setEntityMappings(entityMappingArrayList);
    }

    @Test
    void testCreateEntityMapping_Success() {
        when(userEntityRepository.findById(anyLong())).thenReturn(Optional.of(userEntity));
        when(entityMappingRepository.save(any(EntityMapping.class))).thenReturn(entityMapping);

        EntityMapping result = uamEntityMappingService.createEntityMapping(1L, entityMappingDTO);

        assertThat(result).isNotNull();
        verify(userEntityRepository).findById(anyLong());
        verify(entityMappingRepository).save(any(EntityMapping.class));
    }

    @Test
    void testCreateEntityMapping_EntityNotFound() {
        when(userEntityRepository.findById(anyLong())).thenReturn(Optional.empty());

        ResourceNotFoundException exception = assertThrows(ResourceNotFoundException.class,
                                                           () -> uamEntityMappingService.createEntityMapping(1L, entityMappingDTO));
        assertThat(exception.getCode()).isEqualTo(AHSSResponseCode.RC_404_001);

        verify(userEntityRepository).findById(anyLong());
        verify(entityMappingRepository, never()).save(any(EntityMapping.class));
    }

    @Test
    void testUpdateEntityMapping_Success() {
        // Set up the mocks EntityMappingDTO objects and EntityMapping objects
        EntityMappingDTO entityMappingDTO = EntityMappingDTO
            .builder()
            .systemId("systemId")
            .name("name")
            .externalIdentifier("externalIdentifier")
            .build();
        entityMappingDTO.setEntityMappingsId(1L);

        EntityMapping entityMapping = new EntityMapping();
        entityMapping.setEntityMappingsId(1L);
        UserEntity userEntity = new UserEntity();
        userEntity.setEntityId(1L);
        entityMapping.setUserEntity(userEntity);

        when(entityMappingRepository.findById(eq(1L))).thenReturn(Optional.of(entityMapping));
        when(entityMappingRepository.save(any(EntityMapping.class))).thenReturn(entityMapping);

        EntityMapping result = uamEntityMappingService.updateEntityMapping(1L, 1L, entityMappingDTO);

        assertThat(result).isNotNull();
        verify(entityMappingRepository).findById(anyLong());
        verify(entityMappingRepository).save(any(EntityMapping.class));
    }

    @Test
    void testUpdateEntityMapping_EntityMappingNotFound() {
        when(entityMappingRepository.findById(anyLong())).thenReturn(Optional.empty());

        assertThatThrownBy(() -> uamEntityMappingService.updateEntityMapping(1L, 1L, entityMappingDTO))
            .isInstanceOf(ResourceNotFoundException.class);

        verify(entityMappingRepository).findById(anyLong());
        verify(entityMappingRepository, never()).save(any(EntityMapping.class));
    }

    @Test
    void testUpdateEntityMappings_Success() {
        when(userEntityRepository.findById(anyLong())).thenReturn(Optional.of(userEntity));
        when(userEntityRepository.save(any(UserEntity.class))).thenReturn(userEntity);
        List<EntityMapping> result = uamEntityMappingService.updateEntityMappings(1L, List.of(entityMappingDTO));

        assertThat(result).isNotNull();
        verify(userEntityRepository).findById(anyLong());
        verify(userEntityRepository).save(any(UserEntity.class));
    }

    @Test
    void testUpdateEntityMappings_EntityNotFound() {
        when(userEntityRepository.findById(anyLong())).thenReturn(Optional.empty());

        assertThatThrownBy(() -> uamEntityMappingService.updateEntityMappings(1L, List.of(entityMappingDTO)))
            .isInstanceOf(ResourceNotFoundException.class);

        verify(userEntityRepository).findById(anyLong());
        verify(userEntityRepository, never()).save(any(UserEntity.class));
    }

    @Test
    void testDeleteEntityMapping_Success() {
        when(entityMappingRepository.findById(anyLong())).thenReturn(Optional.of(entityMapping));
        doNothing()
            .when(entityMappingRepository)
            .delete(any(EntityMapping.class));
        uamEntityMappingService.deleteEntityMapping(1L);

        verify(entityMappingRepository).findById(anyLong());
        verify(entityMappingRepository).delete(any(EntityMapping.class));
    }
}
