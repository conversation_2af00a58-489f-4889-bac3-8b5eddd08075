package com.ahss.uam.core.service.impl;

import com.ahss.uam.core.service.AESEncryptionService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;

class AESEncryptionServiceImplTest {

    private AESEncryptionService encryptionService;
    private static final String TEST_SECRET_KEY = "ThisIsATestSecretKeyForAES256Encryption!";

    @BeforeEach
    void setUp() {
        encryptionService = new AESEncryptionServiceImpl(TEST_SECRET_KEY);
    }

    @Test
    @DisplayName("Should encrypt and decrypt successfully")
    void encryptAndDecrypt_ValidInput_Success() throws Exception {
        // Given
        String originalText = "This is a test message";

        // When
        String encrypted = encryptionService.encrypt(originalText);
        String decrypted = encryptionService.decrypt(encrypted);

        // Then
        assertNotNull(encrypted);
        assertNotEquals(originalText, encrypted);
        assertEquals(originalText, decrypted);
    }

    @Test
    @DisplayName("Should generate different ciphertexts for same plaintext")
    void encrypt_SamePlaintext_DifferentCiphertexts() throws Exception {
        // Given
        String plaintext = "Same message";

        // When
        String encrypted1 = encryptionService.encrypt(plaintext);
        String encrypted2 = encryptionService.encrypt(plaintext);

        // Then
        assertNotEquals(encrypted1, encrypted2);
    }

    @ParameterizedTest
    @ValueSource(strings = {
        "Short text",
        "Medium length text for testing encryption",
        "A very long text that exceeds the block size multiple times and tests the encryption of longer content"
    })
    @DisplayName("Should handle different input lengths")
    void encryptAndDecrypt_DifferentLengths_Success(String input) throws Exception {
        // When
        String encrypted = encryptionService.encrypt(input);
        String decrypted = encryptionService.decrypt(encrypted);

        // Then
        assertEquals(input, decrypted);
    }

    @ParameterizedTest
    @ValueSource(strings = {"", " ", "   "})
    @DisplayName("Should handle empty and blank inputs")
    void encryptAndDecrypt_EmptyAndBlankInput_Success(String input) throws Exception {
        // When
        String encrypted = encryptionService.encrypt(input);
        String decrypted = encryptionService.decrypt(encrypted);

        // Then
        assertEquals(input, decrypted);
    }

    @Test
    @DisplayName("Should throw exception for invalid ciphertext")
    void decrypt_InvalidCiphertext_ThrowsException() {
        // Given
        String invalidCiphertext = "InvalidBase64String";

        // When/Then
        assertThrows(Exception.class,
                     () -> encryptionService.decrypt(invalidCiphertext));
    }

    @Test
    @DisplayName("Should throw exception for tampered ciphertext")
    void decrypt_TamperedCiphertext_ThrowsException() throws Exception {
        // Given
        String originalText = "Original message";
        String encrypted = encryptionService.encrypt(originalText);
        String tamperedCiphertext = encrypted.substring(0, encrypted.length() - 1) + "X";

        // When/Then
        assertThrows(Exception.class,
                     () -> encryptionService.decrypt(tamperedCiphertext));
    }

    @Test
    @DisplayName("Should handle special characters")
    void encryptAndDecrypt_SpecialCharacters_Success() throws Exception {
        // Given
        String specialChars = "!@#$%^&*()_+-=[]{}|;:,.<>?/~`'\"\\";

        // When
        String encrypted = encryptionService.encrypt(specialChars);
        String decrypted = encryptionService.decrypt(encrypted);

        // Then
        assertEquals(specialChars, decrypted);
    }

    @Test
    @DisplayName("Should handle Unicode characters")
    void encryptAndDecrypt_UnicodeCharacters_Success() throws Exception {
        // Given
        String unicodeText = "Hello 世界 Привет मिर";

        // When
        String encrypted = encryptionService.encrypt(unicodeText);
        String decrypted = encryptionService.decrypt(encrypted);

        // Then
        assertEquals(unicodeText, decrypted);
    }

    @Test
    @DisplayName("Should throw exception for null secret key")
    void constructor_NullSecretKey_ThrowsException() {
        // When/Then
        assertThrows(IllegalArgumentException.class,
                     () -> new AESEncryptionServiceImpl(null));
    }
}
