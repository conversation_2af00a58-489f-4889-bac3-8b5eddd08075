package com.ahss.uam.core.service.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.ahss.common.domain.exception.ResourceNotFoundException;
import com.ahss.uam.core.service.UamOrganizationService;
import com.ahss.uam.db.model.Organization;
import com.ahss.uam.db.model.Tenant;
import com.ahss.uam.db.repository.OrganizationRepository;
import com.ahss.uam.db.repository.TenantRepository;
import com.ahss.uam.dm.dto.OrganizationDTO;
import com.ahss.uam.dm.mapper.OrganizationMapper;
import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@ExtendWith(SpringExtension.class)
@ContextConfiguration(classes = {UamOrganizationServiceImpl.class})
public class UamOrganizationServiceImplTest {

    @MockBean
    private OrganizationRepository organizationRepository;

    @MockBean
    private TenantRepository tenantRepository;

    @MockBean
    private OrganizationMapper organizationMapper;

    @Autowired
    private UamOrganizationService uamOrganizationService;

    @Test
    void getAllOrganization_Success() {
        Organization organization = new Organization();
        when(organizationRepository.findAll()).thenReturn(List.of(organization));

        List<Organization> result = uamOrganizationService.getAllOrganization();

        assertNotNull(result);
        assertEquals(1, result.size());
        verify(organizationRepository, times(1)).findAll();
    }

    @Test
    void updateOrganization_Success() {
        OrganizationDTO organizationDTO = new OrganizationDTO();
        organizationDTO.setOrgId(1);

        Organization organization = new Organization();
        organization.setOrgId(1);
        when(organizationRepository.findByOrgId(eq(organizationDTO.getOrgId())))
            .thenReturn(Optional.of(organization));

        uamOrganizationService.updateOrganization(organizationDTO);

        verify(organizationRepository, times(1)).save(organization);
    }

    @Test
    void deleteOrganization_Success() {
        Integer orgId = 1;
        Organization organization = new Organization();
        Tenant tenant = new Tenant();
        organization.setTenant(tenant);
        when(organizationRepository.findByOrgId(orgId)).thenReturn(Optional.of(organization));
        when(tenantRepository.save(any())).thenReturn(tenant);

        uamOrganizationService.deleteOrganization(orgId);

        verify(organizationRepository, times(1)).delete(organization);
    }

    @Test
    void deleteOrganization_ThrowsResourceNotFoundException() {
        Integer orgId = 1;
        when(organizationRepository.findByOrgId(orgId)).thenReturn(Optional.empty());

        assertThrows(ResourceNotFoundException.class, () -> {
            uamOrganizationService.deleteOrganization(orgId);
        });
    }

    @Test
    void getOrganization_Success() {
        Integer orgId = 1;
        Organization organization = new Organization();
        when(organizationRepository.findByOrgId(orgId)).thenReturn(Optional.of(organization));

        Organization result = uamOrganizationService.getOrganization(orgId);

        assertNotNull(result);
        verify(organizationRepository, times(1)).findByOrgId(orgId);
    }

    @Test
    void getOrganization_ThrowsResourceNotFoundException() {
        Integer orgId = 1;
        when(organizationRepository.findByOrgId(orgId)).thenReturn(Optional.empty());

        assertThrows(ResourceNotFoundException.class, () -> {
            uamOrganizationService.getOrganization(orgId);
        });
    }
}
