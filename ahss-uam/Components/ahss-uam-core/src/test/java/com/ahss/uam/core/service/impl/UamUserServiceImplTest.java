package com.ahss.uam.core.service.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.ahss.common.api.AHSSResponseCode;
import com.ahss.common.domain.context.UserContext;
import com.ahss.common.domain.context.UserContextHolder;
import com.ahss.common.domain.exception.AccessDeniedException;
import com.ahss.common.domain.exception.ResourceNotFoundException;
import com.ahss.uam.core.client.OktaAuthorizationServerClient;
import com.ahss.uam.core.client.ProductPortalClient;
import com.ahss.uam.core.service.hmac.HMACService;
import com.ahss.uam.core.service.hmac.HMACServiceFactory;
import com.ahss.uam.db.model.EntityMapping;
import com.ahss.uam.db.model.SsoProvider;
import com.ahss.uam.db.model.StateCache;
import com.ahss.uam.db.model.Tenant;
import com.ahss.uam.db.model.TenantConfigStatus;
import com.ahss.uam.db.model.UserEntity;
import com.ahss.uam.db.model.enums.TenantStatus;
import com.ahss.uam.db.model.enums.UserNameType;
import com.ahss.uam.db.model.enums.UserStatus;
import com.ahss.uam.db.repository.EntityMappingRepository;
import com.ahss.uam.db.repository.SsoProviderRepository;
import com.ahss.uam.db.repository.StateCacheRepository;
import com.ahss.uam.db.repository.TenantConfigRepository;
import com.ahss.uam.db.repository.TenantRepository;
import com.ahss.uam.db.repository.UserEntityRepository;
import com.ahss.uam.dm.dto.EntityDTO;
import com.ahss.uam.dm.dto.StateTokenDTO;
import com.ahss.uam.dm.dto.product.ProductConfigDTO;
import com.ahss.uam.dm.dto.product.ProductDTO;
import com.ahss.uam.dm.dto.product.enums.ProductConfigType;
import com.ahss.uam.dm.mapper.UserEntityMapper;
import com.ahss.uam.dm.mapper.UserEntityMapperImpl;
import java.security.InvalidAlgorithmParameterException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.security.spec.InvalidKeySpecException;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockitoAnnotations;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.util.ReflectionTestUtils;
import javax.crypto.BadPaddingException;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;

@ExtendWith(SpringExtension.class)
@ContextConfiguration(classes = {UamUserServiceImpl.class, UserEntityMapperImpl.class})
class UamUserServiceImplTest {

    @MockBean
    private UserEntityRepository userEntityRepository;

    @MockBean
    private TenantRepository tenantRepository;

    @MockBean
    private TenantConfigRepository tenantConfigRepository;

    @MockBean
    private EntityMappingRepository entityMappingRepository;

    @Autowired
    private UserEntityMapper userEntityMapper;

    @MockBean
    private StateCacheRepository stateCacheRepository;

    @Autowired
    private UamUserServiceImpl uamUserService;

    @MockBean
    private ProductPortalClient productPortalClient;

    @MockBean
    private HMACServiceFactory hmacServiceFactory;

    @MockBean
    private SsoProviderRepository ssoProviderRepository;

    @MockBean
    private OktaAuthorizationServerClient oktaAuthorizationServerClient;

    @MockBean
    private AESEncryptionServiceImpl aesEncryptionService;

    private UserContext userContext;

    private static final String CLIENT_ID = "clientId";

    @BeforeEach
    void setUp() {
        MockitoAnnotations.initMocks(this);
        userContext = UserContext
            .builder()
            .sub("user123")
            .build();
        userContext.setAccessToken("access-token");
        UserContextHolder.setUserContext(userContext);
        ReflectionTestUtils.setField(uamUserService, "oktaApiGatewayClientId", CLIENT_ID);
    }

    @AfterEach
    void tearDown() {
        UserContextHolder.clear();
    }

    @Test
    void getAllUsers() {
        // Given
        List<UserEntity> users = List.of(new UserEntity());
        when(userEntityRepository.findAll()).thenReturn(users);

        // When
        List<UserEntity> result = uamUserService.getAllUsers(1, 1);

        // Then
        assertEquals(users, result);
        verify(userEntityRepository, times(1)).findAll();
    }

    @Test
    void createUser() {
        EntityDTO userDTO = new EntityDTO();
        userDTO.setTenantId(1);
        UserEntity userEntity = new UserEntity();
        when(tenantRepository.findByTenantId(eq(1))).thenReturn(Optional.of(new Tenant()));
        when(userEntityRepository.save(any(UserEntity.class))).thenReturn(userEntity);

        UserEntity result = uamUserService.createUser(userDTO);

        assertEquals(userEntity, result);
        verify(tenantRepository, times(1)).findByTenantId(eq(1));
        verify(userEntityRepository, times(1)).save(any(UserEntity.class));
    }

    @Test
    void createUser_ThrowsResourceNotFoundException() {
        EntityDTO userDTO = new EntityDTO();
        userDTO.setTenantId(1);
        when(tenantRepository.findByTenantId(eq(1))).thenReturn(Optional.empty());

        ResourceNotFoundException exception = assertThrows(ResourceNotFoundException.class, () -> {
            uamUserService.createUser(userDTO);
        });
        assertEquals(AHSSResponseCode.RC_404_003, exception.getCode());
        verify(tenantRepository, times(1)).findByTenantId(eq(1));
    }

    @Test
    void updateUser() {
        EntityDTO userDTO = new EntityDTO();
        userDTO.setEntityId(1L);
        userDTO.setTenantId(1);
        UserEntity userEntity = new UserEntity();
        when(userEntityRepository.findById(eq(1L))).thenReturn(Optional.of(userEntity));
        when(tenantRepository.findByTenantId(eq(1))).thenReturn(Optional.of(new Tenant()));
        when(userEntityRepository.save(any(UserEntity.class))).thenReturn(userEntity);

        UserEntity result = uamUserService.updateUser(userDTO);

        assertEquals(userEntity, result);
        verify(userEntityRepository, times(1)).findById(eq(1L));
        verify(tenantRepository, times(1)).findByTenantId(eq(1));
        verify(userEntityRepository, times(1)).save(any(UserEntity.class));
    }

    @Test
    void updateUser_ThrowsResourceNotFoundException() {
        EntityDTO userDTO = new EntityDTO();
        when(userEntityRepository.findById(eq(1L))).thenReturn(Optional.of(new UserEntity()));

        ResourceNotFoundException exception = assertThrows(ResourceNotFoundException.class, () -> {
            uamUserService.updateUser(userDTO);
        });
        assertEquals(AHSSResponseCode.RC_404_001, exception.getCode());
    }

    @Test
    void deleteUser() {
        UserEntity userEntity = new UserEntity();
        when(userEntityRepository.findById(eq(1L))).thenReturn(Optional.of(userEntity));
        doNothing()
            .when(userEntityRepository)
            .delete(any(UserEntity.class));

        uamUserService.deleteUser(1L);

        verify(userEntityRepository, times(1)).findById(eq(1L));
        verify(userEntityRepository, times(1)).delete(any(UserEntity.class));
    }

    @Test
    void deleteUser_ThrowsResourceNotFoundException() {
        when(userEntityRepository.findById(eq(1L))).thenReturn(Optional.empty());

        ResourceNotFoundException exception = assertThrows(ResourceNotFoundException.class, () -> {
            uamUserService.deleteUser(1L);
        });
        assertEquals(AHSSResponseCode.RC_404_001, exception.getCode());
        verify(userEntityRepository, times(1)).findById(eq(1L));
    }

    @Test
    void getUser() {
        UserEntity userEntity = new UserEntity();
        when(userEntityRepository.findById(eq(1L))).thenReturn(Optional.of(userEntity));

        UserEntity result = uamUserService.getUser(1L);

        assertEquals(userEntity, result);
        verify(userEntityRepository, times(1)).findById(eq(1L));
    }

    @Test
    void getUser_ThrowsResourceNotFoundException() {
        when(userEntityRepository.findById(eq(1L))).thenReturn(Optional.empty());

        ResourceNotFoundException exception = assertThrows(ResourceNotFoundException.class, () -> {
            uamUserService.getUser(1L);
        });
        assertEquals(AHSSResponseCode.RC_404_001, exception.getCode());
        verify(userEntityRepository, times(1)).findById(eq(1L));
    }

    @Test
    void getStateToken_ReturnsExistingToken() throws NoSuchAlgorithmException, InvalidKeyException {

        final String CACHED_STATE = "existing-state";

        StateCache stateCache = new StateCache();
        stateCache.setState(CACHED_STATE);
        stateCache.setAccessToken(userContext.getAccessToken());
        stateCache.setExpiresAt(LocalDateTime
                                    .now()
                                    .plusMinutes(10));

        HMACService hmacService = mock(HMACService.class);
        StateTokenDTO stateTokenDTO = mock(StateTokenDTO.class);

        when(hmacServiceFactory.getHMACService(CLIENT_ID)).thenReturn(hmacService);
        when(hmacService.generateHMAC(CACHED_STATE)).thenReturn(stateTokenDTO);
        when(stateCacheRepository.findFirstByAccessTokenAndExpiresAtGreaterThanAndUsedOrderByExpiresAtDesc(eq(userContext.getAccessToken()),
            any(LocalDateTime.class), eq(false)))
            .thenReturn(Optional.of(stateCache));

        StateTokenDTO result = uamUserService.getStateToken(CLIENT_ID);

        assertEquals(stateTokenDTO, result);
        verify(stateCacheRepository, times(1)).findFirstByAccessTokenAndExpiresAtGreaterThanAndUsedOrderByExpiresAtDesc(eq(userContext.getAccessToken()),
            any(LocalDateTime.class), eq(false));
        verify(stateCacheRepository, never()).save(any(StateCache.class));
    }

    @Test
    void getStateToken_CreatesNewToken() throws NoSuchAlgorithmException, InvalidKeyException {
        String accessToken = "new-access-token";
        HMACService hmacService = mock(HMACService.class);
        StateTokenDTO stateTokenDTO = mock(StateTokenDTO.class);

        when(hmacServiceFactory.getHMACService(CLIENT_ID)).thenReturn(hmacService);
        when(hmacService.generateHMAC(anyString())).thenReturn(stateTokenDTO);
        when(stateCacheRepository.findFirstByAccessTokenAndExpiresAtGreaterThanAndUsedOrderByExpiresAtDesc(eq(accessToken),
            any(LocalDateTime.class),
            eq(false)))
            .thenReturn(Optional.empty());

        StateTokenDTO result = uamUserService.getStateToken(CLIENT_ID);

        assertEquals(stateTokenDTO, result);
        verify(stateCacheRepository, times(1)).save(any(StateCache.class));
    }

    @Test
    void getAccessToken_ReturnsAccessToken() {
        StateTokenDTO tokenDTO = new StateTokenDTO();
        tokenDTO.setState("valid-state");
        StateCache stateCache = new StateCache();
        stateCache.setAccessToken("valid-access-token");

        when(stateCacheRepository.findByStateAndUsedAndExpiresAtGreaterThan(eq("valid-state"),
            eq(false), any())).thenReturn(Optional.of(stateCache));

        String result = uamUserService.getAccessToken(tokenDTO);

        assertEquals("valid-access-token", result);
        verify(stateCacheRepository, times(1)).findByStateAndUsedAndExpiresAtGreaterThan(eq("valid-state"), eq(false), any());
    }

    @Test
    void getAccessToken_ThrowsResourceNotFoundException() {
        StateTokenDTO tokenDTO = new StateTokenDTO();
        tokenDTO.setState("invalid-state");

        when(stateCacheRepository.findByStateAndUsedAndExpiresAtGreaterThan(eq("invalid-state"), eq(false), any())).thenReturn(Optional.empty());

        ResourceNotFoundException exception = assertThrows(ResourceNotFoundException.class, () -> {
            uamUserService.getAccessToken(tokenDTO);
        });

        assertEquals(AHSSResponseCode.RC_404_005, exception.getCode());
        verify(stateCacheRepository, times(1)).findByStateAndUsedAndExpiresAtGreaterThan(eq("invalid-state"), eq(false), any());
    }


    @Test
    void testCreateOrUpdateUser_ExistingEntityMapping() {
        // Arrange
        String email = "<EMAIL>";
        String name = "Test User";
        String sub = "sub-123";
        List<String> memberOf = List.of("TENANT_A");

        Tenant activeTenant = new Tenant();
        when(tenantConfigRepository.findDistinctTenantIdByConfigKeyInAndTenantConfigStatus(memberOf, TenantConfigStatus.ACTIVE))
            .thenReturn(List.of(activeTenant));

        UserEntity existingUserEntity = new UserEntity();
        when(entityMappingRepository.findDistinctFirstBySystemIdAndExternalIdentifier(email, UserNameType.EMAIL.name()))
            .thenReturn(Optional.of(EntityMapping
                                        .builder()
                                        .userEntity(existingUserEntity)
                                        .build()));

        UserContext userContext = UserContext
            .builder()
            .sub(sub)
            .email(email)
            .memberOf(memberOf)
            .name(name)
            .build();

        UserContextHolder.setUserContext(userContext);
        // Act
        UserEntity result = uamUserService.createOrUpdateUserByTokenInfo();

        // Assert
        assertNotNull(result);
        assertEquals(existingUserEntity, result);

        // Verify interactions
        verify(tenantConfigRepository).findDistinctTenantIdByConfigKeyInAndTenantConfigStatus(memberOf, TenantConfigStatus.ACTIVE);
        verify(entityMappingRepository).findDistinctFirstBySystemIdAndExternalIdentifier(email, UserNameType.EMAIL.name());
        verify(userEntityRepository, never()).save(any(UserEntity.class));
    }

    @Test
    void testCreateOrUpdateUser_NewEntityMapping() {
        // Arrange
        String email = "<EMAIL>";
        String name = "New User";
        String sub = "sub-456";
        List<String> memberOf = List.of("TENANT_B");

        Tenant activeTenant = new Tenant();
        when(tenantConfigRepository.findDistinctTenantIdByConfigKeyInAndTenantConfigStatus(memberOf, TenantConfigStatus.ACTIVE))
            .thenReturn(List.of(activeTenant));

        when(entityMappingRepository.findDistinctFirstBySystemIdAndExternalIdentifier(email, UserNameType.EMAIL.name()))
            .thenReturn(Optional.empty());

        UserEntity newUserEntity = UserEntity
            .builder()
            .name(name)
            .status(UserStatus.ACTIVE)
            .build();
        when(userEntityRepository.save(any(UserEntity.class))).thenReturn(newUserEntity);

        UserContext userContext = UserContext
            .builder()
            .sub(sub)
            .email(email)
            .memberOf(memberOf)
            .name(name)
            .build();

        UserContextHolder.setUserContext(userContext);
        // Act
        UserEntity result = uamUserService.createOrUpdateUserByTokenInfo();

        // Assert
        assertNotNull(result);
        assertEquals(name, result.getName());
        assertEquals(UserStatus.ACTIVE, result.getStatus());

        // Verify interactions
        verify(tenantConfigRepository).findDistinctTenantIdByConfigKeyInAndTenantConfigStatus(memberOf, TenantConfigStatus.ACTIVE);
        verify(entityMappingRepository).findDistinctFirstBySystemIdAndExternalIdentifier(email, UserNameType.EMAIL.name());
        verify(userEntityRepository).save(any(UserEntity.class));
    }

    @Test
    void testCreateOrUpdateUser_TenantNotFound_ThrowsAccessDenied() {
        // Arrange
        String email = "<EMAIL>";
        String name = "Test User";
        String sub = "sub-789";
        List<String> memberOf = List.of("TENANT_C");

        when(tenantConfigRepository.findDistinctTenantIdByConfigKeyInAndTenantConfigStatus(memberOf, TenantConfigStatus.ACTIVE))
            .thenReturn(List.of());

        UserContext userContext = UserContext
            .builder()
            .sub(sub)
            .email(email)
            .memberOf(memberOf)
            .name(name)
            .build();

        UserContextHolder.setUserContext(userContext);
        // Act & Assert
        assertThrows(AccessDeniedException.class, () -> uamUserService.createOrUpdateUserByTokenInfo());

        // Verify interactions
        verify(tenantConfigRepository).findDistinctTenantIdByConfigKeyInAndTenantConfigStatus(memberOf, TenantConfigStatus.ACTIVE);
        verify(entityMappingRepository, never()).findDistinctFirstBySystemIdAndExternalIdentifier(anyString(), anyString());
        verify(userEntityRepository, never()).save(any(UserEntity.class));
    }

    @Test
    void getAppRedirectUrl_ReturnsCorrectUrl() throws NoSuchAlgorithmException, InvalidKeyException {
        String appId = "testAppId";
        String redirectUrl = "http://product.url/redirect";
        String productUrl = "http://product.url/";
        HMACService hmacService = mock(HMACService.class);
        StateTokenDTO stateTokenDTO = mock(StateTokenDTO.class);

        when(hmacServiceFactory.getHMACService(appId)).thenReturn(hmacService);
        when(hmacService.generateHMAC(anyString())).thenReturn(stateTokenDTO);
        when(tenantConfigRepository.findDistinctTenantIdByConfigKeyInAndTenantConfigStatus(anyList(), eq(TenantConfigStatus.ACTIVE)))
            .thenReturn(List.of(new Tenant()));
        when(productPortalClient.getProductFromPPService(eq(appId), anyList(), anyString()))
            .thenReturn(ProductDTO
                            .builder()
                            .productConfigs(List.of(ProductConfigDTO
                                                        .builder()
                                                        .configType(ProductConfigType.URL)
                                                        .configValue(productUrl)
                                                        .build()))
                            .build());

        String result = uamUserService.getAppRedirectUrl(appId, redirectUrl);

        String regex = "http://product\\.url/redirect\\?state=[^&]+&timestamp=[^&]+&signature=[^&]+&appId=testAppId";

        // Assert the URL matches the regex pattern
        assertTrue(result.matches(regex), "The URL should match the expected pattern");
    }

    @Test
    void getAppRedirectUrl_ThrowsResourceNotFoundException() throws NoSuchAlgorithmException, InvalidKeyException {
        String appId = "testAppId";
        String redirectUrl = "http://redirect.url";
        String stateToken = "state-token";
        HMACService hmacService = mock(HMACService.class);
        StateTokenDTO stateTokenDTO = mock(StateTokenDTO.class);

        when(hmacServiceFactory.getHMACService(appId)).thenReturn(hmacService);
        when(hmacService.generateHMAC(anyString())).thenReturn(stateTokenDTO);
        when(tenantConfigRepository.findDistinctTenantIdByConfigKeyInAndTenantConfigStatus(anyList(), eq(TenantConfigStatus.ACTIVE)))
            .thenReturn(List.of(new Tenant()));
        when(productPortalClient.getProductFromPPService(eq(appId), anyList(), anyString()))
            .thenReturn(ProductDTO
                            .builder()
                            .productConfigs(List.of())
                            .build());

        assertThrows(ResourceNotFoundException.class, () -> {
            uamUserService.getAppRedirectUrl(appId, redirectUrl);
        });
    }

    @Test
    void revokeAccessToken_ThrowsExceptionWhenAccessTokenIsMissing() {
        // Arrange
        userContext.setAccessToken(null);
        UserContextHolder.setUserContext(userContext);

        // Act and assert
        IllegalStateException exception = assertThrows(IllegalStateException.class, () -> {
            uamUserService.revokeAccessToken();
        });
        assertEquals("Access token is missing", exception.getMessage());
    }

    @Test
    void revokeAccessToken_ThrowsExceptionWhenEmailIsMissing() {
        // Arrange
        userContext.setEmail(null);
        UserContextHolder.setUserContext(userContext);

        // Act and assert
        IllegalStateException exception = assertThrows(IllegalStateException.class, () -> {
            uamUserService.revokeAccessToken();
        });
        assertEquals("Email is missing", exception.getMessage());
    }

    @Test
    void revokeAccessToken_ThrowsExceptionWhenSsoProviderNotFound() {
        // Arrange
        userContext.setEmail("<EMAIL>");
        userContext.setAccessToken("valid-access-token");
        UserContextHolder.setUserContext(userContext);

        when(ssoProviderRepository.findByClientDomainAndTenant_TenantStatus(eq("example.com"), eq(TenantStatus.ACTIVE)))
            .thenReturn(List.of());

        // Act and assert
        IllegalStateException exception = assertThrows(IllegalStateException.class, () -> {
           uamUserService.revokeAccessToken();
        });
        assertEquals("Unable to find SSO information", exception.getMessage());
        verify(ssoProviderRepository, times(1))
            .findByClientDomainAndTenant_TenantStatus(eq("example.com"), eq(TenantStatus.ACTIVE));
        verify(oktaAuthorizationServerClient, never())
            .revokeAccessToken(anyString(), anyString(), anyString(), anyString());
    }

    @Test
    void revokeAccessToken_CallsRevokeAccessTokenWhenValidData()
    throws InvalidAlgorithmParameterException, NoSuchPaddingException, IllegalBlockSizeException, NoSuchAlgorithmException, InvalidKeySpecException,
        BadPaddingException, InvalidKeyException {
        // Arrange
        String accessToken = "valid-access-token";
        String email = "<EMAIL>";
        userContext.setAccessToken(accessToken);
        userContext.setEmail(email);
        UserContextHolder.setUserContext(userContext);

        String decryptedClientSecret = "decrypted-client-secret";
        SsoProvider ssoProvider = SsoProvider
            .builder()
            .clientSecret("client-secret")
            .discoveryUrl("https://okta.example.com")
            .build();

        when(ssoProviderRepository.findByClientDomainAndTenant_TenantStatus(eq("example.com"), eq(TenantStatus.ACTIVE)))
            .thenReturn(List.of(ssoProvider));

        when(aesEncryptionService.decrypt("client-secret")).thenReturn(decryptedClientSecret);

        // Act
        uamUserService.revokeAccessToken();

        // Assert
        verify(ssoProviderRepository, times(1))
            .findByClientDomainAndTenant_TenantStatus(eq("example.com"), eq(TenantStatus.ACTIVE));
        verify(oktaAuthorizationServerClient, times(1))
            .revokeAccessToken(eq(accessToken), eq(CLIENT_ID), eq(decryptedClientSecret), eq("https://okta.example.com"));
    }
}
