package com.ahss.uam.core.service.hmac.impl;

import com.ahss.uam.core.service.hmac.HMACService;
import com.ahss.uam.core.service.hmac.HMACServiceProvider;
import com.ahss.uam.db.model.ClientConfig;
import com.ahss.uam.db.repository.ClientConfigRepository;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class HMACServiceFactoryImplTest {

    @Mock
    private ClientConfigRepository clientConfigRepository;

    @Mock
    private HMACServiceProvider hmacServiceProvider;

    @Mock
    private HMACService hmacService;

    @InjectMocks
    private HMACServiceFactoryImpl hmacServiceFactory;

    private static final String SHARED_KEY = "sharedKey";
    private static final String VALID_CLIENT_ID = "clientId";
    private static final String INVALID_CLIENT_ID = "invalidClientId";

    @Test
    void getHMACService_ValidClientId_Success() {
        ClientConfig clientConfig = ClientConfig.builder().clientSharedKey(SHARED_KEY).build();

        when(clientConfigRepository.findByClientId(VALID_CLIENT_ID)).thenReturn(Optional.of(clientConfig));
        when(hmacServiceProvider.createHMACService(SHARED_KEY, VALID_CLIENT_ID)).thenReturn(hmacService);

        HMACService result = hmacServiceFactory.getHMACService(VALID_CLIENT_ID);

        assertEquals(hmacService, result);
        verify(clientConfigRepository, times(1)).findByClientId(VALID_CLIENT_ID);
        verify(hmacServiceProvider, times(1)).createHMACService(SHARED_KEY, VALID_CLIENT_ID);
    }

    @Test
    void getHMACService_InvalidClientId_ThrowsException() {
        when(clientConfigRepository.findByClientId(INVALID_CLIENT_ID)).thenReturn(Optional.empty());

        assertThrows(IllegalArgumentException.class, () -> hmacServiceFactory.getHMACService(INVALID_CLIENT_ID));
        verify(clientConfigRepository, times(1)).findByClientId(INVALID_CLIENT_ID);
        verify(hmacServiceProvider, never()).createHMACService(anyString(), anyString());
    }
}
