package com.ahss.uam.core.service.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.ahss.common.api.AHSSResponseCode;
import com.ahss.common.domain.exception.BadRequestException;
import com.ahss.common.domain.exception.ResourceNotFoundException;
import com.ahss.uam.core.service.UamTenantService;
import com.ahss.uam.db.model.Organization;
import com.ahss.uam.db.model.Tenant;
import com.ahss.uam.db.model.enums.TenantStatus;
import com.ahss.uam.db.model.enums.TenantType;
import com.ahss.uam.db.repository.OrganizationRepository;
import com.ahss.uam.db.repository.TenantRepository;
import com.ahss.uam.dm.dto.CreateOrganizationDTO;
import com.ahss.uam.dm.dto.CreateTenantDTO;
import com.ahss.uam.dm.dto.OrganizationDTO;
import com.ahss.uam.dm.dto.TenantDTO;
import com.ahss.uam.dm.mapper.TenantMapper;
import com.ahss.uam.dm.mapper.TenantMapperImpl;
import java.time.ZonedDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@ExtendWith(SpringExtension.class)
@ContextConfiguration(classes = {UamTenantServiceImpl.class, TenantMapperImpl.class})
class UamTenantServiceImplTest {

    @MockBean
    private TenantRepository tenantRepository;

    @Autowired
    private UamTenantService uamTenantService;

    @MockBean
    private OrganizationRepository organizationRepository;

    @Autowired
    private TenantMapper tenantMapper;

    private CreateTenantDTO createTenantDTO;
    private TenantDTO tenantDTO;
    private Tenant tenant;
    private OrganizationDTO organizationDTO;
    private Organization organization;


    @BeforeEach
    void setUp() {

        tenant = new Tenant();
        tenant.setTenantId(1);
        tenant.setTenantCode("CODE");

        CreateOrganizationDTO organizationDTO = new CreateOrganizationDTO();

        organization = new Organization();

        createTenantDTO = new CreateTenantDTO();
        createTenantDTO.setTenantCode("CODE");
        createTenantDTO.setType(TenantType.INDIVIDUAL);
        createTenantDTO.setOrganization(organizationDTO);

        tenantDTO = new TenantDTO();
        tenantDTO.setTenantId(1);
        tenantDTO.setTenantCode("CODE");
    }

    @org.junit.jupiter.api.Test
    void getAllTenants() {
        when(tenantRepository.findAll()).thenReturn(List.of(
            Tenant.builder().tenantId(1).tenantCode("AIAHK").name("AIA Hong Kong").tenantStatus(TenantStatus.ACTIVE).createdAt(ZonedDateTime.now())
                  .updatedAt(ZonedDateTime.now()).createdBy("SYSTEM").updatedBy("SYSTEM").build()));

        var result = uamTenantService.getAllTenants();

        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(1, result.getFirst().getTenantId());
        assertEquals("AIAHK", result.getFirst().getTenantCode());
        assertEquals("AIA Hong Kong", result.getFirst().getName());
    }


    @Test
    void testGetAllTenants() {
        when(tenantRepository.findAll()).thenReturn(Collections.singletonList(tenant));

        List<Tenant> tenants = uamTenantService.getAllTenants();

        assertNotNull(tenants);
        assertEquals(1, tenants.size());
        verify(tenantRepository, times(1)).findAll();
    }

    @Test
    void testCreateTenant_Success() {
        when(organizationRepository.findByOrgId(anyInt())).thenReturn(Optional.of(organization));
        when(tenantRepository.getCountTenantByCodeIncludingSuspended(anyString())).thenReturn(0L);
        when(tenantRepository.save(any(Tenant.class))).thenReturn(tenant);

        Tenant result = uamTenantService.createTenant(createTenantDTO);

        assertNotNull(result);
        assertEquals(tenant.getTenantId(), result.getTenantId());
        verify(tenantRepository, times(1)).save(any());
    }

    @Test
    void testCreateTenant_ThrowsBadRequestException_NoOrganization() {
        createTenantDTO.setType(TenantType.BUSINESS_IN);
        createTenantDTO.setOrganization(null);

        BadRequestException exception = assertThrows(BadRequestException.class, () ->
                                                         uamTenantService.createTenant(createTenantDTO)
                                                    );

        assertEquals(AHSSResponseCode.RC_400_007, exception.getCode());
        verify(tenantRepository, never()).save(any(Tenant.class));
    }

    @Test
    void testCreateTenant_ThrowsResourceNotFoundException_ParentTenantNotFound() {
        createTenantDTO.setParentTenantId(2);
        when(tenantRepository.findByTenantId(eq(createTenantDTO.getParentTenantId()))).thenReturn(Optional.empty());

        assertThrows(ResourceNotFoundException.class, () ->
                         uamTenantService.createTenant(createTenantDTO)
                    );

        verify(tenantRepository, never()).save(any(Tenant.class));
    }

    @Test
    void testCreateTenant_ThrowsBadRequestException_TenantAlreadyExists() {
        when(organizationRepository.findByOrgId(anyInt())).thenReturn(Optional.of(organization));
        when(tenantRepository.getCountTenantByCodeIncludingSuspended(anyString())).thenReturn(1L);

        BadRequestException exception = assertThrows(BadRequestException.class, () ->
                                                         uamTenantService.createTenant(createTenantDTO)
                                                    );

        assertEquals(AHSSResponseCode.RC_400_005, exception.getCode());
        verify(tenantRepository, never()).save(any(Tenant.class));
    }

    @Test
    void testUpdateTenant_Success() {
        when(tenantRepository.findByTenantId(anyInt())).thenReturn(Optional.of(tenant));
        when(tenantRepository.getCountTenantByCodeAndNotIdIncludingSuspended(anyString(), anyInt())).thenReturn(0L);
        when(tenantRepository.save(any())).thenReturn(tenant);

        Tenant result = uamTenantService.updateTenant(tenantDTO);

        assertNotNull(result);
        assertEquals(tenant.getTenantId(), result.getTenantId());
        verify(tenantRepository, times(1)).save(tenant);
    }

    @Test
    void testUpdateTenant_ThrowsResourceNotFoundException() {
        when(tenantRepository.findByTenantId(anyInt())).thenReturn(Optional.empty());

        assertThrows(ResourceNotFoundException.class, () ->
                         uamTenantService.updateTenant(tenantDTO)
                    );

        verify(tenantRepository, never()).save(any(Tenant.class));
    }

    @Test
    void testUpdateTenant_ThrowsBadRequestException_TenantAlreadyExists() {
        when(tenantRepository.findByTenantId(anyInt())).thenReturn(Optional.of(tenant));
        when(tenantRepository.getCountTenantByCodeAndNotIdIncludingSuspended(anyString(), anyInt())).thenReturn(1L);

        BadRequestException exception = assertThrows(BadRequestException.class, () ->
                                                         uamTenantService.updateTenant(tenantDTO)
                                                    );

        assertEquals(AHSSResponseCode.RC_400_005, exception.getCode());
        verify(tenantRepository, never()).save(any(Tenant.class));
    }

    @Test
    void testDeleteTenant_Success() {
        when(tenantRepository.findByTenantId(anyInt())).thenReturn(Optional.of(tenant));

        uamTenantService.deleteTenant(1);

        verify(tenantRepository, times(1)).delete(tenant);
    }

    @Test
    void testDeleteTenant_ThrowsResourceNotFoundException() {
        when(tenantRepository.findByTenantId(anyInt())).thenReturn(Optional.empty());

        assertThrows(ResourceNotFoundException.class, () ->
                         uamTenantService.deleteTenant(1)
                    );

        verify(tenantRepository, never()).delete(any(Tenant.class));
    }

    @Test
    void testGetTenant_Success() {
        when(tenantRepository.findByTenantId(anyInt())).thenReturn(Optional.of(tenant));

        Tenant result = uamTenantService.getTenant(1);

        assertNotNull(result);
        assertEquals(tenant.getTenantId(), result.getTenantId());
        verify(tenantRepository, times(1)).findByTenantId(1);
    }

    @Test
    void testGetTenant_ThrowsResourceNotFoundException() {
        when(tenantRepository.findByTenantId(anyInt())).thenReturn(Optional.empty());

        assertThrows(ResourceNotFoundException.class, () ->
                         uamTenantService.getTenant(1)
                    );

        verify(tenantRepository, times(1)).findByTenantId(1);
    }
}
