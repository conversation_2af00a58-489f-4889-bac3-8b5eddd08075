package com.ahss.uam.core.service.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

import com.ahss.common.domain.exception.ResourceNotFoundException;
import com.ahss.uam.core.service.AESEncryptionService;
import com.ahss.uam.db.model.ClientConfig;
import com.ahss.uam.db.model.SsoProvider;
import com.ahss.uam.db.model.Tenant;
import com.ahss.uam.db.model.enums.ClientConfigStatus;
import com.ahss.uam.db.repository.ClientConfigRepository;
import com.ahss.uam.db.repository.SsoProviderRepository;
import com.ahss.uam.db.repository.TenantRepository;
import com.ahss.uam.dm.dto.ClientConfigDTO;
import com.ahss.uam.dm.dto.CreateSsoProviderDTO;
import com.ahss.uam.dm.dto.ServerKeyDTO;
import com.ahss.uam.dm.mapper.SsoProviderMapper;
import java.util.Optional;
import java.util.UUID;
import lombok.SneakyThrows;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockitoAnnotations;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@ExtendWith(SpringExtension.class)
@ContextConfiguration(classes = {UamAdminTokenServiceImpl.class, AESEncryptionServiceImpl.class})
class UamAdminTokenServiceImplTest {

    @MockBean
    private ClientConfigRepository clientConfigRepository;

    @Autowired
    private UamAdminTokenServiceImpl uamAdminTokenService;

    @Autowired
    private AESEncryptionService aesEncryptionService;

    @MockBean
    private SsoProviderRepository ssoProviderRepository;

    @MockBean
    private TenantRepository tenantRepository;

    @MockBean
    private SsoProviderMapper ssoProviderMapper;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void createClientConfig() throws Exception {
        ClientConfigDTO clientConfigDTO = ClientConfigDTO.builder().build();
        clientConfigDTO.setClientId("test-client");

        when(clientConfigRepository.save(any(ClientConfig.class))).thenAnswer(invocation -> invocation.getArgument(0));

        ServerKeyDTO result = uamAdminTokenService.createClientConfig(clientConfigDTO);

        assertNotNull(result);
        assertNotNull(result.getXAPIKey());
        assertNotNull(result.getToken());
        verify(clientConfigRepository, times(1)).save(any(ClientConfig.class));
    }

    @Test
    void updateClientConfig() throws Exception {
        ClientConfigDTO clientConfigDTO = ClientConfigDTO.builder().build();
        clientConfigDTO.setClientId("test-client");
        clientConfigDTO.setClientSharedKey("test-shared-key");

        ClientConfig existingConfig = new ClientConfig();
        existingConfig.setClientId("test-client");
        existingConfig.setClientSharedKey("test-shared-key");
        existingConfig.setClientSecretKey(aesEncryptionService.encrypt(UUID.randomUUID().toString()));

        when(clientConfigRepository.findByClientIdAndClientSharedKey(anyString(), anyString()))
            .thenReturn(Optional.of(existingConfig));
        when(clientConfigRepository.save(any(ClientConfig.class))).thenAnswer(invocation -> invocation.getArgument(0));

        ServerKeyDTO result = uamAdminTokenService.updateClientConfig(clientConfigDTO);

        assertNotNull(result);
        assertEquals("test-shared-key", result.getXAPIKey());
        assertNotNull(result.getToken());
        verify(clientConfigRepository, times(1)).findByClientIdAndClientSharedKey(anyString(), anyString());
        verify(clientConfigRepository, times(1)).save(any(ClientConfig.class));
    }

    @Test
    void updateClientConfig_NotFound() {
        String apiKey = "test-api-key";
        String secret = "test-secret-key";
        ClientConfigDTO clientConfigDTO = ClientConfigDTO.builder().build();
        clientConfigDTO.setClientId("test-client");
        clientConfigDTO.setClientSharedKey("test-shared-key");

        when(clientConfigRepository.findByClientIdAndClientSharedKey(anyString(), anyString()))
            .thenReturn(Optional.empty());

        assertThrows(ResourceNotFoundException.class, () -> uamAdminTokenService.updateClientConfig(clientConfigDTO));
        verify(clientConfigRepository, times(1)).findByClientIdAndClientSharedKey(anyString(), anyString());
    }

    @Test
    void getClientConfig() {
        String apiKey = "test-api-key";
        String secret = "test-secret-key";
        String token = "token";
        ClientConfig clientConfig = new ClientConfig();
        clientConfig.setClientSharedKey(apiKey);
        try {
            clientConfig.setClientSecretKey(aesEncryptionService.encrypt(secret));
        } catch (Exception e) {
            e.printStackTrace();
        }

        when(clientConfigRepository.findByClientSharedKeyAndClientAccessTokenAndStatus(any(),
                                                                                       any(),
                                                                                       eq(ClientConfigStatus.ACTIVE))).thenReturn(Optional.of(
            clientConfig));

        String result = uamAdminTokenService.getSecretKeyByApiKey(apiKey, token);

        assertNotNull(result);
        assertEquals(secret, result);
        verify(clientConfigRepository, times(1)).findByClientSharedKeyAndClientAccessTokenAndStatus(any(), any(), eq(ClientConfigStatus.ACTIVE));
    }

    @Test
    void getClientConfig_NotFound() {
        String apiKey = "test-api-key";
        String token = "token";

        when(clientConfigRepository.findByClientSharedKeyAndClientAccessTokenAndStatus(any(),
                                                                                       any(),
                                                                                       eq(ClientConfigStatus.ACTIVE))).thenReturn(Optional.empty());

        String result = uamAdminTokenService.getSecretKeyByApiKey(apiKey, token);

        assertNull(result);
        verify(clientConfigRepository, times(1)).findByClientSharedKeyAndClientAccessTokenAndStatus(any(), any(), eq(ClientConfigStatus.ACTIVE));
    }

    @Test
    void getApiKeyByClientId() {
        String clientId = "test-client";
        ClientConfig clientConfig = new ClientConfig();
        clientConfig.setClientId(clientId);
        clientConfig.setClientSharedKey("test-shared-key");
        clientConfig.setClientAccessToken("test-token");

        when(clientConfigRepository.findByClientId(anyString())).thenReturn(Optional.of(clientConfig));

        ServerKeyDTO result = uamAdminTokenService.getApiKeyByClientId(clientId);

        assertNotNull(result);
        assertEquals("test-shared-key", result.getXAPIKey());
        assertEquals("test-token", result.getToken());
        verify(clientConfigRepository, times(1)).findByClientId(anyString());
    }

    @Test
    void deleteClientConfig() {
        String clientId = "test-client";
        ClientConfig clientConfig = new ClientConfig();
        clientConfig.setClientId(clientId);

        when(clientConfigRepository.findByClientId(anyString())).thenReturn(Optional.of(clientConfig));

        uamAdminTokenService.deleteClientConfig(clientId);

        verify(clientConfigRepository, times(1)).findByClientId(anyString());
        verify(clientConfigRepository, times(1)).delete(clientConfig);
    }

    @Test
    void getSecretKeyByApiKey() throws Exception {
        String apiKey = "test-api-key";
        String token = "test-token";
        String secretKey = "test-secret-key-test-secret-key";
        ClientConfig clientConfig = new ClientConfig();
        clientConfig.setClientSharedKey(apiKey);
        clientConfig.setClientAccessToken(token);
        clientConfig.setClientSecretKey(aesEncryptionService.encrypt(secretKey));

        when(clientConfigRepository.findByClientSharedKeyAndClientAccessTokenAndStatus(anyString(), anyString(), any(ClientConfigStatus.class)))
            .thenReturn(Optional.of(clientConfig));

        String result = uamAdminTokenService.getSecretKeyByApiKey(apiKey, token);

        assertNotNull(result);
        assertEquals(secretKey, result);
        verify(clientConfigRepository, times(1)).findByClientSharedKeyAndClientAccessTokenAndStatus(anyString(),
                                                                                                    anyString(),
                                                                                                    any(ClientConfigStatus.class));
    }

    @SneakyThrows
    @Test
    void createSsoUrl() {
        CreateSsoProviderDTO dto = CreateSsoProviderDTO
            .builder()
            .tenantCode("tenant1")
            .clientDomain("Domain")
            .clientId("Client123")
            .name("Name")
            .discoveryUrl("https://sso.url")
            .build();
        Tenant tenant = new Tenant();
        tenant.setTenantCode("tenant1");

        when(tenantRepository.findByTenantCode("tenant1")).thenReturn(Optional.of(tenant));
        when(ssoProviderRepository.findByTenantCode("tenant1")).thenReturn(Optional.empty());
        when(ssoProviderMapper.partialUpdate(any(), any())).thenReturn(new SsoProvider());
        SsoProvider savedSsoProvider = new SsoProvider();
        when(ssoProviderRepository.save(any(SsoProvider.class))).thenReturn(savedSsoProvider);

        SsoProvider result = uamAdminTokenService.createSsoUrl(dto);

        assertNotNull(result);
        verify(tenantRepository).findByTenantCode("tenant1");
        verify(ssoProviderRepository).findByTenantCode("tenant1");
        verify(ssoProviderRepository).save(any(SsoProvider.class));

    }

    @SneakyThrows
    @Test
    void testCreateSsoUrl_ExistingSsoProvider() {
        CreateSsoProviderDTO dto = CreateSsoProviderDTO
            .builder()
            .tenantCode("tenant1")
            .clientDomain("Domain")
            .clientId("Client123")
            .name("Name")
            .discoveryUrl("https://sso.url")
            .build();
        Tenant tenant = new Tenant();
        tenant.setTenantCode("tenant1");

        SsoProvider existingSsoProvider = new SsoProvider();
        when(tenantRepository.findByTenantCode("tenant1")).thenReturn(Optional.of(tenant));
        when(ssoProviderRepository.findByTenantCode("tenant1")).thenReturn(Optional.of(existingSsoProvider));
        when(ssoProviderMapper.partialUpdate(any(), any())).thenReturn(new SsoProvider());
        when(ssoProviderRepository.save(existingSsoProvider)).thenReturn(existingSsoProvider);
        SsoProvider result = uamAdminTokenService.createSsoUrl(dto);

        assertNotNull(result);
        verify(ssoProviderRepository).save(existingSsoProvider);
    }

    @Test
    void testCreateSsoUrl_TenantNotFound() {
        CreateSsoProviderDTO dto = CreateSsoProviderDTO
            .builder()
            .tenantCode("tenant1")
            .clientDomain("Domain")
            .clientId("Client123")
            .name("Name")
            .discoveryUrl("https://sso.url")
            .build();

        when(tenantRepository.findByTenantCode("tenant1")).thenReturn(Optional.empty());
        when(ssoProviderMapper.partialUpdate(any(), any())).thenReturn(new SsoProvider());
        assertThrows(ResourceNotFoundException.class, () -> uamAdminTokenService.createSsoUrl(dto));
        verify(tenantRepository).findByTenantCode("tenant1");
        verifyNoInteractions(ssoProviderRepository);
    }

}
