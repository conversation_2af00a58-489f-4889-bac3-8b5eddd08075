package com.ahss.uam.core.service.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.ahss.common.utils.cryptography.KeyGeneratorUtil;
import com.ahss.common.utils.cryptography.SignerType;
import com.ahss.uam.core.service.JwtHelperService;
import com.ahss.uam.dm.builder.JwtTokenBuilder;
import com.ahss.uam.dm.builder.OktaTokenBuilder;
import com.nimbusds.jose.JOSEException;
import com.nimbusds.jose.JWSAlgorithm;
import com.nimbusds.jose.JWSVerifier;
import com.nimbusds.jose.crypto.MACVerifier;
import com.nimbusds.jose.crypto.RSASSAVerifier;
import com.nimbusds.jwt.SignedJWT;
import java.text.ParseException;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;


@Slf4j
class JwtHelperServiceImplTest {

    private JwtHelperService jwtHelperService;

    @BeforeEach
    void setUp() throws JOSEException {
        jwtHelperService = new JwtHelperServiceImpl(KeyGeneratorUtil.generateRandomPasswordKey(32));
    }

    @Test
    void generateUserToken() throws ParseException, JOSEException {
        // Given
        String expectedIssuer = "https://aiasec.oktapreview.com/oauth2/ausgr4dyrr9dsFkpM1d7";
        String expectedSubject = "00ugr4e35mHYJpV6T1d7";
        String expectedEmail = "<EMAIL>";
        String expectedName = "Dennis Dao";
        List<String> expectedMemberOf = List.of("AHSS_ADMIN", "AHIS_AIA_HK_ADMIN");
        List<String> expectedScopes = List.of("openid", "profile", "email", "uam.user");

        var oktaTokenBuilder = OktaTokenBuilder
            .builder()
            .sub("00ugr4e35mHYJpV6T1d7")
            .email(expectedEmail)
            .name(expectedName)
            .memberOf(expectedMemberOf)
            .scopes(expectedScopes)
            .build();

        // When
        String token = jwtHelperService.generateOktaToken(oktaTokenBuilder);

        // Then
        // Parse the JWT
        SignedJWT signedJWT = SignedJWT.parse(token);

        // Verify the signature
        JWSVerifier verifier = new RSASSAVerifier(jwtHelperService.getRsaKey());
        assertTrue(signedJWT.verify(verifier), "Token signature verification failed");

        // Verify header
        assertEquals(JWSAlgorithm.RS256,
                     signedJWT
                         .getHeader()
                         .getAlgorithm(),
                     "Wrong algorithm");

        // Verify claims
        var claims = signedJWT.getJWTClaimsSet();
        assertEquals(expectedIssuer, claims.getIssuer(), "Wrong issuer");
        assertEquals(expectedSubject, claims.getSubject(), "Wrong subject");
        assertEquals(expectedEmail, claims.getClaim("email"), "Wrong email");
        assertEquals(expectedName, claims.getClaim("name"), "Wrong name");
        assertEquals(expectedMemberOf, claims.getClaim("memberOf"), "Wrong memberOf");
        assertEquals(expectedScopes, claims.getClaim("scp"), "Wrong scopes");
    }

    @Test
    void generateSystemToken() throws ParseException, JOSEException {
        // Given
        String expectedIssuer = "AHPP";
        String expectedSubject = "system";
        String expectedEmail = "<EMAIL>";
        String expectedName = "System";
        List<String> expectedMemberOf = List.of("AHSS_INTERNAL_SYSTEM", "AHIS_SYSTEM");
        List<String> expectedScopes = List.of("AHIS_SYSTEM");

        // When
        var builder = JwtTokenBuilder
            .builder()
            .algo("HS256")
            .issuer("AHPP")
            .subject("system")
            .email("<EMAIL>")
            .name("System")
            .memberOf(List.of("AHSS_INTERNAL_SYSTEM", "AHIS_SYSTEM"))
            .scopes(List.of("AHIS_SYSTEM"))
            .expirationSeconds(31_536_000) // One year
            .build();

        var token = jwtHelperService
            .generateToken(builder, SignerType.SYSTEM);
        log.info("Generated token: {}", token);

        // Then
        // Parse the JWT
        SignedJWT signedJWT = SignedJWT.parse(token);

        // Verify the signature
        MACVerifier verifier = new MACVerifier(jwtHelperService
                                                   .getSecretKey()
                                                   .getBytes());
        assertTrue(signedJWT.verify(verifier), "Token signature verification failed");

        // Verify header
        assertEquals(JWSAlgorithm.HS256,
                     signedJWT
                         .getHeader()
                         .getAlgorithm(),
                     "Wrong algorithm");

        // Verify claims
        var claims = signedJWT.getJWTClaimsSet();
        assertEquals(expectedIssuer, claims.getIssuer(), "Wrong issuer");
        assertEquals(expectedSubject, claims.getSubject(), "Wrong subject");
        assertEquals(expectedEmail, claims.getClaim("email"), "Wrong email");
        assertEquals(expectedName, claims.getClaim("name"), "Wrong name");
        assertEquals(expectedMemberOf, claims.getClaim("memberOf"), "Wrong memberOf");
        assertEquals(expectedScopes, claims.getClaim("scope"), "Wrong scopes");

        // Verify timestamps
        assertNotNull(claims.getIssueTime(), "Issue time should not be null");
        assertNotNull(claims.getExpirationTime(), "Expiration time should not be null");
        assertNotNull(claims.getNotBeforeTime(), "Not before time should not be null");

        // Verify one year expiration (with some tolerance for test execution time)
        long expectedExpiration = 31_536_000L; // One year in seconds
        long actualExpiration = (claims
            .getExpirationTime()
            .getTime() - claims
            .getIssueTime()
            .getTime()) / 1000;
        assertTrue(Math.abs(expectedExpiration - actualExpiration) < 5,
                   "Expiration time should be approximately one year");

        // Verify JWT ID
        assertNotNull(claims.getJWTID(), "JWT ID should not be null");
    }
}
