package com.ahss.uam.core.service.hmac.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.ahss.common.utils.cryptography.KeyGeneratorUtil;
import com.ahss.uam.core.service.hmac.HMACService;
import com.ahss.uam.dm.dto.StateTokenDTO;
import com.ahss.uam.dm.request.StateVerificationRequest;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Base64;
import java.util.UUID;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;

@Slf4j
class HMACServiceImplTest {

    private HMACService hmacService;
    private static final String TEST_SHARED_KEY;
    private final ObjectMapper objectMapper = new ObjectMapper();

    static {
        String testSharedKey1;
        try {
            testSharedKey1 = KeyGeneratorUtil.generateSecretKey().getBase64Encoded();
        } catch (NoSuchAlgorithmException e) {
            testSharedKey1 = KeyGeneratorUtil.generateRandomPasswordKey(32);
        }
        TEST_SHARED_KEY = testSharedKey1;
    }

    @BeforeEach
    void setUp() {
        hmacService = new HMACServiceImpl(TEST_SHARED_KEY);
    }

    @Test
    @DisplayName("Should generate HMAC successfully for valid input")
    void generateHMAC_ValidInput_Success() throws Exception {
        // Given
//        String data = KeyGeneratorUtil.generateRandomPasswordKey(25);
        String data = UUID.randomUUID().toString();

        // When
        StateTokenDTO stateTokenDTO = hmacService.generateHMAC(data);
        log.info(objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(stateTokenDTO));

        // Then
        assertNotNull(stateTokenDTO);
        assertNotNull(stateTokenDTO.getSignature());
        assertTrue(Base64.getDecoder().decode(stateTokenDTO.getSignature()).length > 0);
    }

    @Test
    @DisplayName("Should generate different HMACs for different messages")
    void generateHMAC_DifferentMessages_DifferentHMACs() throws Exception {
        // Given
        String message1 = "Test message 1";
        String message2 = "Test message 2";

        // When
        StateTokenDTO signedStateTokenDTO1 = hmacService.generateHMAC(message1);
        StateTokenDTO signedStateTokenDTO2 = hmacService.generateHMAC(message2);

        // Then
        assertNotEquals(signedStateTokenDTO1.getSignature(), signedStateTokenDTO2.getSignature());
    }

    @Test
    @DisplayName("Should generate same HMAC for same message")
    void generateHMAC_SameMessage_SameHMAC() throws Exception {
        // Given
        String message = "Test message";

        // When
        StateTokenDTO stateTokenDTO1 = hmacService.generateHMAC(message);
        StateTokenDTO stateTokenDTO2 = hmacService.generateHMAC(message);

        // Then
        assertEquals(stateTokenDTO1.getSignature(), stateTokenDTO2.getSignature());
    }

    @ParameterizedTest
    @ValueSource(strings = {"", " ", "   "})
    @DisplayName("Should handle empty and blank inputs")
    void generateHMAC_EmptyAndBlankInput_Success(String input) throws Exception {
        // When
        StateTokenDTO stateTokenDTO = hmacService.generateHMAC(input);

        // Then
        assertNotNull(stateTokenDTO);
        assertNotNull(stateTokenDTO.getSignature());
        assertTrue(Base64.getDecoder().decode(stateTokenDTO.getSignature()).length > 0);
    }

    @Test
    @DisplayName("Should verify valid HMAC successfully")
    void verifyHMAC_ValidHMAC_Success() throws Exception {
        // Given
        String message = "Test message";
        StateTokenDTO stateTokenDTO = hmacService.generateHMAC(message);

        // When
        boolean isValid = hmacService.verifyHMAC(new StateVerificationRequest(message, stateTokenDTO.getSignature(), stateTokenDTO.getTimestamp()));

        // Then
        assertTrue(isValid);
    }

    @Test
    @DisplayName("Should fail verification for invalid HMAC")
    void verifyHMAC_InvalidHMAC_Failure() throws Exception {
        // Given
        String message = "Test message";
        String invalidHmac = Base64.getEncoder().encodeToString("invalid-signature".getBytes());

        // When
        boolean isValid = hmacService.verifyHMAC(new StateVerificationRequest(message, invalidHmac, String.valueOf(Instant.now().getEpochSecond())));

        // Then
        assertFalse(isValid);
    }

    @Test
    @DisplayName("Should fail verification for tampered message")
    void verifyHMAC_TamperedMessage_Failure() throws Exception {
        // Given
        String originalMessage = "Original message";
        StateTokenDTO stateTokenDTO = hmacService.generateHMAC(originalMessage);
        String tamperedMessage = "Tampered message";

        // When
        boolean isValid = hmacService.verifyHMAC(new StateVerificationRequest(tamperedMessage,
                                                                              stateTokenDTO.getSignature(),
                                                                              stateTokenDTO.getTimestamp()));

        // Then
        assertFalse(isValid);
    }

    @Test
    @DisplayName("Should throw InvalidKeyException for null shared key")
    void constructor_NullSharedKey_ThrowsInvalidKeyException() {
        // When/Then
        assertThrows(IllegalArgumentException.class, () -> new HMACServiceImpl(null));
    }

    @Test
    @DisplayName("Should generate and verify valid HMAC with current timestamp")
    void generateAndVerify_ValidTimestamp_Success() throws Exception {
        // Given
        String data = "Test message";

        // When
        StateTokenDTO stateTokenDTO = hmacService.generateHMAC(data);

        // Then
        assertTrue(hmacService.verifyHMAC(new StateVerificationRequest(data, stateTokenDTO.getSignature(), stateTokenDTO.getTimestamp())));
    }

    @Test
    @DisplayName("Should reject expired timestamp")
    void verify_ExpiredTimestamp_Failure() throws Exception {
        // Given
        String data = "Test message";
        String expiredTimestamp = String.valueOf(Instant.now().minus(6, ChronoUnit.MINUTES).getEpochSecond());

        // When
        StateVerificationRequest signedData = new StateVerificationRequest(data, "dummy-signature", expiredTimestamp);

        // Then
        assertFalse(hmacService.verifyHMAC(signedData));
    }

    @Test
    @DisplayName("Should reject future timestamp beyond allowed skew")
    void verify_FutureTimestamp_Failure() throws Exception {
        // Given
        String data = "Test message";
        String futureTimestamp = String.valueOf(Instant.now().plus(2, ChronoUnit.MINUTES).getEpochSecond());

        // When
        StateVerificationRequest signedData = new StateVerificationRequest(data, "dummy-signature", futureTimestamp);

        // Then
        assertFalse(hmacService.verifyHMAC(signedData));
    }

    @Test
    @DisplayName("Should accept timestamp within allowed clock skew")
    void verify_TimestampWithinSkew_Success() throws Exception {
        // Given
        String data = "Test message";

        // Create new signed data with slightly future timestamp
        String futureTimestamp = String.valueOf(Instant.now().plus(30, ChronoUnit.SECONDS).getEpochSecond());

        // Calculate new HMAC with future timestamp
        String messageToSign = data + futureTimestamp;
        String newHmac = calculateTestHMAC(messageToSign);

        StateVerificationRequest signedData = new StateVerificationRequest(data, newHmac, futureTimestamp);

        // Then
        assertTrue(hmacService.verifyHMAC(signedData));
    }

    // Helper method for testing
    private String calculateTestHMAC(String message) throws NoSuchAlgorithmException, InvalidKeyException {
        SecretKeySpec secretKeySpec = new SecretKeySpec(TEST_SHARED_KEY.getBytes(StandardCharsets.UTF_8), "HmacSHA256");

        Mac mac = Mac.getInstance("HmacSHA256");
        mac.init(secretKeySpec);

        byte[] hmacBytes = mac.doFinal(message.getBytes(StandardCharsets.UTF_8));
        return Base64.getEncoder().encodeToString(hmacBytes);
    }
}
