package com.ahss.uam.core.client;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.BDDMockito.given;
import static org.mockito.Mockito.verify;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.http.HttpEntity;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.web.client.RestTemplate;

@ContextConfiguration(classes = {})
public class OktaAuthorizationServerClientTest {

    @Mock
    private RestTemplate restTemplate;

    @InjectMocks
    private OktaAuthorizationServerClient oktaAuthorizationServerClient;

    private static final String OKTA_ISSUER_URL = "https://mock-okta.com";
    private static final String ACCESS_TOKEN = "mockAccessToken";
    private static final String CLIENT_ID = "mockClientId";
    private static final String CLIENT_SECRET = "mockClientSecret";

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testRevokeAccessToken_Success() {
        // Arrange
        String revokeUrl = OKTA_ISSUER_URL + "/v1/revoke";
        given(restTemplate.postForEntity(eq(revokeUrl), any(HttpEntity.class), eq(String.class)))
            .willReturn(ResponseEntity.ok(""));

        // Act
        oktaAuthorizationServerClient.revokeAccessToken(ACCESS_TOKEN, CLIENT_ID, CLIENT_SECRET, OKTA_ISSUER_URL);

        // Assert
        verify(restTemplate).postForEntity(eq(revokeUrl), any(HttpEntity.class), eq(String.class));
    }

    @Test
    void testRevokeAccessToken_Failure() {
        // Arrange
        String revokeUrl = OKTA_ISSUER_URL + "/v1/revoke";

        // Mock failed response from RestTemplate
        given(restTemplate.postForEntity(eq(revokeUrl), any(HttpEntity.class), eq(String.class)))
            .willThrow(new RuntimeException("Mocked exception"));

        // Act & Assert
        assertThrows(RuntimeException.class, () -> {
            oktaAuthorizationServerClient.revokeAccessToken(ACCESS_TOKEN, CLIENT_ID, CLIENT_SECRET, OKTA_ISSUER_URL);
        });
    }
}
