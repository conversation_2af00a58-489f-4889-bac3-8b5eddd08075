package com.ahss.uam.core.client;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrowsExactly;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.BDDMockito.given;

import com.ahss.common.api.AHSSResponseCode;
import com.ahss.common.api.ApiResponse;
import com.ahss.common.api.ResponseStatus;
import com.ahss.uam.dm.dto.product.ProductDTO;
import java.util.Arrays;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.client.RestTemplate;

@ContextConfiguration(classes = {})
public class ProductPortalClientTest {

    @Mock
    private RestTemplate restTemplate;  // Mock the RestTemplate

    @InjectMocks
    private ProductPortalClient productPortalClient;  // Inject the mock into ProductPortalClient

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);  // Initialize mocks
        ReflectionTestUtils.setField(productPortalClient, "productPortalUrl", "http://mock-url/");
    }

    @Test
    public void testGetProductFromPPService() {
        // Arrange: Mocked data and setup
        String appId = "testAppId";
        String token = "test-token";
        String tenantCodesParam = "tenant1,tenant2";
        String productPortalUrl = "http://mock-url/";

        // Mock the product data and response
        ProductDTO mockProduct = new ProductDTO();
        mockProduct.setProductCode("testAppId");
        mockProduct.setProductName("Test Product");

        ApiResponse<ProductDTO, Object> mockApiResponse = new ApiResponse<>();
        mockApiResponse.setData(mockProduct);

        // Mock the RestTemplate behavior
        given(restTemplate.exchange(
            eq(productPortalUrl + "products/{appId}?tenantCodes={tenantCodes}"),
            eq(HttpMethod.GET),
            any(HttpEntity.class),
            any(ParameterizedTypeReference.class),
            eq(appId),
            eq(tenantCodesParam)
                                   )).willReturn(ResponseEntity.ok(mockApiResponse));

        // Act: Call the method
        ProductDTO result = productPortalClient.getProductFromPPService(appId, Arrays.asList("tenant1", "tenant2"), token);

        // Assert: Verify the result
        assertEquals("testAppId", result.getProductCode());
        assertEquals("Test Product", result.getProductName());
    }

    @Test
    public void testGetProductFromPPService_404() {
        // Arrange: Mocked data and setup
        String appId = "testAppId";
        String token = "test";
        String tenantCodesParam = "tenant1,tenant2";
        String productPortalUrl = "http://mock-url/";

        given(restTemplate.exchange(
            eq(productPortalUrl + "products/{appId}?tenantCodes={tenantCodes}"),
            eq(HttpMethod.GET),
            any(HttpEntity.class),
            any(ParameterizedTypeReference.class),
            eq(appId),
            eq(tenantCodesParam)
                                   )).willReturn(ResponseEntity.status(HttpStatus.NOT_FOUND).body(
            ApiResponse.<String, Object>builder()
                       .status(ResponseStatus.FAILED)
                       .responseCode(AHSSResponseCode.RC_404_000)
                       .data(AHSSResponseCode.RC_404_000.getDetailMessage())
                       .build()));

        assertThrowsExactly(RuntimeException.class, () -> {
            productPortalClient.getProductFromPPService(appId, Arrays.asList("tenant1", "tenant2"), token);
        });
    }
}
