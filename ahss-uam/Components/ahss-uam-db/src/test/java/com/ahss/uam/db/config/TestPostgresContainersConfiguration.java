package com.ahss.uam.db.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.testcontainers.service.connection.ServiceConnection;
import org.springframework.context.annotation.Bean;
import org.testcontainers.containers.PostgreSQLContainer;

@TestConfiguration(proxyBeanMethods = false)
public class TestPostgresContainersConfiguration {

    /**
     * Starting with SpringBoot 3.1, we can utilize the @ServiceConnection annotation to eliminate the boilerplate code of defining the dynamic
     * properties.
     */
    @Bean
    @ServiceConnection
    public PostgreSQLContainer<?> postgresContainer(@Value("${spring.testcontainers.postgres.db:dev_ahss}") String dbName,
                                                    @Value("${spring.datasource.username:ahss}") String username,
                                                    @Value("${spring.datasource.password:password}") String password) {
        return new PostgreSQLContainer<>("postgres:15.7")
            .withDatabaseName(dbName)
            .withUsername(username)
            .withPassword(password);
    }
}