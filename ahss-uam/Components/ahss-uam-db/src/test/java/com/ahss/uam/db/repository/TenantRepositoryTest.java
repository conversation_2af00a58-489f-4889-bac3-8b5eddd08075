package com.ahss.uam.db.repository;


import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.ahss.uam.db.AhssUamTestApplication;
import com.ahss.uam.db.config.TestPostgresContainersConfiguration;
import com.ahss.uam.db.model.Tenant;
import com.ahss.uam.db.repository.custom.impl.UserEntityCustomRepositoryImpl;
import java.util.List;
import javax.sql.DataSource;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.boot.test.autoconfigure.orm.jpa.TestEntityManager;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.testcontainers.containers.PostgreSQLContainer;
import org.testcontainers.junit.jupiter.Testcontainers;

@ActiveProfiles(value = {"integration"})
@ExtendWith(SpringExtension.class)
@Testcontainers
@DataJpaTest
@ContextConfiguration(classes = {AhssUamTestApplication.class, TestPostgresContainersConfiguration.class, UserEntityCustomRepositoryImpl.class})
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class TenantRepositoryTest {

    @Autowired
    private PostgreSQLContainer postgreSQLContainer;

    @Autowired
    private TestEntityManager entityManager;

    @Autowired
    private DataSource dataSource;

    @Autowired
    private TenantRepository tenantRepository;

    @BeforeAll
    void beforeAll() {
        postgreSQLContainer.start();
    }

    @Test
    void findAllTenantTest() {
        List<Tenant> tenants = tenantRepository.findAll();
        assertNotNull(tenants);
    }
}
