package com.ahss.uam.db.repository;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.ahss.uam.db.AhssUamTestApplication;
import com.ahss.uam.db.config.TestPostgresContainersConfiguration;
import com.ahss.uam.db.model.Tenant;
import com.ahss.uam.db.model.TenantEntity;
import com.ahss.uam.db.model.UserEntity;
import com.ahss.uam.db.model.enums.TenantStatus;
import com.ahss.uam.db.model.enums.UserStatus;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import javax.sql.DataSource;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.boot.test.autoconfigure.orm.jpa.TestEntityManager;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.testcontainers.containers.PostgreSQLContainer;
import org.testcontainers.junit.jupiter.Testcontainers;

@ActiveProfiles(value = {"integration"})
@ExtendWith(SpringExtension.class)
@Testcontainers
@DataJpaTest
@ContextConfiguration(classes = {AhssUamTestApplication.class, TestPostgresContainersConfiguration.class})
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class UserEntityRepositoryTest {

    @Autowired
    private PostgreSQLContainer postgreSQLContainer;

    @Autowired
    private TestEntityManager entityManager;

    @Autowired
    private DataSource dataSource;

    @Autowired
    private UserEntityRepository userEntityRepository;

    @BeforeAll
    void beforeAll() {
        postgreSQLContainer.start();
    }

    private Tenant getTenant() {
        Tenant tenant = new Tenant();
        tenant.setTenantId(1);
        tenant.setName("Test Tenant");
        tenant.setTenantStatus(TenantStatus.ACTIVE);
        tenant.setTenantCode("test");
        tenant.setCreatedAt(ZonedDateTime.of(2021, 1, 1, 0, 0, 0, 0, ZoneId.systemDefault()));
        tenant.setCreatedBy("system");
        tenant.setUpdatedAt(ZonedDateTime.of(2021, 1, 1, 0, 0, 0, 0, ZoneId.systemDefault()));
        tenant.setUpdatedBy("system");
        return tenant;
    }

    private UserEntity getUserEntity() {
        UserEntity user = new UserEntity();
        user.setEntityId(1L);
        user.setName("Test User");
        user.setStatus(UserStatus.ACTIVE);
        user.setPath("test/domain");
        user.setCreatedAt(ZonedDateTime.of(2021, 1, 1, 0, 0, 0, 0, ZoneId.systemDefault()));
        user.setCreatedBy("system");
        user.setUpdatedAt(ZonedDateTime.of(2021, 1, 1, 0, 0, 0, 0, ZoneId.systemDefault()));
        user.setUpdatedBy("system");
        return user;
    }


    @Test
    void findAllUsersTest() {
        UserEntity user = getUserEntity();
        entityManager.persist(user);
        entityManager.flush();

        List<UserEntity> users = userEntityRepository.findAll();
        assertNotNull(users);
        assertFalse(users.isEmpty());
    }

    @Test
    void findByIdTest() {
        UserEntity user = getUserEntity();
        entityManager.persist(user);
        entityManager.flush();

        Optional<UserEntity> foundUser = userEntityRepository.findById(user.getEntityId());
        assertTrue(foundUser.isPresent());
        assertEquals(user.getName(),
                     foundUser
                         .get()
                         .getName());
    }

    @Test
    void searchUsersTest() {
        UserEntity user = getUserEntity();
        TenantEntity tenantEntity = new TenantEntity();
        tenantEntity.setUserEntity(user);
        tenantEntity.setTenant(getTenant());
        tenantEntity.setCreatedAt(ZonedDateTime.of(2021, 1, 1, 0, 0, 0, 0, ZoneId.systemDefault()));
        tenantEntity.setCreatedBy("system");
        tenantEntity.setUpdatedAt(ZonedDateTime.of(2021, 1, 1, 0, 0, 0, 0, ZoneId.systemDefault()));
        tenantEntity.setUpdatedBy("system");
        user.setTenantEntities(Set.of(tenantEntity));
        entityManager.persist(user);
        entityManager.flush();

        Pageable pageable = PageRequest.of(0, 10);
        List<UserEntity> users = userEntityRepository.searchUsers("Test", 1, 0, 10, "name", "asc");
        assertNotNull(users);
        assertFalse(users.isEmpty());
        assertEquals(user.getName(),
                     users
                         .get(0)
                         .getName());
    }

    @Test
    void countUserTest() {
        UserEntity user = getUserEntity();
        TenantEntity tenantEntity = new TenantEntity();
        tenantEntity.setUserEntity(user);
        tenantEntity.setTenant(getTenant());
        tenantEntity.setCreatedAt(ZonedDateTime.of(2021, 1, 1, 0, 0, 0, 0, ZoneId.systemDefault()));
        tenantEntity.setCreatedBy("system");
        tenantEntity.setUpdatedAt(ZonedDateTime.of(2021, 1, 1, 0, 0, 0, 0, ZoneId.systemDefault()));
        tenantEntity.setUpdatedBy("system");
        user.setTenantEntities(Set.of(tenantEntity));
        entityManager.persist(user);
        entityManager.flush();

        Long count = userEntityRepository.countUser("Test", 1);
        assertNotNull(count);
        assertEquals(1, count);
    }
}
