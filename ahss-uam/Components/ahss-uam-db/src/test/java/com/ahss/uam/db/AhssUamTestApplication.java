package com.ahss.uam.db;

import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.scheduling.annotation.EnableAsync;

@SpringBootApplication
@EnableJpaRepositories(basePackages = "com.ahss.uam.db.repository")
@EntityScan(basePackages = "com.ahss.uam.db.model")
@EnableAsync
public class AhssUamTestApplication {

}
