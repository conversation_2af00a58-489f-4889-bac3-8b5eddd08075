package com.ahss.uam.db.repository;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

import com.ahss.uam.db.AhssUamTestApplication;
import com.ahss.uam.db.config.TestCacheConfig;
import com.ahss.uam.db.config.TestPostgresContainersConfiguration;
import com.ahss.uam.db.model.ClientConfig;
import com.ahss.uam.db.model.enums.ClientConfigStatus;
import java.time.ZonedDateTime;
import java.util.Optional;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestInstance.Lifecycle;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.boot.test.autoconfigure.orm.jpa.TestEntityManager;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.testcontainers.containers.PostgreSQLContainer;
import org.testcontainers.junit.jupiter.Testcontainers;


@ActiveProfiles(value = {"integration"})
@ExtendWith(SpringExtension.class)
@Testcontainers
@DataJpaTest
@ContextConfiguration(classes = {AhssUamTestApplication.class, TestPostgresContainersConfiguration.class})
@TestInstance(Lifecycle.PER_CLASS)
@Import(TestCacheConfig.class)
public class ClientConfigRepositoryTest {

    @Autowired
    private PostgreSQLContainer postgreSQLContainer;

    @Autowired
    private TestEntityManager entityManager;

    @SpyBean
    private ClientConfigRepository clientConfigRepository;

    @Autowired
    private CacheManager cacheManager;

    @BeforeAll
    public void setup() {
        postgreSQLContainer.start();
    }

    private ClientConfig getClientConfig() {
        return ClientConfig
            .builder()
            .clientId("test-client-id")
            .clientSharedKey("test-client-shared-key")
            .clientAccessToken("test-client-access-token")
            .status(ClientConfigStatus.ACTIVE)
            .createdAt(ZonedDateTime.now())
            .createdBy("test-client-created-by")
            .updatedBy("test-client-updated-by")
            .build();
    }

    @Test
    public void whenFindByClientId_thenCacheResult() {
        ClientConfig clientConfig = getClientConfig();
        entityManager.persist(clientConfig);
        entityManager.flush();

        Optional<ClientConfig> clientConfig1 = clientConfigRepository.findByClientId("test-client-id");
        assertThat(clientConfig1).isPresent();

        Cache cache = cacheManager.getCache("clientConfigCache");
        assertThat(cache).isNotNull();
        assertThat(cache.get("test-client-id")).isNotNull();

        Optional<ClientConfig> clientConfig2 = clientConfigRepository.findByClientId("test-client-id");
        assertThat(clientConfig2).isEqualTo(clientConfig1);

        Optional<ClientConfig> clientConfig3 = clientConfigRepository.findByClientId("test-client-id");
        assertThat(clientConfig3).isEqualTo(clientConfig2);

        verify(clientConfigRepository, times(1)).findByClientId("test-client-id");
    }

    @Test
    public void whenFindByClientIdAndClientSharedKey_thenCacheResult() {
        ClientConfig clientConfig = getClientConfig();
        entityManager.persist(clientConfig);
        entityManager.flush();

        Optional<ClientConfig> clientConfig1 = clientConfigRepository.findByClientIdAndClientSharedKey("test-client-id", "test-client-shared-key");
        assertThat(clientConfig1).isPresent();

        String cacheKey = "test-client-id:test-client-shared-key";
        Cache cache = cacheManager.getCache("clientConfigCache");
        assertThat(cache).isNotNull();
        assertThat(cache.get(cacheKey)).isNotNull();

        Optional<ClientConfig> clientConfig2 = clientConfigRepository.findByClientIdAndClientSharedKey("test-client-id", "test-client-shared-key");
        assertThat(clientConfig2).isEqualTo(clientConfig1);

        Optional<ClientConfig> clientConfig3 = clientConfigRepository.findByClientIdAndClientSharedKey("test-client-id", "test-client-shared-key");
        assertThat(clientConfig3).isEqualTo(clientConfig2);

        verify(clientConfigRepository, times(1)).findByClientIdAndClientSharedKey("test-client-id", "test-client-shared-key");
    }

    @Test
    public void whenFindByClientSharedKeyAndClientAccessTokenAndStatus_thenCacheResult() {
        ClientConfig clientConfig = getClientConfig();
        entityManager.persist(clientConfig);
        entityManager.flush();

        Optional<ClientConfig> clientConfig1 = clientConfigRepository.findByClientSharedKeyAndClientAccessTokenAndStatus(
            "test-client-shared-key", "test-client-access-token", ClientConfigStatus.ACTIVE);
        assertThat(clientConfig1).isPresent();

        String cacheKey = "test-client-shared-key:test-client-access-token:ACTIVE";
        Cache cache = cacheManager.getCache("clientConfigCache");
        assertThat(cache).isNotNull();
        assertThat(cache.get(cacheKey)).isNotNull();

        Optional<ClientConfig> clientConfig2 = clientConfigRepository.findByClientSharedKeyAndClientAccessTokenAndStatus(
            "test-client-shared-key", "test-client-access-token", ClientConfigStatus.ACTIVE);
        assertThat(clientConfig2).isEqualTo(clientConfig1);

        Optional<ClientConfig> clientConfig3 = clientConfigRepository.findByClientSharedKeyAndClientAccessTokenAndStatus(
            "test-client-shared-key", "test-client-access-token", ClientConfigStatus.ACTIVE);
        assertThat(clientConfig3).isEqualTo(clientConfig2);

        verify(clientConfigRepository, times(1)).findByClientSharedKeyAndClientAccessTokenAndStatus(
            "test-client-shared-key", "test-client-access-token", ClientConfigStatus.ACTIVE);
    }
}
