CREATE SEQUENCE entity_mappings_id_big_int_seq;
-- <PERSON><PERSON> enum types
CREATE TYPE tenant_type AS ENUM ('BUSINESS_IN', 'BUSINESS_OUT', 'INDIVIDUAL');
CREATE TYPE tenant_status AS ENUM ('ACTIVE', 'INACTIVE', 'SUSPENDED');
CREATE TYPE user_status AS ENUM ('ACTIVE', 'INACTIVE', 'SUSPENDED');
CREATE TYPE ext_int_config_status AS ENUM ('DRAFT', 'ACTIVE', 'INACTIVE');
CREATE TYPE user_name_type AS ENUM ('EMAIL', 'PERSONAL_IDENTITY', 'PHONE_NUMBER', 'CUSTOM');
CREATE TYPE tenant_config_status AS ENUM ('DRAFT', 'ACTIVE', 'INACTIVE');
CREATE TYPE tenant_config_type AS ENUM ('LOCATION_REGION', 'LOCATION_COUNTRY', 'LOCATION_MARKET', 'ID', 'PRODUCT', 'POLICY', 'ENTITY_NO', 'CUSTOM1', 'CUSTOM2', 'CUSTOM3', 'CUSTOM4', 'CUSTOM5', 'ROLE', 'COMPANY');
CREATE TYPE value_format_type AS ENUM ('EMAIL', 'PHONE_NUMBER', 'POSTAL_CODE', 'COUNTRY_CODE_ISO3', 'COUNTRY_CODE_ISO2', 'COUNTRY_CODE_NUMERIC', 'CURRENCY_CODE_ISO3', 'CURRENCY_CODE_NUMERIC', 'URL', 'FREE_TEXT', 'ALPHABETIC', 'ALPHA_NUMERIC', 'ALPHA_NUMERIC_WITH_SPACE', 'NUMERIC', 'DATE', 'DATETIME', 'BOOLEAN');
CREATE TYPE client_config_status AS ENUM ('ACTIVE', 'INACTIVE', 'DRAFT', 'SUSPENDED');

-- Create tables
CREATE TABLE organization
(
    org_id     SERIAL PRIMARY KEY,
    name       TEXT        NOT NULL,
    country    TEXT,
    created_at TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(50) NOT NULL DEFAULT 'system',
    updated_by VARCHAR(50) NOT NULL DEFAULT 'system'
);

CREATE TABLE tenant
(
    tenant_id        SERIAL PRIMARY KEY,
    tenant_code      VARCHAR(20) UNIQUE NOT NULL,
    name             TEXT               NOT NULL,
    parent_tenant_id INTEGER,
    path             TEXT,
    type             tenant_type        NOT NULL,
    organization_id  INTEGER,
    tenant_status    tenant_status      NOT NULL,
    created_at       TIMESTAMP          NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at       TIMESTAMP          NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by       VARCHAR(50)        NOT NULL DEFAULT 'system',
    updated_by       VARCHAR(50)        NOT NULL DEFAULT 'system',
    FOREIGN KEY (parent_tenant_id) REFERENCES tenant (tenant_id),
    FOREIGN KEY (organization_id) REFERENCES organization (org_id) ON DELETE SET NULL
);

CREATE TABLE tenant_config
(
    tenant_config_id         SERIAL PRIMARY KEY,
    tenant_id                INTEGER              NOT NULL,
    config_key               VARCHAR(255),
    tenant_code              VARCHAR(20)          NOT NULL,
    config_type              tenant_config_type   NOT NULL,
    config_value             TEXT                 NOT NULL,
    description              TEXT,
    config_value_format_type value_format_type    NOT NULL,
    regex                    TEXT,
    tenant_config_status     tenant_config_status NOT NULL,
    created_at               TIMESTAMP            NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at               TIMESTAMP            NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by               VARCHAR(50)          NOT NULL DEFAULT 'system',
    updated_by               VARCHAR(50)          NOT NULL DEFAULT 'system',
    FOREIGN KEY (tenant_id) REFERENCES tenant (tenant_id)
);

CREATE TABLE entity
(
    entity_id        BIGSERIAL PRIMARY KEY,
    name             TEXT        NOT NULL,
    parent_entity_id BIGINT,
    status           user_status NOT NULL,
    path             TEXT,
    created_at       TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at       TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by       VARCHAR(50) NOT NULL DEFAULT 'system',
    updated_by       VARCHAR(50) NOT NULL DEFAULT 'system',
    FOREIGN KEY (parent_entity_id) REFERENCES entity (entity_id)
);

CREATE TABLE entity_mappings
(
    entity_mappings_id  BIGSERIAL PRIMARY KEY,
    entity_id           BIGINT      NOT NULL,
    system_id           TEXT        NOT NULL,
    name                TEXT        NOT NULL,
    external_identifier TEXT        NOT NULL,
    created_at          TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at          TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by          VARCHAR(50) NOT NULL DEFAULT 'system',
    updated_by          VARCHAR(50) NOT NULL DEFAULT 'system',
    FOREIGN KEY (entity_id) REFERENCES entity (entity_id),
    CONSTRAINT entity_mappings_unique_system_id_external_identifier UNIQUE (external_identifier, system_id)
);

CREATE TABLE role
(
    role_id    SERIAL PRIMARY KEY,
    name       VARCHAR(255) NOT NULL,
    created_at TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(50)  NOT NULL DEFAULT 'system',
    updated_by VARCHAR(50)  NOT NULL DEFAULT 'system'
);


CREATE TABLE profile
(
    profile_id    SERIAL PRIMARY KEY,
    entity_id     INTEGER        NOT NULL,
    role_id       INTEGER        NOT NULL,
    username      TEXT           NOT NULL,
    username_type user_name_type NOT NULL,
    created_at    TIMESTAMP      NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at    TIMESTAMP      NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by    VARCHAR(50)    NOT NULL DEFAULT 'system',
    updated_by    VARCHAR(50)    NOT NULL DEFAULT 'system',
    FOREIGN KEY (entity_id) REFERENCES entity (entity_id),
    FOREIGN KEY (role_id) REFERENCES role (role_id)
);

CREATE TABLE permission
(
    permission_id SERIAL PRIMARY KEY,
    name          TEXT        NOT NULL,
    description   TEXT,
    created_at    TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at    TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by    VARCHAR(50) NOT NULL DEFAULT 'system',
    updated_by    VARCHAR(50) NOT NULL DEFAULT 'system'
);

CREATE TABLE role_permission
(
    role_id       INTEGER     NOT NULL,
    permission_id INTEGER     NOT NULL,
    created_at    TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at    TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by    VARCHAR(50) NOT NULL DEFAULT 'system',
    updated_by    VARCHAR(50) NOT NULL DEFAULT 'system',
    PRIMARY KEY (role_id, permission_id),
    FOREIGN KEY (role_id) REFERENCES role (role_id),
    FOREIGN KEY (permission_id) REFERENCES permission (permission_id)
);

CREATE TABLE user_group
(
    user_group_id SERIAL PRIMARY KEY,
    name          TEXT        NOT NULL,
    description   TEXT,
    created_at    TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at    TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by    VARCHAR(50) NOT NULL DEFAULT 'system',
    updated_by    VARCHAR(50) NOT NULL DEFAULT 'system'
);

CREATE TABLE user_group_member
(
    user_group_id INTEGER     NOT NULL,
    entity_id     INTEGER     NOT NULL,
    created_at    TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at    TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by    VARCHAR(50) NOT NULL DEFAULT 'system',
    updated_by    VARCHAR(50) NOT NULL DEFAULT 'system',
    PRIMARY KEY (user_group_id, entity_id),
    FOREIGN KEY (user_group_id) REFERENCES user_group (user_group_id),
    FOREIGN KEY (entity_id) REFERENCES entity (entity_id)
);

CREATE TABLE group_role
(
    user_group_id INTEGER     NOT NULL,
    role_id       INTEGER     NOT NULL,
    created_at    TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at    TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by    VARCHAR(50) NOT NULL DEFAULT 'system',
    updated_by    VARCHAR(50) NOT NULL DEFAULT 'system',
    PRIMARY KEY (user_group_id, role_id),
    FOREIGN KEY (user_group_id) REFERENCES user_group (user_group_id),
    FOREIGN KEY (role_id) REFERENCES role (role_id)
);

CREATE TABLE sso_provider
(
    sso_provider_id SERIAL PRIMARY KEY,
    tenant_id       INTEGER            NOT NULL,
    tenant_code     VARCHAR(20) UNIQUE NOT NULL,
    name            TEXT               NOT NULL,
    client_id       TEXT               NOT NULL,
    client_secret   TEXT               NOT NULL,
    discovery_url   TEXT               NOT NULL,
    client_domain   TEXT,
    created_at      TIMESTAMP          NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at      TIMESTAMP          NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by      VARCHAR(50)        NOT NULL DEFAULT 'system',
    updated_by      VARCHAR(50)        NOT NULL DEFAULT 'system',
    FOREIGN KEY (tenant_id) REFERENCES tenant (tenant_id)
);

CREATE TABLE state_cache
(
    state        VARCHAR(255) PRIMARY KEY,
    access_token TEXT,
    used         BOOLEAN   DEFAULT FALSE,
    created_at   TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at   TIMESTAMP
);

CREATE TABLE tenant_entity
(
    tenant_id  INT          NOT NULL,
    entity_id  BIGINT       NOT NULL,
    created_at TIMESTAMP    NOT NULL,
    updated_at TIMESTAMP    NOT NULL,
    created_by VARCHAR(255) NOT NULL,
    updated_by VARCHAR(255) NOT NULL,
    PRIMARY KEY (tenant_id, entity_id),
    FOREIGN KEY (tenant_id) REFERENCES tenant (tenant_id),
    FOREIGN KEY (entity_id) REFERENCES entity (entity_id)
);

CREATE TABLE client_configs
(
    id                  BIGSERIAL PRIMARY KEY,
    client_id           VARCHAR(255) UNIQUE  NOT NULL,
    client_secret_key   VARCHAR(255),
    client_access_token TEXT                 NOT NULL,
    client_shared_key   VARCHAR(255)         NOT NULL,
    status              client_config_status NOT NULL DEFAULT 'DRAFT'::client_config_status,
    created_at          TIMESTAMP            NOT NULL,
    updated_at          TIMESTAMP,
    created_by          VARCHAR(50)          NOT NULL DEFAULT 'system',
    updated_by          VARCHAR(50)          NOT NULL DEFAULT 'system',
    CONSTRAINT uk_client_id UNIQUE (client_shared_key)
);

-- Add indexes for foreign keys and frequently queried columns
CREATE INDEX idx_tenant_tenant_code ON tenant (tenant_code);
CREATE INDEX idx_tenant_config_tenant_id ON tenant_config (tenant_id);
CREATE INDEX idx_tenant_config_tenant_code ON tenant_config (tenant_code);
CREATE INDEX idx_sso_provider_tenant_id ON sso_provider (tenant_id);
CREATE INDEX idx_sso_provider_tenant_code ON sso_provider (tenant_code);
CREATE INDEX idx_entity_mappings_entity_id ON entity_mappings (entity_id);
CREATE INDEX idx_profile_entity_id ON profile (entity_id);
CREATE INDEX idx_profile_role_id ON profile (role_id);
CREATE INDEX idx_role_permission_role_id ON role_permission (role_id);
CREATE INDEX idx_role_permission_permission_id ON role_permission (permission_id);
CREATE INDEX idx_user_group_member_user_group_id ON user_group_member (user_group_id);
CREATE INDEX idx_user_group_member_entity_id ON user_group_member (entity_id);
CREATE INDEX idx_group_role_user_group_id ON group_role (user_group_id);
CREATE INDEX idx_group_role_role_id ON group_role (role_id);
CREATE INDEX idx_expires_at ON state_cache (expires_at);
CREATE INDEX idx_client_configs_status ON client_configs (status);

-- Create a function to clean up expired entries
CREATE OR REPLACE FUNCTION cleanup_expired_state()
    RETURNS void AS
$$
BEGIN
    DELETE FROM state_cache WHERE expires_at < NOW();
END;
$$ LANGUAGE plpgsql;

-- Create a scheduled job to run the cleanup function (runs every hour)
-- CREATE EXTENSION IF NOT EXISTS pg_cron;
-- SELECT cron.schedule('0 * * * *', 'SELECT cleanup_expired_state()');

-- Update id sequence: This is to ensure that the next insert will start from the next available id
SELECT setval(pg_get_serial_sequence('organization', 'org_id'), COALESCE((SELECT MAX(org_id) FROM organization), 1));
SELECT setval(pg_get_serial_sequence('tenant', 'tenant_id'), COALESCE((SELECT MAX(tenant_id) FROM tenant), 1));
SELECT setval(pg_get_serial_sequence('tenant_config', 'tenant_config_id'), COALESCE((SELECT MAX(tenant_config_id) FROM tenant_config), 1));
SELECT setval(pg_get_serial_sequence('entity', 'entity_id'), COALESCE((SELECT MAX(entity_id) FROM entity), 1));
SELECT setval(pg_get_serial_sequence('entity_mappings', 'entity_mappings_id'), COALESCE((SELECT MAX(entity_mappings_id) FROM entity_mappings), 1));
SELECT setval(pg_get_serial_sequence('role', 'role_id'), COALESCE((SELECT MAX(role_id) FROM role), 1));
SELECT setval(pg_get_serial_sequence('profile', 'profile_id'), COALESCE((SELECT MAX(profile_id) FROM profile), 1));
SELECT setval(pg_get_serial_sequence('permission', 'permission_id'), COALESCE((SELECT MAX(permission_id) FROM permission), 1));
SELECT setval(pg_get_serial_sequence('role_permission', 'role_id'), COALESCE((SELECT MAX(role_id) FROM role_permission), 1));
SELECT setval(pg_get_serial_sequence('user_group', 'user_group_id'), COALESCE((SELECT MAX(user_group_id) FROM user_group), 1));
SELECT setval(pg_get_serial_sequence('user_group_member', 'user_group_id'), COALESCE((SELECT MAX(user_group_id) FROM user_group_member), 1));
SELECT setval(pg_get_serial_sequence('sso_provider', 'sso_provider_id'), COALESCE((SELECT MAX(sso_provider_id) FROM sso_provider), 1));
SELECT setval(pg_get_serial_sequence('client_configs', 'id'), COALESCE((SELECT MAX(id) FROM client_configs), 1));
