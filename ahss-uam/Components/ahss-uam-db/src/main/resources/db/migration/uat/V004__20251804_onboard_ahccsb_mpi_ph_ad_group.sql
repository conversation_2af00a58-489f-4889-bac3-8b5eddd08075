-- Insert into tenant_config
INSERT INTO UAM.tenant_config (tenant_id, tenant_code, config_type, config_value, config_value_format_type, tenant_config_status, created_at,
                               updated_at, created_by, updated_by, config_key)
VALUES (8, 'MPI_PH', 'COMPANY', 'AH', 'FREE_TEXT', 'ACTIVE', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'system', 'system',
        'AH_AHCCSB_MPI_PH_USER_PREPROD'),
       (8, 'MPI_PH', 'LOCATION_REGION', 'APAC', 'FREE_TEXT', 'ACTIVE', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'system', 'system',
        'AH_AHCCSB_MPI_PH_USER_PREPROD'),
       (8, 'MPI_PH', 'LOCATION_COUNTRY', 'PH', 'FREE_TEXT', 'ACTIVE', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'system', 'system',
        'AH_AHCCSB_MPI_PH_USER_PREPROD'),
       (8, 'MPI_PH', 'ROLE', 'USER', 'FREE_TEXT', 'ACTIVE', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'system', 'system',
        'AH_AHCCSB_MPI_PH_USER_PREPROD'),
       (8, 'MPI_PH', 'PRODUCT', 'AHCCSB', 'FREE_TEXT', 'ACTIVE', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'system', 'system',
        'AH_AHCCSB_MPI_PH_USER_PREPROD'),
       (8, 'MPI_PH', 'COMPANY', 'AH', 'FREE_TEXT', 'ACTIVE', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'system', 'system',
        'AH_AHCCSB_MPI_PH_ADMIN_PREPROD'),
       (8, 'MPI_PH', 'LOCATION_REGION', 'APAC', 'FREE_TEXT', 'ACTIVE', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'system', 'system',
        'AH_AHCCSB_MPI_PH_ADMIN_PREPROD'),
       (8, 'MPI_PH', 'LOCATION_COUNTRY', 'PH', 'FREE_TEXT', 'ACTIVE', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'system', 'system',
        'AH_AHCCSB_MPI_PH_ADMIN_PREPROD'),
       (8, 'MPI_PH', 'ROLE', 'ADMIN', 'FREE_TEXT', 'ACTIVE', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'system', 'system',
        'AH_AHCCSB_MPI_PH_ADMIN_PREPROD'),
       (8, 'MPI_PH', 'PRODUCT', 'AHCCSB', 'FREE_TEXT', 'ACTIVE', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'system', 'system',
        'AH_AHCCSB_MPI_PH_ADMIN_PREPROD');


