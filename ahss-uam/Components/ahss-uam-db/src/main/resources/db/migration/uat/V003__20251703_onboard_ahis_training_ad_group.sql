-- Insert into organization
INSERT INTO UAM.organization (org_id, name, country, created_at, updated_at, created_by, updated_by)
VALUES (8, 'AH Training', 'TRAINING', '2024-03-17', '2024-03-17', 'system', 'system');

-- Insert into tenant
INSERT INTO UAM.tenant (tenant_id, organization_id, tenant_code, name, path, type, tenant_status, created_at, updated_at, created_by, updated_by)
VALUES (10, 8, 'AH_TRAINING', 'AH Training', '/AH/AH_TRAINING', 'BUSINESS_IN', 'ACTIVE', '2025-03-17', '2025-03-17', 'system', 'system');

-- Insert into tenant_config
INSERT INTO UAM.tenant_config (tenant_id, tenant_code, config_type, config_value, config_value_format_type, tenant_config_status, created_at,
                               updated_at, created_by, updated_by, config_key)
VALUES (10, 'AH_TRAINING', 'COMPANY', 'AH', 'FREE_TEXT', 'ACTIVE', '2025-03-17', '2025-03-17', 'system', 'system', 'AH_AHIS_AH_TRAINING_USER_UAT'),
       (10, 'AH_TRAINING', 'LOCATION_REGION', 'APAC', 'FREE_TEXT', 'ACTIVE', '2025-03-17', '2025-03-17', 'system', 'system',
        'AH_AHIS_AH_TRAINING_USER_UAT'),
       (10, 'AH_TRAINING', 'LOCATION_COUNTRY', 'INTERNAL', 'FREE_TEXT', 'ACTIVE', '2025-03-17', '2025-03-17', 'system', 'system',
        'AH_AHIS_AH_TRAINING_USER_UAT'),
       (10, 'AH_TRAINING', 'ROLE', 'USER', 'FREE_TEXT', 'ACTIVE', '2025-03-17', '2025-03-17', 'system', 'system', 'AH_AHIS_AH_TRAINING_USER_UAT'),
       (10, 'AH_TRAINING', 'PRODUCT', 'AHIS', 'FREE_TEXT', 'ACTIVE', '2025-03-17', '2025-03-17', 'system', 'system', 'AH_AHIS_AH_TRAINING_USER_UAT'),
       (10, 'AH_TRAINING', 'COMPANY', 'AH', 'FREE_TEXT', 'ACTIVE', '2025-03-17', '2025-03-17', 'system', 'system', 'AH_AHIS_AH_TRAINING_ADMIN_UAT'),
       (10, 'AH_TRAINING', 'LOCATION_REGION', 'APAC', 'FREE_TEXT', 'ACTIVE', '2025-03-17', '2025-03-17', 'system', 'system',
        'AH_AHIS_AH_TRAINING_ADMIN_UAT'),
       (10, 'AH_TRAINING', 'LOCATION_COUNTRY', 'INTERNAL', 'FREE_TEXT', 'ACTIVE', '2025-03-17', '2025-03-17', 'system', 'system',
        'AH_AHIS_AH_TRAINING_ADMIN_UAT'),
       (10, 'AH_TRAINING', 'ROLE', 'USER', 'FREE_TEXT', 'ACTIVE', '2025-03-17', '2025-03-17', 'system', 'system', 'AH_AHIS_AH_TRAINING_ADMIN_UAT'),
       (10, 'AH_TRAINING', 'PRODUCT', 'AHIS', 'FREE_TEXT', 'ACTIVE', '2025-03-17', '2025-03-17', 'system', 'system', 'AH_AHIS_AH_TRAINING_ADMIN_UAT');


