-- Insert into organization
INSERT INTO UAM.organization (org_id, name, country, created_at, updated_at, created_by, updated_by)
VALUES (1, 'AIA Group', 'SG', '2024-01-01', '2024-01-01', 'system', 'system'),
       (2, 'AIA Singapore', 'SG', '2024-01-01', '2024-01-01', 'system', 'system'),
       (3, 'AIA Malaysia', 'MY', '2024-01-01', '2024-01-01', 'system', 'system'),
       (4, 'AIA Thailand', 'TH', '2024-01-01', '2024-01-01', 'system', 'system'),
       (5, 'AIA HongKong', 'HK', '2024-01-01', '2024-01-01', 'system', 'system'),
       (6, 'AH Internal', 'INTERNAL', '2024-01-01', '2024-01-01', 'system', 'system');

-- Insert into tenant
INSERT INTO UAM.tenant (tenant_id, organization_id, tenant_code, name, path, type, tenant_status, created_at, updated_at, created_by, updated_by)
VALUES (1, 1, 'AIA', 'AIA Group', '/AIA', 'BUSINESS_IN', 'ACTIVE', '2024-01-01', '2024-01-01', 'system', 'system'),
       (2, 2, 'AIA_SG', 'AIA Singapore', '/AIA/AIA_SG', 'BUSINESS_IN', 'ACTIVE', '2024-01-01', '2024-01-01', 'system', 'system'),
       (3, 3, 'AIA_MY', 'AIA Malaysia', '/AIA/AIA_MY', 'BUSINESS_IN', 'ACTIVE', '2024-01-01', '2024-01-01', 'system', 'system'),
       (4, 4, 'AIA_TH', 'AIA Thailand', '/AIA/AIA_TH', 'BUSINESS_IN', 'ACTIVE', '2024-01-01', '2024-01-01', 'system', 'system'),
       (5, 5, 'AIA_HK', 'AIA HongKong', '/AIA/AIA_HK', 'BUSINESS_IN', 'ACTIVE', '2024-01-01', '2024-01-01', 'system', 'system'),
       (6, 6, 'AH_INTERNAL', 'AH Internal', '/AH/AH_INTERNAL', 'BUSINESS_IN', 'ACTIVE', '2024-01-01', '2024-01-01', 'system', 'system');

-- Insert into tenant_config
INSERT INTO UAM.tenant_config (tenant_id, tenant_code, config_type, config_value, config_value_format_type, tenant_config_status, created_at,
                               updated_at, created_by, updated_by, config_key)
VALUES (2, 'AIA_SG', 'PRODUCT', 'AHPP', 'FREE_TEXT', 'ACTIVE', '2024-01-01', '2024-01-01', 'system', 'system', null),
       (3, 'AIA_MY', 'PRODUCT', 'AHPP', 'FREE_TEXT', 'ACTIVE', '2024-01-01', '2024-01-01', 'system', 'system', null),
       (4, 'AIA_TH', 'PRODUCT', 'AHPP', 'FREE_TEXT', 'ACTIVE', '2024-01-01', '2024-01-01', 'system', 'system', null),
       (5, 'AIA_HK', 'COMPANY', 'AH', 'FREE_TEXT', 'ACTIVE', '2024-01-01', '2024-01-01', 'system', 'system', 'AH_AHIS_AIA_HK_USER_UAT'),
       (5, 'AIA_HK', 'LOCATION_REGION', 'APAC', 'FREE_TEXT', 'ACTIVE', '2024-01-01', '2024-01-01', 'system', 'system', 'AH_AHIS_AIA_HK_USER_UAT'),
       (5, 'AIA_HK', 'LOCATION_COUNTRY', 'SG', 'FREE_TEXT', 'ACTIVE', '2024-01-01', '2024-01-01', 'system', 'system', 'AH_AHIS_AIA_HK_USER_UAT'),
       (5, 'AIA_HK', 'ROLE', 'USER', 'FREE_TEXT', 'ACTIVE', '2024-01-01', '2024-01-01', 'system', 'system', 'AH_AHIS_AIA_HK_USER_UAT'),
       (5, 'AIA_HK', 'PRODUCT', 'AHIS', 'FREE_TEXT', 'ACTIVE', '2024-01-01', '2024-01-01', 'system', 'system', 'AH_AHIS_AIA_HK_USER_UAT'),
       (5, 'AIA_HK', 'COMPANY', 'AH', 'FREE_TEXT', 'ACTIVE', '2024-01-01', '2024-01-01', 'system', 'system', 'AH_AHIS_AIA_HK_ADMIN_UAT'),
       (5, 'AIA_HK', 'LOCATION_REGION', 'APAC', 'FREE_TEXT', 'ACTIVE', '2024-01-01', '2024-01-01', 'system', 'system', 'AH_AHIS_AIA_HK_ADMIN_UAT'),
       (5, 'AIA_HK', 'LOCATION_COUNTRY', 'SG', 'FREE_TEXT', 'ACTIVE', '2024-01-01', '2024-01-01', 'system', 'system', 'AH_AHIS_AIA_HK_ADMIN_UAT'),
       (5, 'AIA_HK', 'ROLE', 'ADMIN', 'FREE_TEXT', 'ACTIVE', '2024-01-01', '2024-01-01', 'system', 'system', 'AH_AHIS_AIA_HK_ADMIN_UAT'),
       (5, 'AIA_HK', 'PRODUCT', 'AHIS', 'FREE_TEXT', 'ACTIVE', '2024-01-01', '2024-01-01', 'system', 'system', 'AH_AHIS_AIA_HK_ADMIN_UAT'),

       (3, 'AIA_MY', 'COMPANY', 'AH', 'FREE_TEXT', 'ACTIVE', '2024-01-01', '2024-01-01', 'system', 'system', 'AH_AHIS_AIA_MY_USER_UAT'),
       (3, 'AIA_MY', 'LOCATION_REGION', 'APAC', 'FREE_TEXT', 'ACTIVE', '2024-01-01', '2024-01-01', 'system', 'system', 'AH_AHIS_AIA_MY_USER_UAT'),
       (3, 'AIA_MY', 'LOCATION_COUNTRY', 'MY', 'FREE_TEXT', 'ACTIVE', '2024-01-01', '2024-01-01', 'system', 'system', 'AH_AHIS_AIA_MY_USER_UAT'),
       (3, 'AIA_MY', 'ROLE', 'USER', 'FREE_TEXT', 'ACTIVE', '2024-01-01', '2024-01-01', 'system', 'system', 'AH_AHIS_AIA_MY_USER_UAT'),
       (3, 'AIA_MY', 'PRODUCT', 'AHIS', 'FREE_TEXT', 'ACTIVE', '2024-01-01', '2024-01-01', 'system', 'system', 'AH_AHIS_AIA_MY_USER_UAT'),
       (3, 'AIA_MY', 'COMPANY', 'AH', 'FREE_TEXT', 'ACTIVE', '2024-01-01', '2024-01-01', 'system', 'system', 'AH_AHIS_AIA_MY_ADMIN_UAT'),
       (3, 'AIA_MY', 'LOCATION_REGION', 'APAC', 'FREE_TEXT', 'ACTIVE', '2024-01-01', '2024-01-01', 'system', 'system', 'AH_AHIS_AIA_MY_ADMIN_UAT'),
       (3, 'AIA_MY', 'LOCATION_COUNTRY', 'MY', 'FREE_TEXT', 'ACTIVE', '2024-01-01', '2024-01-01', 'system', 'system', 'AH_AHIS_AIA_MY_ADMIN_UAT'),
       (3, 'AIA_MY', 'ROLE', 'ADMIN', 'FREE_TEXT', 'ACTIVE', '2024-01-01', '2024-01-01', 'system', 'system', 'AH_AHIS_AIA_MY_ADMIN_UAT'),
       (3, 'AIA_MY', 'PRODUCT', 'AHIS', 'FREE_TEXT', 'ACTIVE', '2024-01-01', '2024-01-01', 'system', 'system', 'AH_AHIS_AIA_MY_ADMIN_UAT'),

       (6, 'AH_INTERNAL', 'COMPANY', 'AH', 'FREE_TEXT', 'ACTIVE', '2024-01-01', '2024-01-01', 'system', 'system', 'AH_AHIS_AH_INTERNAL_USER_UAT'),
       (6, 'AH_INTERNAL', 'LOCATION_REGION', 'APAC', 'FREE_TEXT', 'ACTIVE', '2024-01-01', '2024-01-01', 'system', 'system',
        'AH_AHIS_AH_INTERNAL_USER_UAT'),
       (6, 'AH_INTERNAL', 'LOCATION_COUNTRY', 'INTERNAL', 'FREE_TEXT', 'ACTIVE', '2024-01-01', '2024-01-01', 'system', 'system',
        'AH_AHIS_AH_INTERNAL_USER_UAT'),
       (6, 'AH_INTERNAL', 'ROLE', 'USER', 'FREE_TEXT', 'ACTIVE', '2024-01-01', '2024-01-01', 'system', 'system', 'AH_AHIS_AH_INTERNAL_USER_UAT'),
       (6, 'AH_INTERNAL', 'PRODUCT', 'AHIS', 'FREE_TEXT', 'ACTIVE', '2024-01-01', '2024-01-01', 'system', 'system', 'AH_AHIS_AH_INTERNAL_USER_UAT'),
       (6, 'AH_INTERNAL', 'COMPANY', 'AH', 'FREE_TEXT', 'ACTIVE', '2024-01-01', '2024-01-01', 'system', 'system', 'AH_AHIS_AH_INTERNAL_ADMIN_UAT'),
       (6, 'AH_INTERNAL', 'LOCATION_REGION', 'APAC', 'FREE_TEXT', 'ACTIVE', '2024-01-01', '2024-01-01', 'system', 'system',
        'AH_AHIS_AH_INTERNAL_ADMIN_UAT'),
       (6, 'AH_INTERNAL', 'LOCATION_COUNTRY', 'INTERNAL', 'FREE_TEXT', 'ACTIVE', '2024-01-01', '2024-01-01', 'system', 'system',
        'AH_AHIS_AH_INTERNAL_ADMIN_UAT'),
       (6, 'AH_INTERNAL', 'ROLE', 'ADMIN', 'FREE_TEXT', 'ACTIVE', '2024-01-01', '2024-01-01', 'system', 'system', 'AH_AHIS_AH_INTERNAL_ADMIN_UAT'),
       (6, 'AH_INTERNAL', 'PRODUCT', 'AHIS', 'FREE_TEXT', 'ACTIVE', '2024-01-01', '2024-01-01', 'system', 'system', 'AH_AHIS_AH_INTERNAL_ADMIN_UAT');

-- Insert into role
INSERT INTO UAM.role (name, created_at, updated_at, created_by, updated_by)
VALUES ('Admin Role', '2024-01-01', '2024-01-01', 'system', 'system');

CREATE TEMP TABLE temp_inserted_entities
(
    id   SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL
);

INSERT INTO UAM.entity (name, path, status, created_at, updated_at, created_by, updated_by)
VALUES ('Warren', '', 'ACTIVE', '2024-01-01', '2024-01-01', 'system', 'system'),
       ('Dennis', '', 'ACTIVE', '2024-01-01', '2024-01-01', 'system', 'system'),
       ('Kai-en', '', 'ACTIVE', '2024-01-01', '2024-01-01', 'system', 'system'),
       ('Hwai Siang', '', 'ACTIVE', '2024-01-01', '2024-01-01', 'system', 'system');

UPDATE UAM.entity
SET path = CONCAT('/', entity_id)
WHERE path = '';


-- Insert into entity_mappings table using the captured IDs
INSERT
INTO UAM.entity_mappings (entity_id, system_id, name, external_identifier, created_at, updated_at, created_by, updated_by)
SELECT entity.entity_id,
       CASE entity.name
           WHEN 'Warren' THEN '<EMAIL>'
           WHEN 'Dennis' THEN '<EMAIL>'
           WHEN 'Kai-en' THEN '<EMAIL>'
           WHEN 'Hwai Siang' THEN '<EMAIL>'
           END,
       entity.name,
       'EMAIL',
       '2024-01-01',
       '2024-01-01',
       'system',
       'system'
FROM (SELECT entity_id, name from UAM.entity) as entity;

-- Insert into profile table using the captured IDs
INSERT INTO UAM.profile (entity_id, role_id, username, username_type, created_at, updated_at, created_by, updated_by)
SELECT entity.entity_id,
       (select role_id from UAM.role where name = 'Admin Role'),
       entity.system_id,
       'EMAIL',
       '2024-01-01',
       '2024-01-01',
       'system',
       'system'
FROM (SELECT entity.entity_id, system_id
      from UAM.entity
               join UAM.entity_mappings on entity.entity_id = entity_mappings.entity_id) as entity;

-- Insert into profile

-- Insert into permission
INSERT INTO UAM.permission (name, description, created_at, updated_at, created_by, updated_by)
VALUES ('AHIS_P1', 'Insight Studio Permission 1', '2024-01-01', '2024-01-01', 'system', 'system');

-- Insert into role_permission
INSERT INTO UAM.role_permission (role_id, permission_id, created_at, updated_at, created_by, updated_by)
VALUES ((select role_id from UAM.role where name = 'Admin Role'), (select permission_id from UAM.permission), '2024-01-01', '2024-01-01', 'system',
        'system');

-- Insert into user_group
INSERT INTO UAM.user_group (name, description, created_at, updated_at, created_by, updated_by)
VALUES ('Admin Group', 'Group for Admin Role', '2024-01-01', '2024-01-01', 'system', 'system');

-- Insert into user_group_member
INSERT INTO UAM.user_group_member (user_group_id, entity_id, created_at, updated_at, created_by, updated_by)
SELECT (select user_group_id from UAM.user_group where name = 'Admin Group'),
       entity.entity_id,
       '2024-01-01',
       '2024-01-01',
       'system',
       'system'
FROM (SELECT entity_id from UAM.entity) as entity;

-- Insert into sso_provider
INSERT INTO UAM.sso_provider (tenant_id, tenant_code, name, client_id, client_secret, client_domain, discovery_url, created_at, updated_at,
                              created_by,
                              updated_by)
VALUES (1, 'AIA', 'SSO Provider AIA', '0oa1zilpdp05xQp540h8',
        'z2cOy/zlOQr1Jwo18MX8bra22JCB1FS7A84ZBo2JCadgW1+ZZoSSjFqVxpTQD6u0NPI5KXccgVabFaHrcIbyBEBR2uqKNWYx+2aT5UmdaMSgO4N9FGg7RV3FPYbHe6iBVlxeqXmHrMf0ANU/',
        'aia.com', 'https://aiatest.okta.com/oauth2/aus1zilsv95ktmdzW0h8',
        '2024-01-01',
        '2024-01-01', 'system',
        'system'),
       (2, 'AIA_SG', 'SSO Provider AIA SG', 'client_id_sg', 'secret_sg', 'aia.com', 'https://sso.sg.discovery.url', '2024-01-01', '2024-01-01',
        'system',
        'system'),
       (3, 'AIA_MY', 'SSO Provider AIA MY', 'client_id_my', 'secret_my', 'aia.com', 'https://sso.my.discovery.url', '2024-01-01', '2024-01-01',
        'system',
        'system'),
       (4, 'AIA_TH', 'SSO Provider AIA TH', 'client_id_th', 'secret_th', 'aia.com', 'https://sso.th.discovery.url', '2024-01-01', '2024-01-01',
        'system',
        'system'),
       (5, 'AIA_HK', 'SSO Provider AIA HK', 'client_id_hk', 'secret_hk', 'aia.com', 'https://sso.th.discovery.url', '2024-01-01', '2024-01-01',
        'system',
        'system'),
       (6, 'AH_INTERNAL', 'SSO Provider AH INTERNAL', '0oa1zilpdp05xQp540h8',
        'z2cOy/zlOQr1Jwo18MX8bra22JCB1FS7A84ZBo2JCadgW1+ZZoSSjFqVxpTQD6u0NPI5KXccgVabFaHrcIbyBEBR2uqKNWYx+2aT5UmdaMSgO4N9FGg7RV3FPYbHe6iBVlxeqXmHrMf0ANU/',
        'amplifyhealth.com', 'https://aiatest.okta.com/oauth2/aus1zilsv95ktmdzW0h8', '2024-01-01',
        '2024-01-01',
        'system',
        'system');

-- Insert into client_configs
INSERT INTO UAM.client_configs (client_id, client_secret_key, client_access_token, client_shared_key, status, created_at, updated_at, created_by,
                                updated_by)
VALUES ('AHIS', 'aUu1qW5rchDRuiItyLvfLXhJtr+LnQq/HVA3/ByJncdxUampBFuCsh2t3x85UjntWzwircskv1iRl0ltoTxtaNS0Q4zauKWVJb47mRc4QkpO18YSUvVy',
        'eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************.QRsn4AHqAIPW6ofFJHY4yhWuXs0oo7gyjdAnckkgAoo',
        'IGwIzIDlcMgNCeayaXlHESB-s9Visdtv9EaUqilFnJc', 'ACTIVE', '2024-10-28 12:59:46.925069', '2024-10-28 12:59:46.925069', 'system', 'system')
ON CONFLICT DO NOTHING;

-- Update id sequences
SELECT setval(pg_get_serial_sequence('UAM.organization', 'org_id'), COALESCE((SELECT MAX(org_id) FROM UAM.organization), 1));
SELECT setval(pg_get_serial_sequence('UAM.tenant', 'tenant_id'), COALESCE((SELECT MAX(tenant_id) FROM UAM.tenant), 1));
SELECT setval(pg_get_serial_sequence('UAM.tenant_config', 'tenant_id'), COALESCE((SELECT MAX(tenant_id) FROM UAM.tenant_config), 1));
SELECT setval(pg_get_serial_sequence('UAM.entity_mappings', 'entity_id'), COALESCE((SELECT MAX(entity_id) FROM UAM.entity_mappings), 1));
SELECT setval(pg_get_serial_sequence('UAM.role', 'role_id'), COALESCE((SELECT MAX(role_id) FROM UAM.role), 1));
SELECT setval(pg_get_serial_sequence('UAM.profile', 'entity_id'), COALESCE((SELECT MAX(entity_id) FROM UAM.profile), 1));
SELECT setval(pg_get_serial_sequence('UAM.permission', 'permission_id'), COALESCE((SELECT MAX(permission_id) FROM UAM.permission), 1));
SELECT setval(pg_get_serial_sequence('UAM.role_permission', 'role_id'), COALESCE((SELECT MAX(role_id) FROM UAM.role_permission), 1));
SELECT setval(pg_get_serial_sequence('UAM.user_group', 'user_group_id'), COALESCE((SELECT MAX(user_group_id) FROM UAM.user_group), 1));
SELECT setval(pg_get_serial_sequence('UAM.user_group_member', 'user_group_id'), COALESCE((SELECT MAX(user_group_id) FROM UAM.user_group_member), 1));
SELECT setval(pg_get_serial_sequence('UAM.sso_provider', 'tenant_id'), COALESCE((SELECT MAX(tenant_id) FROM UAM.sso_provider), 1));

