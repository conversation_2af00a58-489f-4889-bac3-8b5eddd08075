DO
$$
    DECLARE
        organization_id BIGINT;
        new_tenant_id   BIGINT;
        new_product_id  BIGINT;
        new_package_id  BIGINT;
        new_plan_id     BIGINT;
        new_module_id   BIGINT;
        tenant_code     TEXT   := 'AH_EXTERNAL';
        product_code    TEXT   := 'PRODUCTCODE';
        ad_group        TEXT[] := ARRAY ['AH_PRODUCTCODE_AH_EXTERNAL_USER_DEV', 'AH_PRODUCTCODE_AH_EXTERNAL_ADMIN_DEV']; -- Array of config keys
        key             TEXT;
    BEGIN
        INSERT INTO UAM.organization (name, country, created_at, updated_at, created_by, updated_by)
        VALUES ('Organization name', 'External', '2024-01-01', '2024-01-01', 'system', 'system')
        RETURNING org_id INTO organization_id;

        -- Create a new tenant using the retrieved org_id
        INSERT INTO UAM.tenant (organization_id, tenant_code, name, path, type, tenant_status, created_at, updated_at, created_by, updated_by)
        VALUES (organization_id, tenant_code, 'AH Internal', 'path', 'BUSINESS_IN', 'ACTIVE', '2024-01-01', '2024-01-01', 'system',
                'system')
        RETURNING tenant_id INTO new_tenant_id;

        -- Use the retrieved tenant_id for tenant configurations
        FOREACH key IN ARRAY ad_group
            LOOP
                INSERT INTO UAM.tenant_config (tenant_id, tenant_code, config_type, config_value, config_value_format_type, tenant_config_status,
                                               created_at,
                                               updated_at, created_by, updated_by, config_key)
                VALUES (new_tenant_id, tenant_code, 'PRODUCT', product_code, 'FREE_TEXT', 'ACTIVE', '2024-01-01', '2024-01-01', 'system', 'system',
                        key);
            END LOOP;

        -- Add SSO provider using the tenant_id
        INSERT INTO UAM.sso_provider (tenant_id, tenant_code, name, client_id, client_secret, client_domain, discovery_url, created_at, updated_at,
                                      created_by, updated_by)
        VALUES (new_tenant_id, tenant_code, 'SSO Provider AH INTERNAL', '',
                '', 'amplifyhealth.com', '', '2024-01-01', '2024-01-01', 'system', 'system');

        -- Insert Product and retrieve the product_id
        INSERT INTO AHPP.product (product_code, product_name, description, product_status, created_at, updated_at, created_by, updated_by)
        VALUES (product_code, 'Product name', 'Description', 'ACTIVE', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'system', 'system')
        RETURNING product_id INTO new_product_id;

        -- Use the retrieved product_id to insert into product_config
        INSERT INTO AHPP.product_config (product_id, product_code, config_type, config_value, config_value_format_type, regex,
                                         product_config_status,
                                         created_at, updated_at, created_by, updated_by)
        VALUES (new_product_id, product_code, 'URL', 'https://sit.insights.amplifyhealth.com/authorize', 'URL', '^https?://', 'ACTIVE',
                CURRENT_TIMESTAMP,
                CURRENT_TIMESTAMP, 'system', 'system');

        -- new Plan setup ---
        INSERT INTO AHPP.plan (name, discount_rate, start_date, end_date, plan_type, plan_status, created_at, updated_at, created_by, updated_by)
        VALUES ('Internal billing plan', 0, '2024-01-01', '9999-12-31', 'SUBSCRIPTION', 'ACTIVE', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'system',
                'system')
        Returning plan_id INTO new_plan_id;

        -- Insert into tenant_plan
        INSERT INTO AHPP.tenant_plan (tenant_id, tenant_code, plan_id, assigned_at, created_at, updated_at, created_by, updated_by)
        VALUES (new_tenant_id, tenant_code, new_plan_id, '2024-01-01', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'system', 'system');

        -- Insert into package
        INSERT INTO AHPP.package (plan_id, name, type, price, package_status, start_date, end_date, version, created_at, updated_at, created_by,
                                  updated_by)
        VALUES (new_plan_id, 'Insight Package', 'Type1', 1000, 'ACTIVE', '2024-01-01', '9999-12-31', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP,
                'system',
                'system')
        returning package_id into new_package_id;

        -- Insert into module
        INSERT INTO AHPP.module (name, price, product_id, module_status, created_at, updated_at, created_by, updated_by)
        VALUES ('Payment Management', 400, new_product_id, 'ACTIVE', '2024-01-01', '2024-01-01', 'system', 'system')
        returning module_id into new_module_id;

        -- Insert into package_module
        INSERT INTO AHPP.package_module (package_id, module_id, created_at, updated_at, created_by, updated_by)
        VALUES (new_package_id, new_module_id, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'system', 'system');
    END
$$;

