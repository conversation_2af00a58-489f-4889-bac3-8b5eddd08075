package com.ahss.uam.db.repository.custom.impl;

import com.ahss.uam.db.model.UserEntity;
import com.ahss.uam.db.repository.custom.UserEntityCustomRepository;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.TypedQuery;
import java.util.List;
import org.springframework.stereotype.Repository;

@Repository
public class UserEntityCustomRepositoryImpl implements UserEntityCustomRepository {

    @PersistenceContext
    private EntityManager entityManager;

    @Override
    public List<UserEntity> searchUsers(String search, Integer tenantId, Integer page, Integer size, String sortBy, String sortDirection) {
        String baseQuery = buildQuery("SELECT u FROM UserEntity u JOIN u.tenantEntities te WHERE 1=1", tenantId, search);
        baseQuery += " ORDER BY u." + sortBy + " " + sortDirection;

        TypedQuery<UserEntity> query = entityManager.createQuery(baseQuery, UserEntity.class);
        setParameters(query, tenantId, search);

        query.setFirstResult(page * size);
        query.setMaxResults(size);

        return query.getResultList();
    }

    @Override
    public Long countUser(String search, Integer tenantId) {
        String baseQuery = buildQuery("SELECT COUNT(u) FROM UserEntity u JOIN u.tenantEntities te WHERE 1=1", tenantId, search);

        TypedQuery<Long> query = entityManager.createQuery(baseQuery, Long.class);
        setParameters(query, tenantId, search);

        return query.getSingleResult();
    }

    private String buildQuery(String baseQuery, Integer tenantId, String search) {
        StringBuilder query = new StringBuilder(baseQuery);

        if (tenantId != null) {
            query.append(" AND te.tenant.tenantId = :tenantId");
        }

        if (search != null && !search.isEmpty()) {
            query.append(" AND u.name LIKE :search");
        }

        return query.toString();
    }

    private <T> void setParameters(TypedQuery<T> query, Integer tenantId, String search) {
        if (tenantId != null) {
            query.setParameter("tenantId", tenantId);
        }

        if (search != null && !search.isEmpty()) {
            query.setParameter("search", "%" + search + "%");
        }
    }
}
