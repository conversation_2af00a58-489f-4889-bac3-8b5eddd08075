package com.ahss.uam.db.model;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@AllArgsConstructor
@Entity
@Table(name = "ENTITY_MAPPINGS", schema = "UAM")
@NoArgsConstructor
@SuperBuilder
public class EntityMapping extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "entity_mappings_id")
    private Long entityMappingsId;

    @Column(name = "system_id", nullable = false)
    private String systemId;

    @Column(name = "name", nullable = false)
    private String name;

    @Column(name = "external_identifier", nullable = false)
    private String externalIdentifier;

    @ManyToOne
    @JoinColumn(name = "entity_id", referencedColumnName = "entity_id")
    private UserEntity userEntity;

}
