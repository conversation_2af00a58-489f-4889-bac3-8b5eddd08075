package com.ahss.uam.db.repository;

import com.ahss.uam.db.model.Tenant;
import com.ahss.uam.db.model.TenantConfig;
import com.ahss.uam.db.model.TenantConfigStatus;
import io.micrometer.observation.annotation.Observed;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Observed
@Repository
public interface TenantConfigRepository extends JpaRepository<TenantConfig, Integer> {

    @Query("SELECT DISTINCT e.tenant FROM TenantConfig e WHERE e.configKey IN :configKeys AND e.tenantConfigStatus = :status")
    List<Tenant> findDistinctTenantIdByConfigKeyInAndTenantConfigStatus(List<String> configKeys, TenantConfigStatus status);


}
