package com.ahss.uam.db.repository;

import com.ahss.uam.db.model.Tenant;
import io.micrometer.observation.annotation.Observed;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Observed
@Repository
public interface TenantRepository extends JpaRepository<Tenant, Integer> {

    @Override
    List<Tenant> findAll();

    Optional<Tenant> findByTenantId(Integer id);

    Optional<Tenant> findByTenantCode(String tenantCode);

    @Query(value = "SELECT COUNT(t.tenant_id) FROM UAM.Tenant t WHERE t.tenant_code = :tenantCode", nativeQuery = true)
    Long getCountTenantByCodeIncludingSuspended(String tenantCode);

    @Query(value = "SELECT COUNT(t.tenant_id) FROM UAM.Tenant t WHERE t.tenant_code = :tenantCode AND t.tenant_id <> :tenantId", nativeQuery = true)
    Long getCountTenantByCodeAndNotIdIncludingSuspended(String tenantCode, Integer tenantId);
}
