package com.ahss.uam.db.model;

import com.ahss.uam.db.model.enums.UserNameType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.JdbcType;
import org.hibernate.dialect.PostgreSQLEnumJdbcType;

@Getter
@Setter
@ToString
@NoArgsConstructor
@Entity
@SuperBuilder
@Table(name = "PROFILE", schema = "UAM")
public class Profile extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "profile_id")
    private Long profileId;

    @Column(name = "role_id", nullable = false)
    private Integer roleId;

    @Column(name = "username", nullable = false)
    private String username;

    @Enumerated(EnumType.STRING)
    @Column(name = "username_type", nullable = false)
    @JdbcType(PostgreSQLEnumJdbcType.class)
    private UserNameType usernameType;

    @ManyToOne
    @JoinColumn(name = "entity_id", referencedColumnName = "entity_id")
    private UserEntity userEntity;

}
