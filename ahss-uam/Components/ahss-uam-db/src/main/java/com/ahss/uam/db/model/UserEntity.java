package com.ahss.uam.db.model;

import com.ahss.uam.db.model.enums.UserStatus;
import com.github.f4b6a3.tsid.TsidCreator;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Id;
import jakarta.persistence.OneToMany;
import jakarta.persistence.PostPersist;
import jakarta.persistence.PrePersist;
import jakarta.persistence.Table;
import java.util.List;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.JdbcType;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLRestriction;
import org.hibernate.dialect.PostgreSQLEnumJdbcType;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "ENTITY", schema = "UAM")
@SuperBuilder
@SQLDelete(sql = "UPDATE UAM.ENTITY SET status = 'SUSPENDED' WHERE entity_id = ?")
@SQLRestriction("status <> 'SUSPENDED'")
public class UserEntity extends BaseEntity {

    @Id
    @Column(name = "entity_id")
    private Long entityId;

    @Column(name = "name", nullable = false)
    private String name;

    @Column(name = "parent_entity_id")
    private Long parentEntityId;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    @JdbcType(PostgreSQLEnumJdbcType.class)
    private UserStatus status;

    @Column(name = "path")
    private String path;

    @OneToMany(mappedBy = "userEntity", cascade = CascadeType.ALL)
    private List<Profile> profiles;

    @OneToMany(mappedBy = "userEntity", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<EntityMapping> entityMappings;

    @OneToMany(mappedBy = "userEntity", cascade = CascadeType.ALL, orphanRemoval = true)
    private Set<TenantEntity> tenantEntities;

    @PrePersist
    public void generateTsId() {
        this.entityId = TsidCreator
            .getTsid()
            .toLong();
    }

    @PostPersist
    public void setPathAfterPersist() {
        this.path = "/" + this.entityId;
    }
}
