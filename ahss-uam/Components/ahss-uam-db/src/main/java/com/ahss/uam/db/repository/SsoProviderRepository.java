package com.ahss.uam.db.repository;

import com.ahss.uam.db.model.SsoProvider;
import com.ahss.uam.db.model.enums.TenantStatus;
import io.micrometer.observation.annotation.Observed;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Observed
@Repository
public interface SsoProviderRepository extends JpaRepository<SsoProvider, Integer> {

    List<SsoProvider> findByClientDomainAndTenant_TenantStatus(String domain, TenantStatus tenantStatus);

    Optional<SsoProvider> findByTenantCode(String tenantCode);
}
