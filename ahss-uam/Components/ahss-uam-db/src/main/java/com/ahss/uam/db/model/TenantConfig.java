package com.ahss.uam.db.model;

import com.ahss.uam.db.model.enums.TenantConfigType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.JdbcType;
import org.hibernate.dialect.PostgreSQLEnumJdbcType;

@Entity
@Table(name = "tenant_config", schema = "UAM")
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Getter
@Setter
public class TenantConfig extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "tenant_config_id")
    private Long tenantConfigId;

    @Column(name = "tenant_code", nullable = false, unique = true, length = 20)
    private String tenantCode;

    @Enumerated(EnumType.STRING)
    @Column(name = "config_type", nullable = false)
    @JdbcType(PostgreSQLEnumJdbcType.class)
    private TenantConfigType configType;

    @Column(name = "config_key", nullable = false, columnDefinition = "TEXT")
    private String configKey;

    @Column(name = "config_value", nullable = false, columnDefinition = "TEXT")
    private String configValue;

    @Column(name = "description", columnDefinition = "TEXT")
    private String description;

    @Enumerated(EnumType.STRING)
    @Column(name = "config_value_format_type", nullable = false)
    @JdbcType(PostgreSQLEnumJdbcType.class)
    private ValueFormatType configValueFormatType;

    @Column(name = "regex", columnDefinition = "TEXT")
    private String regex;

    @Enumerated(EnumType.STRING)
    @Column(name = "tenant_config_status", nullable = false)
    @JdbcType(PostgreSQLEnumJdbcType.class)
    private TenantConfigStatus tenantConfigStatus;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "tenant_id")
    private Tenant tenant;


}
