package com.ahss.uam.db.model;

import com.ahss.uam.db.model.enums.ClientConfigStatus;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.JdbcType;
import org.hibernate.dialect.PostgreSQLEnumJdbcType;

@Entity
@Table(name = "client_configs", schema = "UAM")
@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
public class ClientConfig extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "client_id", nullable = false, unique = true)
    private String clientId;

    @Column(name = "client_secret_key")
    private String clientSecretKey;

    @Column(name = "client_access_token", nullable = false)
    private String clientAccessToken;

    @Column(name = "client_shared_key", nullable = false)
    private String clientSharedKey;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    @JdbcType(PostgreSQLEnumJdbcType.class)
    private ClientConfigStatus status = ClientConfigStatus.DRAFT;
}
