package com.ahss.uam.db.repository;

import com.ahss.uam.db.model.ClientConfig;
import com.ahss.uam.db.model.enums.ClientConfigStatus;
import io.micrometer.observation.annotation.Observed;
import java.util.Optional;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Observed
@Repository
public interface ClientConfigRepository extends JpaRepository<ClientConfig, Long> {

    @Cacheable(value = "clientConfigCache", key = "#clientId")
    Optional<ClientConfig> findByClientId(String clientId);

    @Cacheable(value = "clientConfigCache", key = "#clientId + ':' + #clientSharedKey")
    Optional<ClientConfig> findByClientIdAndClientSharedKey(String clientId, String clientSharedKey);

    @Cacheable(value = "clientConfigCache", key = "#clientSharedKey + ':' + #token + ':' + #status")
    Optional<ClientConfig> findByClientSharedKeyAndClientAccessTokenAndStatus(String clientSharedKey, String token, ClientConfigStatus status);

}
