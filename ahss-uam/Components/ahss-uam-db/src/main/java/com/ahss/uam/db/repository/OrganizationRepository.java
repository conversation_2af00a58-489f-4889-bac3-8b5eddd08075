package com.ahss.uam.db.repository;

import com.ahss.uam.db.model.Organization;
import io.micrometer.observation.annotation.Observed;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Observed
@Repository
public interface OrganizationRepository extends JpaRepository<Organization, Integer> {

    @Override
    List<Organization> findAll();

    Optional<Organization> findByOrgId(Integer orgId);

}
