package com.ahss.uam.db.repository;

import com.ahss.uam.db.model.EntityMapping;
import io.micrometer.observation.annotation.Observed;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Observed
@Repository
public interface EntityMappingRepository extends JpaRepository<EntityMapping, Long> {

    Optional<EntityMapping> findDistinctFirstBySystemIdAndExternalIdentifier(String systemId, String externalIdentifier);
}
