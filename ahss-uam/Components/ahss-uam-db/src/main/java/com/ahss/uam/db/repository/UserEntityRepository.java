package com.ahss.uam.db.repository;

import com.ahss.uam.db.model.UserEntity;
import com.ahss.uam.db.repository.custom.UserEntityCustomRepository;
import io.micrometer.observation.annotation.Observed;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Observed
@Repository
public interface UserEntityRepository extends JpaRepository<UserEntity, Long>, UserEntityCustomRepository {

}
