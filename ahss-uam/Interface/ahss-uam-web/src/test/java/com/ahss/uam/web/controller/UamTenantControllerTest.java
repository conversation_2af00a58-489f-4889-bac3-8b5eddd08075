package com.ahss.uam.web.controller;

import static com.ahss.common.api.AHSSResponseCode.RC_200_000;
import static com.ahss.common.api.AHSSResponseCode.RC_400_005;
import static com.ahss.common.api.AHSSResponseCode.RC_400_007;
import static com.ahss.common.api.AHSSResponseCode.RC_404_000;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.restdocs.mockmvc.RestDocumentationRequestBuilders.delete;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.ahss.uam.db.model.Tenant;
import com.ahss.uam.db.model.enums.TenantStatus;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.Test;
import org.springframework.http.MediaType;

class UamTenantControllerTest extends BaseControllerTest {

    private static final String BASE_URL = "/v1/api/uam/tenant";

    @Test
    void getAllTenants() throws Exception {
        // Given
        when(tenantRepository.findAll()).thenReturn(List.of(
            Tenant.builder()
                  .tenantId(1)
                  .tenantCode("AIAHK")
                  .name("AIA Hong Kong")
                  .tenantStatus(TenantStatus.ACTIVE)
                  .createdAt(ZonedDateTime.now())
                  .updatedAt(ZonedDateTime.now())
                  .createdBy("SYSTEM")
                  .updatedBy("SYSTEM").build()));

        // When & Then
        this.mockMvc.perform(get("http://localhost:" + port + BASE_URL)).andDo(print())
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.status").value("SUCCESS"))
                    .andExpect(jsonPath("$.responseCode.code").value(RC_200_000.getCode()))
                    .andExpect(jsonPath("$.data").isNotEmpty())
                    .andExpect(jsonPath("$.data[0].tenantId").value(1))
                    .andExpect(jsonPath("$.data[0].name").value("AIA Hong Kong"));
    }

    @Test
    void createTenantSuccess() throws Exception {
        Tenant tenant = new Tenant();
        tenant.setTenantId(1);
        when(tenantRepository.getCountTenantByCodeIncludingSuspended(eq("ABC"))).thenReturn(0L);
        this.mockMvc.perform(post("http://localhost:" + port + BASE_URL)
                                 .contentType(MediaType.APPLICATION_JSON)
                                 .content("""
                                              {
                                                "name": "Updated Tenant",
                                                "tenantCode": "ABC",
                                                "type": "INDIVIDUAL",
                                                "tenantStatus": "ACTIVE",
                                                "path": "test/domain"
                                              }
                                              """))
                    .andDo(print())
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.status").value("SUCCESS"));
    }

    @Test
    void createTenant_BadRequestMissingOrganization() throws Exception {
        when(tenantRepository.getCountTenantByCodeIncludingSuspended(eq("ABC"))).thenReturn(0L);
        this.mockMvc.perform(post("http://localhost:" + port + BASE_URL)
                                 .contentType(MediaType.APPLICATION_JSON)
                                 .content("""
                                              {
                                                "name": "Updated Tenant",
                                                "tenantCode": "ABC",
                                                "type": "BUSINESS_IN",
                                                "tenantStatus": "ACTIVE",
                                                "path": "test/domain"
                                              }
                                              """))
                    .andDo(print())
                    .andExpect(status().isBadRequest())
                    .andExpect(jsonPath("$.status").value("FAILED"))
                    .andExpect(jsonPath("$.responseCode.code").value(RC_400_007.getCode()));
    }

    @Test
    void updateTenantSuccess() throws Exception {
        Tenant tenant = new Tenant();
        tenant.setTenantId(1);
        when(tenantRepository.findByTenantId(eq(1))).thenReturn(Optional.of(tenant));
        when(tenantRepository.getCountTenantByCodeAndNotIdIncludingSuspended(eq("ABC"), eq(1))).thenReturn(0L);
        this.mockMvc.perform(put("http://localhost:" + port + BASE_URL)
                                 .contentType(MediaType.APPLICATION_JSON)
                                 .content("""
                                              {
                                                "tenantId": 1,
                                                "name": "Updated Tenant",
                                                "tenantCode": "ABC"
                                              }
                                              """))
                    .andDo(print())
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.status").value("SUCCESS"));
    }

    @Test
    void updateTenant_ThrowsBadRequestException() throws Exception {
        Tenant tenant = new Tenant();
        tenant.setTenantId(1);
        when(tenantRepository.findByTenantId(eq(1))).thenReturn(Optional.of(tenant));
        when(tenantRepository.getCountTenantByCodeAndNotIdIncludingSuspended(eq("ABC"), eq(1))).thenReturn(1L);
        this.mockMvc.perform(put("http://localhost:" + port + BASE_URL)
                                 .contentType(MediaType.APPLICATION_JSON)
                                 .content("""
                                              {
                                                "tenantId": 1,
                                                "name": "Updated Tenant",
                                                "tenantCode": "ABC"
                                              }
                                              """))
                    .andDo(print())
                    .andExpect(status().isBadRequest())
                    .andExpect(jsonPath("$.status").value("FAILED"))
                    .andExpect(jsonPath("$.responseCode.code").value(RC_400_005.getCode()));
    }

    @Test
    void deleteTenant_Success() throws Exception {
        Tenant tenant = new Tenant();
        when(tenantRepository.findByTenantId(eq(1))).thenReturn(Optional.of(tenant));
        doNothing().when(tenantRepository).delete(any());

        // Perform DELETE request
        this.mockMvc.perform(delete("http://localhost:" + port + BASE_URL + "/{id}", 1))
                    .andDo(print())
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.status").value("SUCCESS"))
                    .andExpect(jsonPath("$.responseCode.code").value(RC_200_000.getCode()))
                    .andExpect(jsonPath("$.data").value(1));

        verify(tenantRepository, times(1)).delete(any());
    }

    @Test
    void deleteTenant_ThrowsResourceNotFoundException() throws Exception {
        when(tenantRepository.findByTenantId(eq(1))).thenReturn(Optional.empty());

        this.mockMvc.perform(delete("http://localhost:" + port + BASE_URL + "/{id}", 1))
                    .andDo(print())
                    .andExpect(status().isNotFound())
                    .andExpect(jsonPath("$.status").value("FAILED"))
                    .andExpect(jsonPath("$.responseCode.code").value(RC_404_000.getCode()));
    }

    @Test
    void getTenant_Success() throws Exception {
        Tenant tenant = new Tenant();
        tenant.setTenantId(1);
        tenant.setName("Tenant 1");
        when(tenantRepository.findByTenantId(eq(1))).thenReturn(Optional.of(tenant));

        this.mockMvc.perform(get("http://localhost:" + port + BASE_URL + "/{id}", 1))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.status").value("SUCCESS"))
                    .andExpect(jsonPath("$.responseCode.code").value(RC_200_000.getCode()))
                    .andExpect(jsonPath("$.data.tenantId").value(1))
                    .andExpect(jsonPath("$.data.name").value("Tenant 1"));
    }
}
