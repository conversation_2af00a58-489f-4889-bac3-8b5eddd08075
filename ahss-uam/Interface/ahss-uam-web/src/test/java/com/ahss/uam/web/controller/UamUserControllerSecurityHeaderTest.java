package com.ahss.uam.web.controller;

import static com.ahss.common.api.AHSSResponseCode.RC_200_000;
import static com.ahss.common.api.AHSSResponseCode.RC_404_005;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.ahss.common.api.ResponseStatus;
import com.ahss.common.utils.cryptography.KeyGeneratorUtil;
import com.ahss.common.utils.cryptography.SignerType;
import com.ahss.uam.config.CustomAuthenticationManagerResolver;
import com.ahss.uam.config.JwtDecoderFactory;
import com.ahss.uam.core.client.ProductPortalClient;
import com.ahss.uam.core.service.JwtHelperService;
import com.ahss.uam.core.service.UamAdminTokenService;
import com.ahss.uam.core.service.hmac.HMACService;
import com.ahss.uam.core.service.hmac.HMACServiceFactory;
import com.ahss.uam.core.service.impl.JwtHelperServiceImpl;
import com.ahss.uam.db.model.StateCache;
import com.ahss.uam.dm.builder.JwtTokenBuilder;
import com.ahss.uam.dm.dto.ProductRedirectUrlDTO;
import com.ahss.uam.dm.dto.StateTokenDTO;
import com.ahss.uam.dm.dto.product.ProductConfigDTO;
import com.ahss.uam.dm.dto.product.ProductDTO;
import com.ahss.uam.dm.dto.product.enums.ProductConfigType;
import com.nimbusds.jose.JOSEException;
import java.security.NoSuchAlgorithmException;
import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.security.oauth2.resource.OAuth2ResourceServerProperties;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.http.MediaType;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken;
import org.springframework.web.context.WebApplicationContext;
import org.testcontainers.shaded.com.fasterxml.jackson.databind.ObjectMapper;


class UamUserControllerSecurityHeaderTest extends BaseControllerWithSecurityAndFilterTest {

    private static final String BASE_URL = "/v1/api/uam/user";

    @Value("${ahpp.secret-key}")
    private String secretKey;

    @Autowired
    private WebApplicationContext context;

    @Autowired
    private CustomAuthenticationManagerResolver customAuthenticationManagerResolver;

    @MockBean
    private UamAdminTokenService uamAdminTokenService;

    @MockBean
    private HMACServiceFactory hmacServiceFactory;

    @MockBean
    private ProductPortalClient productPortalClient;


    private final String xApiKey;
    private JwtAuthenticationToken authentication;

    private final ObjectMapper objectMapper = new ObjectMapper();

    public UamUserControllerSecurityHeaderTest() throws NoSuchAlgorithmException {
        this.xApiKey = KeyGeneratorUtil
            .generateSecretKey()
            .getBase64Encoded();
    }

    @TestConfiguration
    static class TestContextConfiguration {

        @Bean
        @Primary
        public JwtDecoderFactory jwtDecoderFactory(JwtHelperService jwtHelperService) {
            return new TestJwtDecoderFactory(jwtHelperService);
        }

        @Bean
        public JwtHelperService jwtHelperService(@Value("${ahpp.secret-key}") String secretKey) throws JOSEException {
            return new JwtHelperServiceImpl(secretKey);
        }

    }

    @Test
    void testGetRedirectUrl() throws Exception {
        String appId = "app-id";
        String requestRedirectUrl = "http://product.com/redirect";

        ProductRedirectUrlDTO request = ProductRedirectUrlDTO
            .builder()
            .redirectUrl(requestRedirectUrl)
            .build();
        JwtTokenBuilder jwtTokenBuilder = JwtTokenBuilder
            .builder()
            .algo("HS256")
            .issuer("AHPP")
            .subject("system")
            .email("<EMAIL>")
            .name("System")
            .memberOf(List.of("ADMIN", "AHIS_AIA_HK_ADMIN"))
            .scopes(List.of("openid", "profile", "email", "uam.user"))
            .build();
        String mockSystemJwt = jwtHelperService.generateToken(jwtTokenBuilder, SignerType.SYSTEM);
        when(uamAdminTokenService.getSecretKeyByApiKey(xApiKey, mockSystemJwt)).thenReturn(secretKey);

        HMACService hmacService = mock(HMACService.class);
        String expectedState = "expected-state";
        String expectedSignature = "expected-signature";
        String expectedTimestamp = "expected-timestamp";
        StateTokenDTO stateTokenDTO = StateTokenDTO
            .builder()
            .state(expectedState)
            .signature(expectedSignature)
            .timestamp(expectedTimestamp)
            .build();
        when(hmacServiceFactory.getHMACService(appId)).thenReturn(hmacService);
        when(hmacService.generateHMAC(anyString())).thenReturn(stateTokenDTO);

        String productUrl = "http://product.com";
        ProductDTO productDTO =
            ProductDTO
                .builder()
                .productConfigs(List.of(ProductConfigDTO
                                            .builder()
                                            .configType(ProductConfigType.URL)
                                            .configValue(productUrl)
                                            .build()))
                .build();
        when(productPortalClient.getProductFromPPService(eq(appId), anyList(), eq(mockSystemJwt))).thenReturn(productDTO);

        String expectedRedirectUrl =
            requestRedirectUrl
                + "?state="
                + expectedState
                + "&timestamp="
                + expectedTimestamp
                + "&signature="
                + expectedSignature
                + "&appId"
                + "="
                + appId;

        this.mockMvc
            .perform(post("http://localhost:" + port + BASE_URL + "/generate-state-token/" + appId)
                         .contentType(MediaType.APPLICATION_JSON)
                         .header("X-API-Key", xApiKey)
                         .header("Authorization", "Bearer " + mockSystemJwt)
                         .content(objectMapper.writeValueAsString(request)))
            .andExpect(status().isOk())
            .andDo(print())
            .andExpect(jsonPath("$.status").value(ResponseStatus.SUCCESS.name()))
            .andExpect(jsonPath("$.responseCode.code").value(RC_200_000.getCode()))
            .andExpect(jsonPath("$.data").value(expectedRedirectUrl));
    }

    @Test
    void testValidateStateToken() throws Exception {
        // Given
        String expectedAccessToken = "access-token";
        final String stateToken = "valid-token";
        StateTokenDTO stateTokenDTO = new StateTokenDTO(stateToken);
        var builder = JwtTokenBuilder
            .builder()
            .algo("HS256")
            .issuer("AHPP")
            .subject("system")
            .email("<EMAIL>")
            .name("System")
            .memberOf(List.of("SYSTEM_USER"))
            .scopes(List.of("AH_INTERNAL_CLIENT"))
            .expirationSeconds(31_536_000) // One year
            .build();

        var mockSystemJwt = jwtHelperService
            .generateToken(builder, SignerType.SYSTEM);

        when(uamAdminTokenService.getSecretKeyByApiKey(xApiKey, mockSystemJwt)).thenReturn(secretKey);

        // Mock sequential responses for the repository
        when(stateCacheRepository.findByStateAndUsedAndExpiresAtGreaterThan(
            eq(stateTokenDTO.getState()),
            eq(false),
            any()))
            .thenReturn(
                // First call returns unused token
                Optional.of(StateCache
                                .builder()
                                .state(stateTokenDTO.getState())
                                .accessToken(expectedAccessToken)
                                .used(false)
                                .build()),
                // Second call returns empty optional
                Optional.empty());

        // When/Then
        this.mockMvc
            .perform(post("http://localhost:" + port + BASE_URL + "/validate-state-token")
                         .contentType(MediaType.APPLICATION_JSON)
                         .header("X-API-Key", xApiKey)
                         .header("Authorization", "Bearer " + mockSystemJwt)
                         .content("""
                                      {
                                          "state": "valid-token"
                                      }
                                      """))
            .andExpect(status().isOk())
            .andDo(print())
            .andExpect(jsonPath("$.status").value(ResponseStatus.SUCCESS.name()))
            .andExpect(jsonPath("$.responseCode.code").value(RC_200_000.getCode()))
            .andExpect(jsonPath("$.data.accessToken").value(expectedAccessToken));

        // assert if key got stored correctly in the cache
        var item = customAuthenticationManagerResolver
            .getAuthManagerCache()
            .get("server::" + xApiKey);
        Assertions.assertNotNull(item);

        // When/Then 2nd time call should fail
        this.mockMvc
            .perform(post("http://localhost:" + port + BASE_URL + "/validate-state-token")
                         .contentType(MediaType.APPLICATION_JSON)
                         .header("X-API-Key", xApiKey)
                         .header("Authorization", "Bearer " + mockSystemJwt)
                         .content("""
                                      {
                                          "state": "valid-token"
                                      }
                                      """))
            .andExpect(status().isNotFound())
            .andDo(print())
            .andExpect(jsonPath("$.status").value(ResponseStatus.FAILED.name()))
            .andExpect(jsonPath("$.responseCode.code").value(RC_404_005.getCode()));
    }
}
