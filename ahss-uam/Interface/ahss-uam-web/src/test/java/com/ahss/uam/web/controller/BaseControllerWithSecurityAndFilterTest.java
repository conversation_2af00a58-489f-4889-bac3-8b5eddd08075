package com.ahss.uam.web.controller;

import com.ahss.uam.config.JwtDecoderFactory;
import com.ahss.uam.core.service.AESEncryptionService;
import com.ahss.uam.core.service.JwtHelperService;
import com.ahss.uam.core.service.UamAdminTokenService;
import com.ahss.uam.core.service.UamEntityMappingService;
import com.ahss.uam.core.service.UamOrganizationService;
import com.ahss.uam.core.service.UamPublicService;
import com.ahss.uam.core.service.UamTenantService;
import com.ahss.uam.core.service.UamUserService;
import com.ahss.uam.db.repository.ClientConfigRepository;
import com.ahss.uam.db.repository.EntityMappingRepository;
import com.ahss.uam.db.repository.OrganizationRepository;
import com.ahss.uam.db.repository.ProfileRepository;
import com.ahss.uam.db.repository.SsoProviderRepository;
import com.ahss.uam.db.repository.StateCacheRepository;
import com.ahss.uam.db.repository.TenantConfigRepository;
import com.ahss.uam.db.repository.TenantRepository;
import com.ahss.uam.db.repository.UserEntityRepository;
import com.nimbusds.jose.JOSEObjectType;
import com.nimbusds.jose.JWSAlgorithm;
import com.nimbusds.jose.jwk.JWK;
import com.nimbusds.jose.jwk.JWKSet;
import com.nimbusds.jose.proc.DefaultJOSEObjectTypeVerifier;
import com.nimbusds.jose.proc.JWSKeySelector;
import com.nimbusds.jose.proc.JWSVerificationKeySelector;
import com.nimbusds.jose.proc.SecurityContext;
import com.nimbusds.jwt.proc.DefaultJWTProcessor;
import jakarta.persistence.EntityManager;
import jakarta.persistence.EntityManagerFactory;
import java.text.ParseException;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashSet;
import java.util.Set;
import javax.sql.DataSource;
import org.jetbrains.annotations.NotNull;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.flyway.FlywayAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.jooq.JooqAutoConfiguration;
import org.springframework.boot.autoconfigure.security.oauth2.resource.OAuth2ResourceServerProperties;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.web.server.LocalServerPort;
import org.springframework.data.jpa.mapping.JpaMetamodelMappingContext;
import org.springframework.security.oauth2.core.DelegatingOAuth2TokenValidator;
import org.springframework.security.oauth2.core.OAuth2TokenValidator;
import org.springframework.security.oauth2.core.OAuth2TokenValidatorResult;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.jwt.JwtClaimNames;
import org.springframework.security.oauth2.jwt.JwtClaimValidator;
import org.springframework.security.oauth2.jwt.JwtDecoder;
import org.springframework.security.oauth2.jwt.JwtTimestampValidator;
import org.springframework.security.oauth2.jwt.JwtValidators;
import org.springframework.security.oauth2.jwt.NimbusJwtDecoder;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@EnableAutoConfiguration(exclude = {FlywayAutoConfiguration.class, DataSourceAutoConfiguration.class, JooqAutoConfiguration.class})
@AutoConfigureMockMvc(addFilters = true)
@MockBean(classes = {JpaMetamodelMappingContext.class, EntityManagerFactory.class, EntityManager.class})
public abstract class BaseControllerWithSecurityAndFilterTest {

    @LocalServerPort
    protected int port;

    @MockBean
    protected DataSource dataSource;

    @Autowired
    protected MockMvc mockMvc;

    // Mock Repository
    @MockBean
    protected TenantRepository tenantRepository;

    @MockBean
    protected OrganizationRepository organizationRepository;

    @MockBean
    protected UserEntityRepository userEntityRepository;

    @MockBean
    protected EntityMappingRepository entityMappingRepository;

    @MockBean
    protected SsoProviderRepository ssoProviderRepository;

    @MockBean
    protected StateCacheRepository stateCacheRepository;

    @MockBean
    protected TenantConfigRepository tenantConfigRepository;

    @MockBean
    protected ClientConfigRepository clientConfigRepository;

    // Autowired Services
    @Autowired
    protected UamEntityMappingService uamEntityMappingService;

    @Autowired
    protected UamOrganizationService uamOrganizationService;

    @Autowired
    protected UamTenantService uamTenantService;

    @Autowired
    protected UamUserService uamUserService;

    @Autowired
    protected UamPublicService uamPublicService;

    @Autowired
    protected JwtHelperService jwtHelperService;

    @Autowired
    protected UamAdminTokenService uamAdminTokenService;

    @Autowired
    protected AESEncryptionService aesEncryptionService;

    @MockBean
    protected ProfileRepository profileRepository;

    public static class TestJwtDecoderFactory extends JwtDecoderFactory {

        private final JwtHelperService jwtHelperService;

        public TestJwtDecoderFactory(JwtHelperService jwtHelperService) {
            this.jwtHelperService = jwtHelperService;
        }


        @Override
        public JwtDecoder getClientJwtDecoder(String issuer, String jwkSetUri, String audience) {
            try {
                JWK jwk = JWK.parse(jwtHelperService.getPublicJwk());
                DefaultJWTProcessor<SecurityContext> jwtProcessor = getSecurityContextDefaultJWTProcessor(jwk);

                // Create decoder with the processor
                NimbusJwtDecoder decoder = new NimbusJwtDecoder(jwtProcessor);

                // Add validators
                OAuth2TokenValidator<Jwt> validator = new DelegatingOAuth2TokenValidator<>(
                    JwtValidators.createDefaultWithIssuer(issuer),
                    audienceValidator(audience),
                    getDefaultValidators()
                );
                decoder.setJwtValidator(validator);

                return decoder;
            } catch (ParseException e) {
                System.out.println(e.getMessage());
                throw new IllegalStateException("Unable to create Okta-like JWT Decoder");
            }
        }

        private @NotNull DefaultJWTProcessor<SecurityContext> getSecurityContextDefaultJWTProcessor(JWK jwk) {
            JWKSet jwkSet = new JWKSet(jwk);

            DefaultJWTProcessor<SecurityContext> jwtProcessor = new DefaultJWTProcessor<>();

            // Configure type verification
            Set<JOSEObjectType> types = new HashSet<>(Arrays.asList(
                JOSEObjectType.JWT,
                new JOSEObjectType("at+jwt")
                                                                   ));
            jwtProcessor.setJWSTypeVerifier(new DefaultJOSEObjectTypeVerifier<>(types));

            // Configure key selector
            JWSKeySelector<SecurityContext> keySelector =
                new JWSVerificationKeySelector<>(
                    JWSAlgorithm.RS256,
                    (headerMap, context) -> jwkSet.getKeys()
                );
            jwtProcessor.setJWSKeySelector(keySelector);
            return jwtProcessor;
        }

        private OAuth2TokenValidator<Jwt> audienceValidator(String audience) {
            return new JwtClaimValidator<Collection<String>>(
                JwtClaimNames.AUD,
                aud -> aud != null && aud.contains(audience)
            );
        }

        private OAuth2TokenValidator<Jwt> getDefaultValidators() {
            return new DelegatingOAuth2TokenValidator<>(
                new JwtTimestampValidator(),
                token -> OAuth2TokenValidatorResult.success()
            );
        }
    }

}
