package com.ahss.uam.web.controller;

import static com.ahss.common.api.AHSSResponseCode.RC_200_000;
import static com.ahss.common.api.AHSSResponseCode.RC_404_000;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.restdocs.mockmvc.RestDocumentationRequestBuilders.delete;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.ahss.uam.db.model.Organization;
import com.ahss.uam.db.model.Tenant;
import com.ahss.uam.dm.dto.OrganizationDTO;
import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.Test;
import org.springframework.http.MediaType;

class UamOrganizationControllerTest extends BaseControllerTest {

    private static final String BASE_URL = "/v1/api/uam/organization";

    @Test
    void getAllOrganizations() throws Exception {
        // Mock service and mapper behavior
        Organization organization = new Organization();
        organization.setOrgId(1);
        organization.setName("Organization 1");

        OrganizationDTO organizationDTO = new OrganizationDTO();
        organizationDTO.setOrgId(1);
        organizationDTO.setName("Organization 1");

        when(organizationRepository.findAll()).thenReturn(List.of(organization));

        // Perform GET request and assert response
        this.mockMvc.perform(get("http://localhost:" + port + BASE_URL))
                    .andDo(print())
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.status").value("SUCCESS"))
                    .andExpect(jsonPath("$.responseCode.code").value(RC_200_000.getCode()))
                    .andExpect(jsonPath("$.data").isNotEmpty())
                    .andExpect(jsonPath("$.data[0].orgId").value(1))
                    .andExpect(jsonPath("$.data[0].name").value("Organization 1"));
    }


    @Test
    void updateOrganization_Success() throws Exception {
        OrganizationDTO organizationDTO = new OrganizationDTO();
        organizationDTO.setOrgId(1);
        organizationDTO.setName("Updated Organization");

        Organization organization = new Organization();
        organization.setName("Old Name");
        organization.setOrgId(1);
        when(organizationRepository.findByOrgId(eq(1))).thenReturn(Optional.of(organization));
        when(organizationRepository.save(any())).thenReturn(organization);

        // Perform PUT request
        this.mockMvc.perform(put("http://localhost:" + port + BASE_URL)
                                 .contentType(MediaType.APPLICATION_JSON)
                                 .content("""
                                              {
                                                "orgId": 1,
                                                "name": "Updated Organization"
                                              }
                                              """))
                    .andDo(print())
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.status").value("SUCCESS"))
                    .andExpect(jsonPath("$.responseCode.code").value(RC_200_000.getCode()))
                    .andExpect(jsonPath("$.data.name").value("Updated Organization"));
    }

    @Test
    void deleteOrganization_Success() throws Exception {
        Organization organization = new Organization();
        Tenant tenant = new Tenant();
        organization.setTenant(tenant);
        when(organizationRepository.findByOrgId(eq(1))).thenReturn(Optional.of(organization));
        when(tenantRepository.save(any())).thenReturn(tenant);
        doNothing().when(organizationRepository).delete(any());

        // Perform DELETE request
        this.mockMvc.perform(delete("http://localhost:" + port + BASE_URL + "/{id}", 1))
                    .andDo(print())
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.status").value("SUCCESS"))
                    .andExpect(jsonPath("$.responseCode.code").value(RC_200_000.getCode()))
                    .andExpect(jsonPath("$.data").value(1));

        verify(organizationRepository, times(1)).delete(any());
    }

    @Test
    void deleteOrganization_ThrowsResourceNotFoundException() throws Exception {
        when(organizationRepository.findByOrgId(eq(1))).thenReturn(Optional.empty());

        this.mockMvc.perform(delete("http://localhost:" + port + BASE_URL + "/{id}", 1))
                    .andDo(print())
                    .andExpect(status().isNotFound())
                    .andExpect(jsonPath("$.status").value("FAILED"))
                    .andExpect(jsonPath("$.responseCode.code").value(RC_404_000.getCode()));
    }

    @Test
    void getOrganization_Success() throws Exception {
        Organization organization = new Organization();
        organization.setOrgId(1);
        organization.setName("Organization 1");
        when(organizationRepository.findByOrgId(eq(1))).thenReturn(Optional.of(organization));

        this.mockMvc.perform(get("http://localhost:" + port + BASE_URL + "/{id}", 1))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.status").value("SUCCESS"))
                    .andExpect(jsonPath("$.responseCode.code").value(RC_200_000.getCode()))
                    .andExpect(jsonPath("$.data.orgId").value(1))
                    .andExpect(jsonPath("$.data.name").value("Organization 1"));
    }
}
