package com.ahss.uam.web.controller;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.delete;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.ahss.uam.db.model.EntityMapping;
import com.ahss.uam.db.model.UserEntity;
import com.ahss.uam.dm.dto.EntityMappingDTO;
import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.http.MediaType;


class UamEntityMappingControllerTest extends BaseControllerTest {

    private static final String BASE_URL = "/v1/api/uam/user/{entityId}/entitymapping";

    private EntityMappingDTO entityMappingDTO;

    @BeforeEach
    void setUp() {
        entityMappingDTO = EntityMappingDTO.builder()
                                           .entityMappingsId(1L)
                                           .systemId("systemId")
                                           .name("name")
                                           .externalIdentifier("externalIdentifier")
                                           .build();

    }

    @Test
    void testCreateEntityMapping() throws Exception {
        when(userEntityRepository.findById(1L)).thenReturn(Optional.of(UserEntity.builder().entityId(1L).build()));

        when(entityMappingRepository.save(any())).thenReturn(
            EntityMapping.builder().name("name").externalIdentifier("externalIdentifier").systemId("systemId").build());

        this.mockMvc.perform(post("http://localhost:" + port + BASE_URL.replace("{entityId}", "1"))
                                 .contentType(MediaType.APPLICATION_JSON)
                                 .content("""
                                              {
                                                "name": "name",
                                                "externalIdentifier": "externalIdentifier",
                                                "systemId": "systemId"
                                              }
                                              """)
                                 .accept(MediaType.APPLICATION_JSON))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.data.name").value("name"))
                    .andExpect(jsonPath("$.data.externalIdentifier").value("externalIdentifier"))
                    .andExpect(jsonPath("$.data.systemId").value("systemId"));
    }

    @Test
    void testUpdateEntityMapping() throws Exception {
        UserEntity userEntity = UserEntity.builder().entityId(1L).build();
        EntityMapping entityMapping = EntityMapping.builder().name("name").externalIdentifier("externalIdentifier").systemId("systemId").build();
        userEntity.setEntityMappings(List.of(entityMapping));
        entityMapping.setUserEntity(userEntity);
        when(userEntityRepository.findById(1L)).thenReturn(Optional.of(userEntity));
        when(entityMappingRepository.findById(1L)).thenReturn(Optional.of(entityMapping));
        when(userEntityRepository.save(any())).thenReturn(userEntity);

        this.mockMvc.perform(put("http://localhost:" + port + BASE_URL.replace("{entityId}", "1") + "/1")
                                 .contentType(MediaType.APPLICATION_JSON)
                                 .content("""
                                              {
                                              "entityMappingsId": 1,
                                                "name": "name2",
                                                "externalIdentifier": "externalIdentifier2",
                                                "systemId": "systemId2"
                                              }
                                              """)
                                 .accept(MediaType.APPLICATION_JSON))
                    .andExpect(status().isOk());
    }

    @Test
    void testUpdateEntityMappings() throws Exception {
        UserEntity userEntity = UserEntity.builder().entityId(1L).build();
        EntityMapping entityMapping = EntityMapping.builder().name("name").externalIdentifier("externalIdentifier").systemId("systemId").build();
        userEntity.setEntityMappings(List.of(entityMapping));
        entityMapping.setUserEntity(userEntity);
        when(userEntityRepository.findById(1L)).thenReturn(Optional.of(userEntity));
        when(entityMappingRepository.findById(1L)).thenReturn(Optional.of(entityMapping));
        when(userEntityRepository.save(any())).thenReturn(userEntity);

        this.mockMvc.perform(put("http://localhost:" + port + BASE_URL.replace("{entityId}", "1"))
                                 .contentType(MediaType.APPLICATION_JSON)
                                 .content("""
                                              [{
                                              "entityMappingsId": 1,
                                                "name": "name2",
                                                "externalIdentifier": "externalIdentifier2",
                                                "systemId": "systemId2"
                                              }]
                                              """)
                                 .accept(MediaType.APPLICATION_JSON))
                    .andExpect(status().isOk());
    }

    @Test
    void testDeleteEntityMapping() throws Exception {
        when(entityMappingRepository.findById(1L)).thenReturn(Optional.of(EntityMapping.builder().entityMappingsId(1L).build()));
        doNothing().when(entityMappingRepository).deleteById(1L);
        mockMvc.perform(delete("http://localhost:" + port + BASE_URL.replace("{entityId}", "1") + "/1")
                            .contentType(MediaType.APPLICATION_JSON)
                            .accept(MediaType.APPLICATION_JSON))
               .andExpect(status().isOk());

    }

}
