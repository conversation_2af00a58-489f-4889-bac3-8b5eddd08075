<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.ahss.gw</groupId>
        <artifactId>ahss-gateway</artifactId>
        <version>0.0.1-SNAPSHOT</version>
        <relativePath>../../pom.xml</relativePath>
    </parent>
    <artifactId>ahss-gateway-web</artifactId>
    <packaging>jar</packaging>

    <properties>
        <version>${project.parent.version}</version>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <spring-cloud.version>2023.0.3</spring-cloud.version>
        <jib.version>3.4.2</jib.version>
        <jib.image.name>ahss-gateway-web</jib.image.name>
    </properties>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>

        <!-- Modules dependencies -->
        <dependency>
            <groupId>com.ahss.gw</groupId>
            <artifactId>ahss-gateway-common</artifactId>
            <version>${version}</version>
        </dependency>
        <dependency>
            <groupId>com.ahss.gw</groupId>
            <artifactId>ahss-gateway-config</artifactId>
            <version>${version}</version>
        </dependency>
        <dependency>
            <groupId>com.ahss.gw</groupId>
            <artifactId>ahss-gateway-core</artifactId>
            <version>${version}</version>
        </dependency>
        <dependency>
            <groupId>com.ahss.gw</groupId>
            <artifactId>ahss-gateway-domain</artifactId>
            <version>${version}</version>
        </dependency>

        <!-- Spring libraries -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-gateway</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>

        <!-- Observability -->
        <dependency>
            <groupId>io.micrometer</groupId>
            <artifactId>micrometer-registry-prometheus</artifactId>
        </dependency>
        <dependency>
            <groupId>io.micrometer</groupId>
            <artifactId>micrometer-tracing-bridge-brave</artifactId>
        </dependency>
        <!-- Jaeger Client for Tracing -->
        <dependency>
            <groupId>io.zipkin.reporter2</groupId>
            <artifactId>zipkin-reporter-brave</artifactId>
        </dependency>

        <!-- 3rd-party libraries -->
        <dependency>
            <groupId>net.logstash.logback</groupId>
            <artifactId>logstash-logback-encoder</artifactId>
            <version>6.6</version>
        </dependency>
        <dependency>
            <groupId>io.opentelemetry.instrumentation</groupId>
            <artifactId>opentelemetry-logback-appender-1.0</artifactId>
            <version>2.6.0-alpha</version>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>com.microsoft.azure</groupId>
            <artifactId>applicationinsights-runtime-attach</artifactId>
            <version>3.6.2</version>
        </dependency>

        <!-- Serialization Deserialization JSON   -->
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-core</artifactId>
            <version>2.17.2</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
            <version>2.17.2</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.datatype</groupId>
            <artifactId>jackson-datatype-jsr310</artifactId>
        </dependency>

        <!-- Testing -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-contract-stub-runner</artifactId>
        </dependency>

        <!-- WireMock for mocking HTTP services -->
        <dependency>
            <groupId>com.maciejwalkowiak.spring</groupId>
            <artifactId>wiremock-spring-boot</artifactId>
            <version>2.1.2</version>
        </dependency>
    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>com.google.cloud.tools</groupId>
                <artifactId>jib-maven-plugin</artifactId>
                <version>${jib.version}</version>
                <configuration>
                    <from>
                        <image>eclipse-temurin:21-alpine</image>
                    </from>
                    <to>
                        <image>acrah01seadahss01.azurecr.io/eclipse-temurin:21-alpine</image> <!-- Amazon Corretto 21 for Alpine Linux -->
                        <auth>
                            <username>${env.ACR_AHSS_USERNAME}</username>
                            <password>${env.ACR_AHSS_PASSWORD}</password>
                        </auth>
                    </to>
                    <container>
                        <mainClass>com.ahss.gateway.AHSSGatewayWebApplication</mainClass> <!-- Replace with your actual main class -->
                        <user>root</user> <!-- Start as root to set up permissions -->
                        <workingDirectory>/app</workingDirectory>
                        <jvmFlags>
                            <jvmFlag>-Dspring.profiles.active=prod</jvmFlag>
                        </jvmFlags>
                        <creationTime>USE_CURRENT_TIMESTAMP</creationTime>
                        <entrypoint>
                            <shell>sh</shell>
                            <arg>-c</arg>
                            <arg>adduser -D -u 1000 ahss &amp;&amp; mkdir -p /app/logs &amp;&amp; chown -R ahss:ahss /app &amp;&amp; chmod -R 755 /app &amp;&amp; chmod 777 /app/logs &amp;&amp; su -s /bin/sh ahss -c "java -cp /app/resources:/app/classes:/app/libs/* com.ahss.gateway.AHSSGatewayWebApplication"</arg>
                        </entrypoint>
                        <environment>
                            <JAVA_TOOL_OPTIONS>-XX:+UseContainerSupport -XX:MaxRAMPercentage=75.0</JAVA_TOOL_OPTIONS>
                            <SPRING_APPLICATION_NAME>ahss-gateway</SPRING_APPLICATION_NAME>
                        </environment>
                    </container>
                    <extraDirectories>
                        <paths>
                            <path>
                                <from>target</from>
                                <into>/app</into>
                            </path>
                            <path>
                                <from>src/main/resources</from>
                                <into>/app/resources</into>
                            </path>
                        </paths>
                        <permissions>
                            <permission>
                                <file>/app/docker-entrypoint.sh</file>
                                <mode>755</mode>
                            </permission>
                        </permissions>
                    </extraDirectories>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <profiles>
        <!-- Profile for Local Docker Repository -->
        <profile>
            <id>local-docker</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>com.google.cloud.tools</groupId>
                        <artifactId>jib-maven-plugin</artifactId>
                        <version>${jib.version}</version>
                        <configuration>
                            <to>
                                <image>${jib.image.name}:${version}</image>
                            </to>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>local-docker-arm</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>com.google.cloud.tools</groupId>
                        <artifactId>jib-maven-plugin</artifactId>
                        <version>${jib.version}</version>
                        <configuration>
                            <from>
                                <image>arm64v8/eclipse-temurin:21-alpine</image>
                            </from>
                            <to>
                                <image>${jib.image.name}:${version}</image>
                            </to>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <!-- Profile for Azure ACR -->
        <profile>
            <id>azure-acr</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>com.google.cloud.tools</groupId>
                        <artifactId>jib-maven-plugin</artifactId>
                        <version>${jib.version}</version>
                        <configuration>
                            <to>
                                <image>acrah01seadahss01.azurecr.io/eclipse-temurin:21-alpine</image>
                                <auth>
                                    <username>${env.ACR_AHSS_USERNAME}</username>
                                    <password>${env.ACR_AHSS_PASSWORD}</password>
                                </auth>
                            </to>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>

</project>
