package com.ahss.gateway;

import com.microsoft.applicationinsights.attach.ApplicationInsights;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

@SpringBootApplication(scanBasePackages = {"com.ahss.gateway.web", "com.ahss.gateway.common",
    "com.ahss.gateway.config", "com.ahss.gateway.core"})
public class AHSSGatewayWebApplication {

    public static void main(String[] args) {
        // As an alternative to adding Hooks.enableAutomaticContextPropagation() in the application main function,
        // spring boot created property config which you can specify in application.properties
        ApplicationInsights.attach();
        SpringApplication.run(AHSSGatewayWebApplication.class, args);
    }

}
