package com.ahss.gateway.web.controller;

import com.ahss.common.api.AHSSResponseCode;
import com.ahss.common.api.ApiResponse;
import com.ahss.common.api.ResponseStatus;
import com.ahss.gateway.core.service.CommonService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Slf4j
@RestController
@RequestMapping(value = "/v1/api")
public class CommonController {

    private final CommonService commonService;

    public CommonController(CommonService commonService) {
        this.commonService = commonService;
    }

    @GetMapping(value = "/hello")
    public Mono<ResponseEntity<ApiResponse<String, Object>>> helloWorld() {
        log.info("Processing hello world");
        var helloStr = commonService.sayHello();
        var apiResponse = ApiResponse
            .<String, Object>builder()
            .status(ResponseStatus.SUCCESS)
            .responseCode(AHSSResponseCode.RC_200_000)
            .request(null)
            .data(helloStr)
            .build();
        return Mono.just(ResponseEntity.ok(apiResponse));
    }
}