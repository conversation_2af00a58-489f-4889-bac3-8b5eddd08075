<configuration>
  <include resource="org/springframework/boot/logging/logback/defaults.xml"/>
  <springProperty scope="context" name="springAppName" source="spring.application.name"/>
  <property name="LOG_FILE" value="${BUILD_FOLDER:-build}/${springAppName}"/>

  <property name="CONSOLE_LOG_PATTERN"
            value="%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(${LOG_LEVEL_PATTERN:-%5p}) %clr([${springAppName:-},%X{traceId:-},%X{spanId:-}]){faint} %clr(${PID:- }){magenta} %clr(---){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %clr(%M) %clr(:){faint} %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}"/>

  <!-- Async appender for better performance -->
  <appender name="ASYNC" class="ch.qos.logback.classic.AsyncAppender">
    <appender-ref ref="CONSOLE"/>
    <appender-ref ref="FLAT_FILE"/>
    <queueSize>5000</queueSize>
    <discardingThreshold>0</discardingThreshold>
  </appender>

  <!-- Appender to log to console -->
  <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
    <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
      <level>DEBUG</level>
    </filter>
    <encoder>
      <pattern>${CONSOLE_LOG_PATTERN}</pattern>
      <charset>utf8</charset>
    </encoder>
  </appender>

  <!-- Appender to log to file with rotation -->
  <appender name="FLAT_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
    <file>${LOG_FILE}</file>
    <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
      <fileNamePattern>${LOG_FILE}.%d{yyyy-MM-dd}.gz</fileNamePattern>
      <maxHistory>7</maxHistory>
      <totalSizeCap>1GB</totalSizeCap>
    </rollingPolicy>
    <encoder>
      <pattern>${CONSOLE_LOG_PATTERN}</pattern>
      <charset>utf8</charset>
    </encoder>
  </appender>

  <!-- JSON appender for structured logging -->
  <appender name="JSON" class="ch.qos.logback.core.ConsoleAppender">
    <encoder class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
      <providers>
        <timestamp/>
        <logLevel/>
        <loggerName/>
        <message/>
        <mdc/>
        <context/>
        <arguments/>
        <stackTrace/>
        <customFields>{"springAppName":"${springAppName}"}</customFields>
        <custom>
          <keyValuePair>
            <key>traceId</key>
            <value>%X{traceId}</value>
          </keyValuePair>
          <keyValuePair>
            <key>spanId</key>
            <value>%X{spanId}</value>
          </keyValuePair>
        </custom>
      </providers>
    </encoder>
  </appender>

  <root level="INFO">
    <appender-ref ref="CONSOLE"/>
<!--    <appender-ref ref="JSON"/>-->
  </root>

  <!-- Example of setting a different log level for a specific package -->
  <logger name="com.example.package" level="DEBUG"/>
  <logger name="org.springframework.cloud.gateway" level="DEBUG"/>
  <logger name="reactor.netty.http.client" level="DEBUG"/>

</configuration>