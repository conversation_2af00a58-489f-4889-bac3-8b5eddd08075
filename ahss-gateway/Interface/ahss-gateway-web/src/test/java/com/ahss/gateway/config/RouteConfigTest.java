package com.ahss.gateway.config;

import static com.github.tomakehurst.wiremock.client.WireMock.aResponse;
import static com.github.tomakehurst.wiremock.client.WireMock.get;
import static com.github.tomakehurst.wiremock.client.WireMock.stubFor;
import static com.github.tomakehurst.wiremock.client.WireMock.urlPathMatching;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.autoconfigure.web.reactive.AutoConfigureWebTestClient;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.SpringBootTest.WebEnvironment;
import org.springframework.cloud.contract.wiremock.AutoConfigureWireMock;
import org.springframework.core.env.Environment;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.EnabledIf;
import org.springframework.test.web.reactive.server.WebTestClient;

@SpringBootTest(webEnvironment = WebEnvironment.RANDOM_PORT)
@AutoConfigureWebTestClient
@AutoConfigureWireMock(port = 0)
@ActiveProfiles("integration")
@EnabledIf(value = "#{'${spring.profiles.active:default}' == 'integration'}", loadContext = true)
@Slf4j
class RouteConfigTest {

    @Autowired
    private WebTestClient webTestClient;

    @Autowired
    private Environment env;

    @Value("${spring.profiles.active}")
    private String profile;

    private final String productsListResponse = """
            {
              "status": "SUCCESS",
              "responseCode": {
                "code": 20000,
                "message": "Success",
                "detailMessage": "Request has been processed successfully"
              },
              "data": [
                {
                  "id": 1,
                  "productName": "Insights Studio"
                },
                {
                  "id": 2,
                  "productName": "FWD"
                },
                {
                  "id": 3,
                  "productName": "Core Claims"
                }
              ],
              "request": null,
              "actions": null
            }
        """;

    @BeforeEach
    public void setupMocks() {
        // returns a URL to WireMockServer instance
        env.getProperty("user-client.url");


    }

    @Test
    void ahppProductsRouteTest() {
        log.info("Profile {}", profile);
        // Given
        stubFor(get(urlPathMatching("/v1/api/pp/.*"))
                    .willReturn(
                        aResponse()
                            .withHeader("Content-Type", "application/json")
                            .withBody(productsListResponse)));

        webTestClient.get().uri("/ahss-pp/v1/api/pp/products").exchange().expectStatus().isOk()
                     .expectHeader().contentType("application/json")
                     .expectBody().jsonPath("$.data[0].productName").isEqualTo("Insights Studio");
    }
}
