package com.ahss.gateway.web.controller;

import static com.ahss.common.api.AHSSResponseCode.RC_200_000;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.reactive.AutoConfigureWebTestClient;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.server.LocalServerPort;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.reactive.server.WebTestClient;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureWebTestClient
@ActiveProfiles({"default", "test"})
class CommonControllerTest {

    private static final String BASE_URL = "/v1/api";

    @LocalServerPort
    private int port;

    @Autowired
    private WebTestClient webTestClient;

    @Test
    void helloWorld() {
        webTestClient.get().uri("http://localhost:" + port + BASE_URL + "/hello").exchange().expectStatus().isOk()
                     .expectBody()
                     .jsonPath("$.status").isEqualTo("SUCCESS")
                     .jsonPath("$.responseCode.code").isEqualTo(RC_200_000.getCode())
                     .jsonPath("$.data").isEqualTo("hello world");
    }
}