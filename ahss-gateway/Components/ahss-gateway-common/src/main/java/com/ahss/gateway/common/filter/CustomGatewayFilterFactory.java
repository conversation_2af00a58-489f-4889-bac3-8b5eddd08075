package com.ahss.gateway.common.filter;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.gateway.filter.GatewayFilter;
import org.springframework.cloud.gateway.filter.factory.AbstractGatewayFilterFactory;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

@Slf4j
@Component
public class CustomGatewayFilterFactory extends
    AbstractGatewayFilterFactory<CustomGatewayFilterFactory.Config> {

    public CustomGatewayFilterFactory() {
        super(Config.class);
    }

    @Override
    public GatewayFilter apply(Config config) {
        return (exchange, chain) -> {
            // Pre-processing
            if (config.isPreLogger()) {
                log.info("Pre GatewayFilter logging: {}", config.getBaseMessage());
            }
            return chain.filter(exchange)
                        .then(Mono.fromRunnable(() -> {
                            // Post-processing
                            if (config.isPostLogger()) {
                                log.info("Post GatewayFilter logging: {}", config.getBaseMessage());
                            }
                        }));
        };
    }

    /**
     * Config can be set in application.yml
     */
    @Data
    @AllArgsConstructor
    @Builder
    public static class Config {
        private String baseMessage;
        private boolean preLogger;
        private boolean postLogger;
    }
}