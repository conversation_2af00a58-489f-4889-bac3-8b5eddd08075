package com.ahss.gateway.common.utils;


import org.reactivestreams.Subscription;
import org.slf4j.Logger;
import org.slf4j.MDC;
import reactor.core.CoreSubscriber;
import reactor.util.context.Context;

public class LoggingSubscriber<T> implements CoreSubscriber<T> {

    private final CoreSubscriber<T> delegate;
    private final Logger logger;

    public LoggingSubscriber(CoreSubscriber<T> delegate, Logger logger) {
        this.delegate = delegate;
        this.logger = logger;
    }

    @Override
    public void onSubscribe(Subscription s) {
        delegate.onSubscribe(s);
    }

    @Override
    public void onNext(T t) {
        addToMdc(delegate.currentContext());
        delegate.onNext(t);
    }

    @Override
    public void onError(Throwable t) {
        addToMdc(delegate.currentContext());
        delegate.onError(t);
    }

    @Override
    public void onComplete() {
        addToMdc(delegate.currentContext());
        delegate.onComplete();
    }

    @Override
    public Context currentContext() {
        return delegate.currentContext();
    }

    private void addToMdc(Context context) {
        context.getOrEmpty("traceId")
               .ifPresent(traceId -> MDC.put("traceId", traceId.toString()));
        context.getOrEmpty("spanId")
               .ifPresent(spanId -> MDC.put("spanId", spanId.toString()));
    }
}