package com.ahss.gateway.common.handler;

import com.ahss.common.api.AHSSResponseCode;
import com.ahss.common.api.ApiResponse;
import com.ahss.common.api.ResponseStatus;
import com.ahss.common.domain.exception.ResourceNotFoundException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

@Slf4j
@ControllerAdvice
public class GlobalReactiveExceptionHandler {

    @ExceptionHandler(ResourceNotFoundException.class)
    @org.springframework.web.bind.annotation.ResponseStatus(HttpStatus.NOT_FOUND)
    public Mono<ResponseEntity<ApiResponse<String, Object>>> handleResourceNotFoundException(ResourceNotFoundException ex,
                                                                                             ServerWebExchange exchange) {
        return Mono.just(ResponseEntity.status(HttpStatus.NOT_FOUND).body(
            ApiResponse.<String, Object>builder()
                       .status(ResponseStatus.FAILED)
                       .responseCode(AHSSResponseCode.RC_404_000)
                       .data(AHSSResponseCode.RC_404_000.getDetailMessage()).build()));
    }

    // Handle all other exceptions
    @ExceptionHandler(Exception.class)
    @org.springframework.web.bind.annotation.ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public Mono<ResponseEntity<ApiResponse<String, Object>>> handleGlobalException(Exception ex, ServerWebExchange exchange) {
        return Mono.just(ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(
            ApiResponse.<String, Object>builder()
                       .status(ResponseStatus.FAILED)
                       .responseCode(AHSSResponseCode.RC_500_000)
                       .data(AHSSResponseCode.RC_500_000.getDetailMessage()).build()));
    }
}