package com.ahss.gateway.common.handler;

import com.ahss.common.api.AHSSResponseCode;
import com.ahss.common.api.ResponseStatus;
import com.ahss.common.domain.exception.ResourceNotFoundException;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.AutoConfigurationExcludeFilter;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.context.TypeExcludeFilter;
import org.springframework.boot.test.autoconfigure.web.reactive.AutoConfigureWebTestClient;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.server.LocalServerPort;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.ComponentScan.Filter;
import org.springframework.context.annotation.FilterType;
import org.springframework.stereotype.Controller;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.web.reactive.server.WebTestClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import reactor.core.publisher.Mono;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ContextConfiguration(classes = {GlobalReactiveExceptionHandlerTest.TestController.class})
@EnableAutoConfiguration
@ComponentScan(
    excludeFilters = {@Filter(
        type = FilterType.CUSTOM,
        classes = {TypeExcludeFilter.class}
    ), @Filter(
        type = FilterType.CUSTOM,
        classes = {AutoConfigurationExcludeFilter.class}
    )}
)
@AutoConfigureWebTestClient
class GlobalReactiveExceptionHandlerTest {

    private static final String BASE_PATH = "/v1/api/error";
    @LocalServerPort
    private int port;

    @Autowired
    private WebTestClient webTestClient;

    @Test
    void handleResourceNotFoundException() {
        webTestClient.get().uri(BASE_PATH + "/notfound").exchange().expectStatus().isNotFound().expectBody()
                     .jsonPath("$.status").isEqualTo(ResponseStatus.FAILED.name())
                     .jsonPath("$.responseCode.code").isEqualTo(AHSSResponseCode.RC_404_000.getCode());
    }

    @Test
    void handleGlobalException() {
        webTestClient.get().uri(BASE_PATH + "/500").exchange().expectStatus().is5xxServerError().expectBody()
                     .jsonPath("$.status").isEqualTo(ResponseStatus.FAILED.name())
                     .jsonPath("$.responseCode.code").isEqualTo(AHSSResponseCode.RC_500_000.getCode());
    }

    @Controller
    @RequestMapping(BASE_PATH)
    public static class TestController {

        @GetMapping("/notfound")
        public Mono<String> error() {
            throw new ResourceNotFoundException();
        }

        @GetMapping("/500")
        public Mono<String> error500() throws Exception {
            throw new Exception("Internal Server Error");
        }
    }
}