package com.ahss.gateway.config;

import com.ahss.gateway.common.filter.CustomGatewayFilterFactory;
import com.ahss.gateway.common.filter.CustomGatewayFilterFactory.Config;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.gateway.route.RouteLocator;
import org.springframework.cloud.gateway.route.builder.RouteLocatorBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Slf4j
@Configuration
public class RouteConfig {

    @Value("${ahss.ahpp.path:/v1/api/pp/**}")
    private String ahppApiPrefixPath;

    @Value("${ahss.ahpp.stripPrefix:0}")
    private int ppStripPrefix;

    @Value("${ahss.ahpp.uri.scheme}://${ahss.ahpp.uri.host}:${ahss.ahpp.uri.port}")
    private String ahppUri;

    @Value("${ahss.uam.path:/v1/api/uam/**}")
    private String uamApiPrefixPath;

    @Value("${ahss.uam.stripPrefix:0}")
    private int uamStripPrefix;

    @Value("${ahss.uam.uri.scheme}://${ahss.uam.uri.host}:${ahss.uam.uri.port}")
    private String uamUri;

    @Bean
    public RouteLocator routes(RouteLocatorBuilder builder, CustomGatewayFilterFactory customGatewayFilterFactory) {

        return builder.routes()
                      .route(rs -> rs.path(ahppApiPrefixPath)
                                     .filters(f -> f.stripPrefix(ppStripPrefix).filter(customGatewayFilterFactory.apply(
                                         // You can add custom logging messages or logic by updating CustomGatewayFilterFactory.
                                         // Default to false
                                         Config.builder().baseMessage("Custom Logging").preLogger(false).postLogger(false).build())))
                                     .uri(ahppUri))
                      .route(rs -> rs.path(uamApiPrefixPath)
                                     .filters(f -> f.stripPrefix(uamStripPrefix).filter(customGatewayFilterFactory.apply(
                                         // You can add custom logging messages or logic by updating CustomGatewayFilterFactory.
                                         // Default to false
                                         Config.builder().baseMessage("Custom Logging").preLogger(false).postLogger(false).build())))
                                     .uri(uamUri))
                      .build();
    }

}
