package com.ahss.gateway.config;

import com.ahss.gateway.common.utils.LoggingSubscriber;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import reactor.core.publisher.Hooks;
import reactor.core.publisher.Operators;

@Configuration
public class LoggingConfig {

    @Bean
    public Logger reactorLogger() {
        Logger logger = LoggerFactory.getLogger("reactor.netty.http.client");

        Hooks.onEachOperator(Operators.lift((scannable, coreSubscriber) ->
                                                new LoggingSubscriber<>(coreSubscriber, logger)));

        return logger;
    }
}