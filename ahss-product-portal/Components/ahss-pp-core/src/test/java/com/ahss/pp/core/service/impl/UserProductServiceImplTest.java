package com.ahss.pp.core.service.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.ahss.common.domain.context.UserContext;
import com.ahss.common.domain.context.UserContextHolder;
import com.ahss.common.domain.exception.ResourceNotFoundException;
import com.ahss.pp.db.model.Product;
import com.ahss.pp.db.model.UserProduct;
import com.ahss.pp.db.repository.ProductRepository;
import com.ahss.pp.db.repository.UserProductRepository;
import com.ahss.pp.dm.dto.ProductFavoriteRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockitoAnnotations;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.ImportAutoConfiguration;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@ExtendWith(SpringExtension.class)
@ImportAutoConfiguration(classes = {UserProductServiceImpl.class})
class UserProductServiceImplTest {

    @Autowired
    private UserProductServiceImpl userProductService;

    @MockBean
    private UserProductRepository userProductRepository;

    @MockBean
    private ProductRepository productRepository;

    private UserContext userContext;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.initMocks(this);
        userContext = UserContext.builder().sub("user123").build();
        UserContextHolder.setUserContext(userContext);
    }

    @Test
    void getAllProducts_ReturnsUserFavorites() {
        // Arrange
        List<UserProduct> userProducts = new ArrayList<>();
        userProducts.add(new UserProduct());
        when(userProductRepository.findByUidAndIsFavorite("user123", true)).thenReturn(userProducts);

        // Act
        List<UserProduct> result = userProductService.getFavoriteProducts();

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        verify(userProductRepository, times(1)).findByUidAndIsFavorite("user123", true);
    }

    @Test
    void favoriteProduct_ProductExists_UserProductExists() {
        // Arrange
        ProductFavoriteRequest request = new ProductFavoriteRequest();
        request.setProductId(1);
        request.setIsFavorite(true);

        UserProduct existingUserProduct = new UserProduct();
        Product product = new Product();
        product.setProductId(1);

        when(userProductRepository.findFirstByProduct_ProductIdAndUid(1, "user123")).thenReturn(Optional.of(existingUserProduct));
        when(productRepository.findProductByTenantCodesAndId(any(), eq(1))).thenReturn(Optional.of(product));
        when(userProductRepository.save(existingUserProduct)).thenReturn(existingUserProduct);

        // Act
        UserProduct result = userProductService.favoriteProduct(request);

        // Assert
        assertNotNull(result);
        assertEquals(existingUserProduct, result);
        verify(userProductRepository, times(1)).findFirstByProduct_ProductIdAndUid(1, "user123");
        verify(productRepository, times(1)).findProductByTenantCodesAndId(any(), eq(1));
        verify(userProductRepository, times(1)).save(existingUserProduct);
    }

    @Test
    void favoriteProduct_ProductExists_UserProductDoesNotExist() {
        // Arrange
        ProductFavoriteRequest request = new ProductFavoriteRequest();
        request.setProductId(1);
        request.setIsFavorite(true);

        Product product = new Product();
        product.setProductId(1);
        UserProduct newUserProduct = new UserProduct();

        when(userProductRepository.findFirstByProduct_ProductIdAndUid(1, "user123")).thenReturn(Optional.empty());
        when(productRepository.findProductByTenantCodesAndId(any(), eq(1))).thenReturn(Optional.of(product));
        when(userProductRepository.save(any(UserProduct.class))).thenReturn(newUserProduct);

        // Act
        UserProduct result = userProductService.favoriteProduct(request);

        // Assert
        assertNotNull(result);
        verify(userProductRepository, times(1)).findFirstByProduct_ProductIdAndUid(1, "user123");
        verify(productRepository, times(1)).findProductByTenantCodesAndId(any(), eq(1));
        verify(userProductRepository, times(1)).save(any(UserProduct.class));
    }

    @Test
    void favoriteProduct_ProductDoesNotExist_ThrowsResourceNotFoundException() {
        // Arrange
        ProductFavoriteRequest request = new ProductFavoriteRequest();
        request.setProductId(1);
        request.setIsFavorite(true);

        when(userProductRepository.findFirstByProduct_ProductIdAndUid(1, "user123")).thenReturn(Optional.empty());
        when(productRepository.findProductByTenantCodesAndId(anyList(), eq(1))).thenReturn(Optional.empty());

        // Act & Assert
        assertThrows(ResourceNotFoundException.class, () -> userProductService.favoriteProduct(request));

        verify(userProductRepository, times(1)).findFirstByProduct_ProductIdAndUid(1, "user123");
        verify(productRepository, times(1)).findProductByTenantCodesAndId(any(), eq(1));
    }
}
