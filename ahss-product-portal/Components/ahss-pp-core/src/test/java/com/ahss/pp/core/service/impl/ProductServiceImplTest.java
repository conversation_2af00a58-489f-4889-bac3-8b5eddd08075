package com.ahss.pp.core.service.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.ahss.common.domain.exception.BadRequestException;
import com.ahss.common.domain.exception.ResourceNotFoundException;
import com.ahss.pp.core.service.ProductService;
import com.ahss.pp.db.model.Product;
import com.ahss.pp.db.repository.ProductConfigRepository;
import com.ahss.pp.db.repository.ProductRepository;
import com.ahss.pp.dm.dto.ProductDTO;
import com.ahss.pp.dm.mapper.ProductConfigMapper;
import com.ahss.pp.dm.mapper.ProductMapper;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.ImportAutoConfiguration;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@ExtendWith(SpringExtension.class)  // Enable Spring's TestContext Framework
@ImportAutoConfiguration(classes = {ProductServiceImpl.class})
class ProductServiceImplTest {

    @MockBean
    private ProductRepository productRepository;  // Mock this if we don't have local DB running or test-container

    @MockBean
    private ProductConfigRepository productConfigRepository;

    @MockBean
    ProductMapper productMapper;

    @MockBean
    ProductConfigMapper productConfigMapper;


    @Autowired
    private ProductService productService;

    @Test
    void getAllProducts() {
        // Given
        when(productRepository.findAll()).thenReturn(List.of(Product.builder().productId(1).productName("Insights Studio").build()));

        // When
        var productList = productService.getAllProducts();

        // Then
        assertNotNull(productList);
        assertEquals(1, productList.getFirst().getProductId());
        assertEquals("Insights Studio", productList.getFirst().getProductName());
    }

    @Test
    void testGetAllProducts() {
        Product p1 = new Product();
        p1.setProductId(1);
        p1.setProductName("Product 1");

        Product p2 = new Product();
        p2.setProductId(2);
        p2.setProductName("Product 2");
        List<Product> products = Arrays.asList(p1, p2);
        when(productRepository.findAll()).thenReturn(products);

        List<Product> result = productService.getAllProducts();

        assertEquals(2, result.size());
        verify(productRepository, times(1)).findAll();
    }

    @Test
    void testCreateProduct() {
        ProductDTO productDTO = new ProductDTO();
        Product product = new Product();
        when(productMapper.toEntity(productDTO)).thenReturn(product);
        when(productRepository.save(product)).thenReturn(product);

        Product result = productService.createProduct(productDTO);

        assertNotNull(result);
        verify(productMapper, times(1)).toEntity(productDTO);
        verify(productRepository, times(1)).save(product);
    }

    @Test
    void testUpdateProduct() {
        ProductDTO productDTO = new ProductDTO();
        productDTO.setProductId(1);
        Product existingProduct = new Product();
        when(productRepository.findById(1)).thenReturn(Optional.of(existingProduct));
        when(productRepository.save(existingProduct)).thenReturn(existingProduct);

        Product result = productService.updateProduct(productDTO);

        assertNotNull(result);
        verify(productRepository, times(1)).findById(1);
        verify(productMapper, times(1)).partialUpdate(productDTO, existingProduct);
        verify(productConfigRepository, times(1)).saveAll(existingProduct.getProductConfigs());
        verify(productRepository, times(1)).save(existingProduct);
    }

    @Test
    void testUpdateProduct_ThrowsResourceNotFoundException() {
        ProductDTO productDTO = new ProductDTO();
        productDTO.setProductId(1);
        when(productRepository.findById(1)).thenReturn(Optional.empty());

        assertThrows(ResourceNotFoundException.class, () -> productService.updateProduct(productDTO));
        verify(productRepository, times(1)).findById(1);
        verify(productMapper, never()).partialUpdate(any(), any());
        verify(productRepository, never()).save(any());
    }

    @Test
    void testDeleteProduct() {
        Product product = new Product();
        when(productRepository.findById(1)).thenReturn(Optional.of(product));

        productService.deleteProduct(1);

        verify(productRepository, times(1)).findById(1);
        verify(productRepository, times(1)).delete(product);
    }

    @Test
    void testDeleteProduct_ThrowsResourceNotFoundException() {
        when(productRepository.findById(1)).thenReturn(Optional.empty());

        assertThrows(ResourceNotFoundException.class, () -> productService.deleteProduct(1));
        verify(productRepository, times(1)).findById(1);
        verify(productRepository, never()).delete(any());
    }

    @Test
    void testGetProduct() {
        Product product = new Product();
        when(productRepository.findById(1)).thenReturn(Optional.of(product));

        Product result = productService.getProduct(1);

        assertNotNull(result);
        verify(productRepository, times(1)).findById(1);
    }

    @Test
    void testGetProduct_ThrowsResourceNotFoundException() {
        when(productRepository.findById(eq(1))).thenReturn(Optional.empty());

        assertThrows(ResourceNotFoundException.class, () -> productService.getProduct(1));
        verify(productRepository, times(1)).findById(1);
    }

    @Test
    public void shouldThrowBadRequestExceptionWhenCreatingDuplicateProduct() {
        ProductDTO request = new ProductDTO();
        request.setProductCode("P001");

        when(productRepository.findByProductCode("P001"))
            .thenReturn(Optional.of(new Product()));

        assertThrows(BadRequestException.class, () -> {
            productService.createProduct(request);
        });
    }

    @Test
    public void shouldThrowBadRequestExceptionWhenUpdatingDuplicateProduct() {
        ProductDTO request = new ProductDTO();
        request.setProductId(1);
        request.setProductCode("P001");

        Product product = new Product();
        when(productRepository.findById(eq(1))).thenReturn(Optional.of(product));

        when(productRepository.findByProductCodeAndProductIdNot("P001", 1))
            .thenReturn(Optional.of(new Product()));

        assertThrows(BadRequestException.class, () -> {
            productService.updateProduct(request);
        });
    }

    @Test
    public void shouldThrowResourceNotFoundExceptionWhenProductIdIsNullInUpdate() {
        ProductDTO request = new ProductDTO();
        request.setProductId(null);

        assertThrows(ResourceNotFoundException.class, () -> {
            productService.updateProduct(request);
        });
    }

    @Test
    public void getProductByAppIdAndTenantCodes_ReturnsProduct() {
        Product product = new Product();
        when(productRepository.findProductByProductCodeAndTenantCodes("appId", List.of("tenantCode")))
            .thenReturn(Optional.of(product));

        Product result = productService.getProductByAppIdAndTenantCodes("appId", List.of("tenantCode"));

        assertNotNull(result);
        verify(productRepository, times(1)).findProductByProductCodeAndTenantCodes("appId", List.of("tenantCode"));
    }

    @Test
    public void getProductByAppIdAndTenantCodes_ThrowsResourceNotFoundException() {
        when(productRepository.findProductByProductCodeAndTenantCodes("appId", List.of("tenantCode")))
            .thenReturn(Optional.empty());

        assertThrows(ResourceNotFoundException.class, () -> {
            productService.getProductByAppIdAndTenantCodes("appId", List.of("tenantCode"));
        });
        verify(productRepository, times(1)).findProductByProductCodeAndTenantCodes("appId", List.of("tenantCode"));
    }
}
