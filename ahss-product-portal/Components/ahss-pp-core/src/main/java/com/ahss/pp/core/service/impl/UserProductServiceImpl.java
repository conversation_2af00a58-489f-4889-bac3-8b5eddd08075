package com.ahss.pp.core.service.impl;

import com.ahss.common.domain.context.UserContext;
import com.ahss.common.domain.context.UserContextHolder;
import com.ahss.common.domain.exception.ResourceNotFoundException;
import com.ahss.pp.core.service.UserProductService;
import com.ahss.pp.db.model.Product;
import com.ahss.pp.db.model.UserProduct;
import com.ahss.pp.db.repository.ProductRepository;
import com.ahss.pp.db.repository.UserProductRepository;
import com.ahss.pp.dm.dto.ProductFavoriteRequest;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@RequiredArgsConstructor
@Service
public class UserProductServiceImpl implements UserProductService {

    private final UserProductRepository userProductRepository;
    private final ProductRepository productRepository;


    @Override
    public List<UserProduct> getProducts() {
        log.info("getAllUserProducts");
        UserContext userContext = UserContextHolder.getUserContext();
        log.info("Param - tenants: {}", userContext.getTenants());
        log.info("Param - products: {}", userContext.getProducts());
        log.info("Param - sub: {}", userContext.getSub());
        return userProductRepository.findProductsByTenantCodesWithFavorite(userContext.getTenants(), userContext.getProducts(), userContext.getSub());
    }

    @Override
    public List<UserProduct> getFavoriteProducts() {
        log.info("getFavoriteProduct");
        UserContext userContext = UserContextHolder.getUserContext();
        return userProductRepository.findByUidAndIsFavorite(userContext.getSub(), true);
    }

    @Override
    public UserProduct favoriteProduct(ProductFavoriteRequest request) {
        log.info("favoriteProduct");
        UserContext userContext = UserContextHolder.getUserContext();
        UserProduct userProduct = userProductRepository.findFirstByProduct_ProductIdAndUid(request.getProductId(), userContext.getSub())
                                                       .orElse(new UserProduct());
        Product product = productRepository
            .findProductByTenantCodesAndId(userContext.getTenants(), request.getProductId())
                                           .orElseThrow(ResourceNotFoundException::new);
        userProduct.setUid(userContext.getSub());
        userProduct.setIsFavorite(request.getIsFavorite());
        userProduct.setProduct(product);
        return userProductRepository.save(userProduct);
    }

}
