package com.ahss.pp.core.service.impl;

import com.ahss.common.api.AHSSResponseCode;
import com.ahss.common.domain.exception.BadRequestException;
import com.ahss.common.domain.exception.ResourceNotFoundException;
import com.ahss.pp.core.service.ProductService;
import com.ahss.pp.db.model.Product;
import com.ahss.pp.db.repository.ProductConfigRepository;
import com.ahss.pp.db.repository.ProductRepository;
import com.ahss.pp.dm.dto.ProductDTO;
import com.ahss.pp.dm.mapper.ProductMapper;
import io.micrometer.observation.annotation.Observed;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@RequiredArgsConstructor
@Service
public class ProductServiceImpl implements ProductService {

    private final ProductRepository productRepository;

    private final ProductConfigRepository productConfigRepository;

    private final ProductMapper productMapper;

    @Observed
    @Override
    public List<Product> getAllProducts() {
        log.info("getAllProducts");
        return productRepository.findAll();
    }

    @Override
    public Product createProduct(ProductDTO request) {
        log.info("createProduct");
        productRepository.findByProductCode(request.getProductCode()).ifPresent(existing -> {
            throw new BadRequestException(AHSSResponseCode.RC_400_004);
        });
        Product product = productMapper.toEntity(request);
        return productRepository.save(product);
    }

    @Override
    public Product updateProduct(ProductDTO request) {
        log.info("updateProduct");
        if (request.getProductId() == null) {
            throw new ResourceNotFoundException();
        }
        productRepository.findByProductCodeAndProductIdNot(request.getProductCode(), request.getProductId()).ifPresent(existing -> {
            throw new BadRequestException(AHSSResponseCode.RC_400_004);
        });
        Product product = productRepository.findById(request.getProductId()).orElseThrow(ResourceNotFoundException::new);
        productMapper.partialUpdate(request, product);
        productConfigRepository.saveAll(product.getProductConfigs());
        return productRepository.save(product);
    }

    @Override
    public void deleteProduct(Integer productId) {
        log.info("deleteProduct");
        Product product = productRepository.findById(productId).orElseThrow(ResourceNotFoundException::new);
        productRepository.delete(product);
    }

    @Override
    public Product getProduct(Integer productId) {
        log.info("getProduct");
        Product product = productRepository.findById(productId).orElseThrow(ResourceNotFoundException::new);
        return product;
    }

    @Override
    public Product getProductByAppIdAndTenantCodes(String appId, List<String> tenantCodes) {
        log.info("getProductByAppIdAndTenantCode");
        return productRepository.findProductByProductCodeAndTenantCodes(appId, tenantCodes)
                                .orElseThrow(ResourceNotFoundException::new);
    }

}
