package com.ahss.pp.core.service;

import com.ahss.pp.db.model.Product;
import com.ahss.pp.dm.dto.ProductDTO;
import java.util.List;

public interface ProductService {

    List<Product> getAllProducts();

    Product createProduct(ProductDTO request);

    Product updateProduct(ProductDTO request);

    void deleteProduct(Integer productId);

    Product getProduct(Integer productId);

    Product getProductByAppIdAndTenantCodes(String appId, List<String> tenantCodes);

}
