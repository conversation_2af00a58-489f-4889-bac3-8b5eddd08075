package com.ahss.pp.config;

import io.swagger.v3.oas.annotations.enums.SecuritySchemeType;
import io.swagger.v3.oas.annotations.security.SecurityScheme;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.servers.Server;
import java.util.List;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@SecurityScheme(
    name = "bearerAuth",
    type = SecuritySchemeType.HTTP,
    scheme = "bearer",
    bearerFormat = "JWT" // Optional, you can specify the format
)
public class OpenApiConfig {

    @Value("${ahpp.service-prefix:''}")
    private String servicePrefix;

    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
            .info(
                new Info()
                    .title("Amplify Health Product Portal")
                    .version("1.0.0")
                    .description("OpenAPI Documentation"))
            .servers(List.of(new Server()
                                 .url(servicePrefix)
                                 .description("Amplify Health Product Portal Service")))
            .addSecurityItem(new SecurityRequirement().addList("bearerAuth"));
    }
}
