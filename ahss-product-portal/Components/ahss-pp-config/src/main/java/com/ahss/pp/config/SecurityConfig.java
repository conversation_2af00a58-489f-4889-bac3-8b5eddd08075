package com.ahss.pp.config;

import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.oauth2.server.resource.web.authentication.BearerTokenAuthenticationFilter;
import org.springframework.security.web.SecurityFilterChain;

@Configuration
@EnableWebSecurity
@RequiredArgsConstructor
public class SecurityConfig {

    private static final String[] AUTH_WHITELIST = {
        "*/v3/api-docs",
        "*/v3/api-docs/**",
        "*/swagger-resources",
        "*/swagger-resources/**",
        "*/swagger-ui.html",
        "*/swagger-ui/**",
        "*/actuator/**",
        "/v3/api-docs",
        "/v3/api-docs/**",
        "/swagger-resources",
        "/swagger-resources/**",
        "/swagger-ui.html",
        "/swagger-ui/**",
        "/actuator/**"
    };


    private final CustomJwtAuthenticationFilter customJwtAuthenticationFilter;

    private final CustomAuthenticationManagerResolver authenticationManagerResolver;

    @Bean
    public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {
        http.csrf(csrf -> csrf.disable())
            .authorizeHttpRequests(authorize -> authorize
                                       .requestMatchers(AUTH_WHITELIST).permitAll()
                                       .anyRequest().authenticated()
                                  )
            .oauth2ResourceServer(oauth2 -> oauth2
                                      .authenticationManagerResolver(authenticationManagerResolver)
                                 )
            .addFilterAfter(customJwtAuthenticationFilter, BearerTokenAuthenticationFilter.class);

        return http.build();
    }

}
