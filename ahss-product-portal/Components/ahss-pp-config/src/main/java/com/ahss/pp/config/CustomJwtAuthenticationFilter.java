package com.ahss.pp.config;

import com.ahss.common.domain.context.UserContext;
import com.ahss.common.domain.context.UserContextHolder;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

@Component
public class CustomJwtAuthenticationFilter extends OncePerRequestFilter {

    @Value("${ahpp.adgroup-pattern:(?<prefix>\\w+)_(?<product>\\w+)_(?<tenant>\\w+)_(?<market>\\w+)_(?<authority>\\w+)_(?<suffix>\\w+)}")
    private String adGroupPattern;

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
    throws IOException, ServletException {

        JwtAuthenticationToken authentication = (JwtAuthenticationToken) SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null && authentication.getToken() instanceof Jwt) {
            Jwt jwt = authentication.getToken();

            // Todo: temporarily manipulate AD Group value of Core Claims to make it adaptable with AD Group pattern.
            // After Core Claims team updated to correct AD Group name, revert code back to this version:
//            UserContext userContext = UserContext.builder()
//                                                 .memberOf(jwt.getClaimAsStringList("memberOf"))
//                                                 .email(jwt.getClaim("email"))
//                                                 .sub(jwt.getClaim("sub"))
//                                                 .name(jwt.getClaim("name"))
//                                                 .accessToken(jwt.getTokenValue())
//                                                 .build();

            List<String> memberOf = jwt.getClaimAsStringList("memberOf")
                .stream()
                .map(member -> member.equals("AH_AHCC_MEDICARD_USER_PREPROD") ? "AH_AHCC_MPI_PH_USER_PREPROD" : member)
                .toList();
            UserContext userContext = UserContext.builder()
                                                 .memberOf(memberOf)
                                                 .email(jwt.getClaim("email"))
                                                 .sub(jwt.getClaim("sub"))
                                                 .name(jwt.getClaim("name"))
                                                 .accessToken(jwt.getTokenValue())
                                                 .build();

            userContext.extractMemberOf(adGroupPattern);

            List<GrantedAuthority> updatedAuthorities = new ArrayList<>(authentication.getAuthorities());
            userContext.getAuthorities().forEach(role -> updatedAuthorities.add(new SimpleGrantedAuthority(role)));

            SecurityContextHolder.getContext().setAuthentication(new JwtAuthenticationToken(jwt, updatedAuthorities));
            UserContextHolder.setUserContext(userContext);
        }

        try {
            filterChain.doFilter(request, response);
        } finally {
            UserContextHolder.clear();
        }
    }
}
