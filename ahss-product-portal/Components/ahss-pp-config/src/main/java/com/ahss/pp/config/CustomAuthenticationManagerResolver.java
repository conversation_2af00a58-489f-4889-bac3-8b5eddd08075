package com.ahss.pp.config;

import com.nimbusds.jwt.JWT;
import com.nimbusds.jwt.JWTParser;
import jakarta.servlet.http.HttpServletRequest;
import java.text.ParseException;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.AuthenticationManagerResolver;
import org.springframework.security.authentication.ProviderManager;
import org.springframework.security.oauth2.core.DelegatingOAuth2TokenValidator;
import org.springframework.security.oauth2.core.OAuth2Error;
import org.springframework.security.oauth2.core.OAuth2TokenValidator;
import org.springframework.security.oauth2.core.OAuth2TokenValidatorResult;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.jwt.JwtDecoder;
import org.springframework.security.oauth2.jwt.JwtValidators;
import org.springframework.security.oauth2.jwt.NimbusJwtDecoder;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationProvider;
import org.springframework.stereotype.Component;

@Component
@ConfigurationProperties(prefix = "security.jwt")
public class CustomAuthenticationManagerResolver implements AuthenticationManagerResolver<HttpServletRequest> {

    private final Map<String, AuthenticationManager> authenticationManagers = new HashMap<>();

    public void setIssuers(List<IssuerConfig> issuers) {
        for (IssuerConfig issuerConfig : issuers) {
            JwtDecoder jwtDecoder = createJwtDecoder(issuerConfig);
            JwtAuthenticationProvider jwtAuthenticationProvider = new JwtAuthenticationProvider(jwtDecoder);
            authenticationManagers.put(issuerConfig.getIssuer(), new ProviderManager(Collections.singletonList(jwtAuthenticationProvider)));
        }
    }

    @Override
    public AuthenticationManager resolve(HttpServletRequest request) {
        String token = extractTokenFromRequest(request);
        if (token == null) {
            throw new RuntimeException("Missing JWT Token");
        }

        String issuer = getIssuerFromToken(token);
        AuthenticationManager authenticationManager = authenticationManagers.get(issuer);
        if (authenticationManager == null) {
            throw new RuntimeException("Unknown issuer: " + issuer);
        }

        return authenticationManager;
    }

    private String extractTokenFromRequest(HttpServletRequest request) {
        String authorizationHeader = request.getHeader("Authorization");
        if (authorizationHeader != null && authorizationHeader.startsWith("Bearer")) {
            return authorizationHeader.substring(7);
        }
        return null;
    }

    private String getIssuerFromToken(String token) {
        try {
            JWT jwt = JWTParser.parse(token);
            return jwt.getJWTClaimsSet().getIssuer();
        } catch (ParseException e) {
            throw new RuntimeException("Invalid JWT Token", e);
        }
    }


    private JwtDecoder createJwtDecoder(IssuerConfig issuerConfig) {
        NimbusJwtDecoder jwtDecoder = NimbusJwtDecoder.withJwkSetUri(issuerConfig.getJwkSetUri()).build();

        OAuth2TokenValidator<Jwt> audienceValidator = jwt -> {
            List<String> audiences = jwt.getClaimAsStringList("aud");
            if (audiences == null || !audiences.contains(issuerConfig.getAudience())) {
                return OAuth2TokenValidatorResult.failure(new OAuth2Error("invalid_token", "Invalid Audience", null));
            }
            return OAuth2TokenValidatorResult.success();
        };

        OAuth2TokenValidator<Jwt> defaultValidator = JwtValidators.createDefaultWithIssuer(issuerConfig.getIssuer());
        OAuth2TokenValidator<Jwt> validator = new DelegatingOAuth2TokenValidator<>(defaultValidator, audienceValidator);

        jwtDecoder.setJwtValidator(validator);

        return jwtDecoder;
    }

    @Data
    public static class IssuerConfig {
        private String issuer;
        private String jwkSetUri;
        private String audience;
    }
}
