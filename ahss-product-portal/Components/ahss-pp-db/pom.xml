<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.ahss.pp</groupId>
        <artifactId>ahss-product-portal</artifactId>
        <version>0.0.1-SNAPSHOT</version>
        <relativePath>../../pom.xml</relativePath>
    </parent>
    <artifactId>ahss-pp-db</artifactId>

    <properties>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.modulith</groupId>
            <artifactId>spring-modulith-starter-jpa</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>

        <!-- ******************************
        ******* 3rd-party libraries *******
        *********************************** -->
        <!-- Jooq -->
        <dependency>
            <groupId>org.jooq</groupId>
            <artifactId>jooq</artifactId>
            <version>${jooq.version}</version>
        </dependency>
        <dependency>
            <groupId>org.jooq</groupId>
            <artifactId>jooq-meta</artifactId>
            <version>${jooq.version}</version>
        </dependency>
        <!-- Lombok -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>

        <!-- Observability -->
        <dependency>
            <groupId>io.micrometer</groupId>
            <artifactId>micrometer-tracing</artifactId>
            <scope>provided</scope>
        </dependency>

        <!-- Testing -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <!-- Testcontainers JUnit 5 integration -->
        <dependency>
            <groupId>org.testcontainers</groupId>
            <artifactId>junit-jupiter</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-testcontainers</artifactId>
            <scope>test</scope>
        </dependency>
        <!-- Testcontainers for PostgreSQL (or other DB you plan to use) -->
        <dependency>
            <groupId>org.testcontainers</groupId>
            <artifactId>postgresql</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- Database -->
        <dependency>
            <groupId>org.flywaydb</groupId>
            <artifactId>flyway-core</artifactId>
            <version>${flyway.version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.flywaydb</groupId>
            <artifactId>flyway-database-postgresql</artifactId>
            <version>${flyway.version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
            <version>${postgresql.version}</version>
            <scope>provided</scope>
        </dependency>
    </dependencies>
    <pluginRepositories>
        <!-- Default Maven Central Plugin Repository -->
        <pluginRepository>
            <id>central</id>
            <url>https://repo.maven.apache.org/maven2</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </pluginRepository>
        <pluginRepository>
            <id>redgate</id>
            <url>https://download.red-gate.com/maven/release</url>
        </pluginRepository>
    </pluginRepositories>
    <build>
        <plugins>
            <!-- Flyway Maven Plugin -->
            <plugin>
                <groupId>com.redgate.flyway</groupId>
                <artifactId>flyway-maven-plugin</artifactId>
                <version>10.17.3</version>
                <configuration>
                    <!-- PostgreSQL-specific configurations -->
                    <driver>org.postgresql.Driver</driver>
                    <!-- Configure your PostgreSQL connection -->
                    <url>*****************************************</url>
                    <user>ahss</user>
                    <password>password</password>
                    <createSchemas>true</createSchemas>
                    <defaultSchema>ahpp</defaultSchema>
                    <!-- Specify the location of your migration scripts -->
                    <locations>
                        <location>classpath:db/migration</location>
                    </locations>
                </configuration>
                <dependencies>
                    <!-- Add PostgreSQL JDBC driver -->
                    <dependency>
                        <groupId>org.postgresql</groupId>
                        <artifactId>postgresql</artifactId>
                        <version>${postgresql.version}</version> <!-- Use a version compatible with PostgreSQL 15.7 -->
                    </dependency>
                </dependencies>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>build-helper-maven-plugin</artifactId>
                <version>3.3.0</version>
                <executions>
                    <execution>
                        <id>add-source</id>
                        <phase>generate-sources</phase>
                        <goals>
                            <goal>add-source</goal>
                        </goals>
                        <configuration>
                            <sources>
                                <source>${project.basedir}/generated-sources/jooq</source>
                            </sources>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
    <profiles>
        <profile>
            <id>generate-jooq</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.jooq</groupId>
                        <artifactId>jooq-codegen-maven</artifactId>
                        <version>3.19.10</version> <!-- Use the latest version compatible with your Spring Boot version -->
                        <executions>
                            <execution>
                                <id>jooq-codegen</id>
                                <phase>generate-sources</phase>
                                <goals>
                                    <goal>generate</goal>
                                </goals>
                            </execution>
                        </executions>
                        <configuration>
                            <jdbc>
                                <driver>org.postgresql.Driver</driver>
                                <url>*****************************************</url>
                                <user>ahss</user>
                                <password>password</password>
                            </jdbc>
                            <generator>
                                <name>org.jooq.codegen.JavaGenerator</name>
                                <strategy>
                                    <name>org.jooq.codegen.DefaultGeneratorStrategy</name>
                                </strategy>
                                <database>
                                    <name>org.jooq.meta.postgres.PostgresDatabase</name>
                                    <includes>.*</includes>
                                    <excludes>flyway_schema_history</excludes>
                                    <inputSchema>ahpp</inputSchema>
                                    <forcedTypes>
                                        <forcedType>
                                            <name>BOOLEAN</name>
                                            <includeExpression>.*\.is_.*</includeExpression>
                                            <includeTypes>.*</includeTypes>
                                        </forcedType>
                                    </forcedTypes>
                                </database>
                                <generate>
                                    <daos>true</daos>
                                    <pojos>true</pojos>
                                    <pojosEqualsAndHashCode>true</pojosEqualsAndHashCode>
                                    <javaTimeTypes>true</javaTimeTypes>
                                    <fluentSetters>true</fluentSetters>
                                    <generatedAnnotation>true</generatedAnnotation>
                                    <generatedAnnotationType>DETECT_FROM_JDK</generatedAnnotationType>
                                    <generatedAnnotationDate>true</generatedAnnotationDate>
                                    <validationAnnotations>true</validationAnnotations>
                                    <!-- The springDao flag enables the generation of @Transactional annotations on a
                                         generated, Spring-specific DAO -->
                                    <springAnnotations>true</springAnnotations>
                                    <springDao>true</springDao>
                                    <constructorPropertiesAnnotation>true</constructorPropertiesAnnotation>
                                    <constructorPropertiesAnnotationOnPojos>true</constructorPropertiesAnnotationOnPojos>
                                    <constructorPropertiesAnnotationOnRecords>true</constructorPropertiesAnnotationOnRecords>
                                </generate>
                                <target>
                                    <packageName>com.ahss.pp.db</packageName>
                                    <directory>generated-sources/jooq</directory>
                                </target>
                            </generator>
                        </configuration>
                        <dependencies>
                            <dependency>
                                <groupId>org.postgresql</groupId>
                                <artifactId>postgresql</artifactId>
                                <version>${postgresql.version}</version>
                            </dependency>
                        </dependencies>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>
</project>
