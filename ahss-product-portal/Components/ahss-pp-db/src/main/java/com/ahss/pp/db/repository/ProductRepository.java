package com.ahss.pp.db.repository;

import com.ahss.pp.db.model.Product;
import io.micrometer.observation.annotation.Observed;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
@Observed
public interface ProductRepository extends JpaRepository<Product, Integer> {


    @Override
    List<Product> findAll();

    @Query("SELECT DISTINCT p FROM Product p " +
        "JOIN p.modules m " +
        "JOIN m.packageModules pm " +
        "JOIN pm.aPackage pk " +
        "JOIN pk.plan pl " +
        "JOIN TenantPlan tp ON pl.planId = tp.planId " +
        "WHERE tp.tenantCode in :tenantCodes AND p.productId = :productId")
    Optional<Product> findProductByTenantCodesAndId(@Param("tenantCodes") List<String> tenantCodes, @Param("productId") Integer productId);

    Optional<Product> findByProductCode(String code);

    Optional<Product> findByProductCodeAndProductIdNot(String code, Integer id);

    @Query("SELECT DISTINCT p FROM Product p " +
        "JOIN p.modules m " +
        "JOIN m.packageModules pm " +
        "JOIN pm.aPackage pk " +
        "JOIN pk.plan pl " +
        "JOIN TenantPlan tp ON pl.planId = tp.planId " +
        "WHERE  p.productCode = :productCode AND tp.tenantCode in :tenantCodes")
    Optional<Product> findProductByProductCodeAndTenantCodes(@Param("productCode") String productCode,
                                                             @Param("tenantCodes") List<String> tenantCodes);

}
