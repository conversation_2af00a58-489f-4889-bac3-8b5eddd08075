-- Create enum types
CREATE TYPE plan_type AS ENUM ('SUBSCRIPTION', 'QUOTABASED', 'AFFILIATE');
CREATE TYPE plan_status AS ENUM ('DRAFT', 'ACTIVE', 'INACTIVE', 'EXPIRED', 'DISCONTINUED', 'PENDING_PAYMENT', 'PENDING_RENEW', 'OVERDUE');
CREATE TYPE package_status AS ENUM ('DRAFT', 'ACTIVE', 'INACTIVE', 'EXPIRED', 'DISCONTINUED');
CREATE TYPE module_status AS ENUM ('DRAFT', 'ACTIVE', 'INACTIVE', 'EXPIRED', 'DISCONTINUED');
CREATE TYPE product_status AS ENUM ('DRAFT', 'ACTIVE', 'INACTIVE', 'SUSPENDED', 'DISCONTINUED');
CREATE TYPE product_config_status AS ENUM ('DRAFT', 'ACTIVE', 'INACTIVE');
CREATE TYPE product_config_type AS ENUM ('URL', 'API_KEY', 'CLIENT_ID', 'CLIENT_SECRET', 'ENDPOINT', 'AUTH_TYPE', 'TIMEOUT', 'CUSTOM1', 'CUSTOM2', 'CUSTOM3', 'CUSTOM4', 'CUSTOM5');
CREATE TYPE value_format_type AS ENUM ('EMAIL', 'PHONE_NUMBER', 'POSTAL_CODE', 'COUNTRY_CODE_ISO3', 'COUNTRY_CODE_ISO2', 'COUNTRY_CODE_NUMERIC', 'CURRENCY_CODE_ISO3', 'CURRENCY_CODE_NUMERIC', 'URL', 'FREE_TEXT', 'ALPHABETIC', 'ALPHA_NUMERIC', 'ALPHA_NUMERIC_WITH_SPACE', 'NUMERIC', 'DATE', 'DATETIME', 'BOOLEAN');


CREATE TABLE plan
(
    plan_id       SERIAL PRIMARY KEY,
    name          TEXT        NOT NULL,
    discount_rate DECIMAL     NOT NULL,
    start_date    DATE        NOT NULL,
    end_date      DATE        NOT NULL,
    plan_type     plan_type   NOT NULL,
    plan_status   plan_status NOT NULL,
    created_at    TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at    TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by    VARCHAR(50) NOT NULL DEFAULT 'system',
    updated_by    VARCHAR(50) NOT NULL DEFAULT 'system'
);

-- Create product-related tables
CREATE TABLE tenant_plan
(
    tenant_id   INTEGER            NOT NULL,
    tenant_code VARCHAR(20) UNIQUE NOT NULL,
    plan_id     INTEGER            NOT NULL,
    assigned_at TIMESTAMP          NOT NULL,
    created_at  TIMESTAMP          NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at  TIMESTAMP          NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by  VARCHAR(50)        NOT NULL DEFAULT 'system',
    updated_by  VARCHAR(50)        NOT NULL DEFAULT 'system',
    PRIMARY KEY (tenant_id, plan_id),
    FOREIGN KEY (plan_id) REFERENCES plan (plan_id)
);

CREATE TABLE package
(
    package_id     SERIAL PRIMARY KEY,
    plan_id        INTEGER        NOT NULL,
    name           TEXT           NOT NULL,
    type           TEXT           NOT NULL,
    price          DECIMAL        NOT NULL,
    package_status package_status NOT NULL,
    start_date     TIMESTAMP      NOT NULL,
    end_date       TIMESTAMP      NOT NULL,
    version        INTEGER        NOT NULL,
    created_at     TIMESTAMP      NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at     TIMESTAMP      NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by     VARCHAR(50)    NOT NULL DEFAULT 'system',
    updated_by     VARCHAR(50)    NOT NULL DEFAULT 'system',
    FOREIGN KEY (plan_id) REFERENCES plan (plan_id)
);

CREATE TABLE product
(
    product_id     SERIAL PRIMARY KEY,
    product_code   VARCHAR UNIQUE NOT NULL,
    product_name   TEXT           NOT NULL,
    description    TEXT           NOT NULL,
    product_status product_status NOT NULL,
    created_at     TIMESTAMP      NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at     TIMESTAMP      NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by     VARCHAR(50)    NOT NULL DEFAULT 'system',
    updated_by     VARCHAR(50)    NOT NULL DEFAULT 'system'
);

CREATE TABLE module
(
    module_id     SERIAL PRIMARY KEY,
    name          TEXT          NOT NULL,
    price         DECIMAL       NOT NULL,
    product_id    INTEGER       NOT NULL,
    module_status module_status NOT NULL,
    created_at    TIMESTAMP     NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at    TIMESTAMP     NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by    VARCHAR(50)   NOT NULL DEFAULT 'system',
    updated_by    VARCHAR(50)   NOT NULL DEFAULT 'system',
    FOREIGN KEY (product_id) REFERENCES product (product_id)
);

CREATE TABLE package_module
(
    package_module_id SERIAL PRIMARY KEY,
    package_id        INTEGER     NOT NULL,
    module_id         INTEGER     NOT NULL,
    created_at        TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at        TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by        VARCHAR(50) NOT NULL DEFAULT 'system',
    updated_by        VARCHAR(50) NOT NULL DEFAULT 'system',
    FOREIGN KEY (package_id) REFERENCES package (package_id),
    FOREIGN KEY (module_id) REFERENCES module (module_id)
);

CREATE TABLE product_config
(
    id                       SERIAL PRIMARY KEY,
    product_id               INTEGER               NOT NULL,
    product_code             VARCHAR               NOT NULL,
    config_type              product_config_type   NOT NULL,
    config_value             TEXT                  NOT NULL,
    config_value_format_type value_format_type     NOT NULL,
    regex                    TEXT                  NOT NULL,
    product_config_status    product_config_status NOT NULL,
    created_at               TIMESTAMP             NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at               TIMESTAMP             NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by               VARCHAR(50)           NOT NULL DEFAULT 'system',
    updated_by               VARCHAR(50)           NOT NULL DEFAULT 'system',
    FOREIGN KEY (product_id) REFERENCES product (product_id)
);

CREATE TABLE user_product
(
    id          SERIAL PRIMARY KEY,
    uid         VARCHAR(50) NOT NULL,
    product_id  INTEGER     NOT NULL,
    is_favorite BOOLEAN     NOT NULL DEFAULT FALSE,
    created_at  TIMESTAMP            DEFAULT CURRENT_TIMESTAMP,
    created_by  VARCHAR(50),
    updated_at  TIMESTAMP            DEFAULT CURRENT_TIMESTAMP,
    updated_by  VARCHAR(50),
    UNIQUE (uid, product_id),
    FOREIGN KEY (product_id) REFERENCES product (product_id)
);

-- Add indexes for foreign keys and frequently queried columns
CREATE INDEX idx_package_plan_id ON package (plan_id);
CREATE INDEX idx_module_product_id ON module (product_id);
CREATE INDEX idx_package_module_package_id ON package_module (package_id);
CREATE INDEX idx_package_module_module_id ON package_module (module_id);
CREATE INDEX idx_product_product_id ON product (product_id);
CREATE INDEX idx_product_product_code ON product (product_code);
CREATE INDEX idx_product_config_product_id ON product_config (product_id);
CREATE INDEX idx_tenant_plan_tenant_code ON tenant_plan (tenant_code);
CREATE INDEX idx_tenant_plan_plan_id ON tenant_plan (plan_id);
CREATE INDEX idx_user_product_uid ON user_product (uid);
CREATE INDEX idx_user_product_product_id ON user_product (product_id);
