-- Insert into plan
INSERT INTO plan (plan_id, name, discount_rate, start_date, end_date, plan_type, plan_status, created_at, updated_at, created_by, updated_by)
VALUES (1, 'Singapore billing plan', 0, '2024-01-01', '9999-12-31', 'SUBSCRIPTION', 'ACTIVE', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'system',
        'system'),
       (2, 'Malaysia billing plan', 0, '2024-01-01', '9999-12-31', 'SUBSCRIPTION', 'ACTIVE', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'system',
        'system'),
       (3, 'Thailand billing plan', 0, '2024-01-01', '9999-12-31', 'SUBSCRIPTION', 'ACTIVE', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'system',
        'system'),
       (4, 'Hongkong billing plan', 0, '2024-01-01', '9999-12-31', 'SUBSCRIPTION', 'ACTIVE', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'system',
        'system'),
       (5, 'Internal billing plan', 0, '2024-01-01', '9999-12-31', 'SUBSCRIPTION', 'ACTIVE', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'system',
        'system');

-- Insert into tenant_plan
INSERT INTO tenant_plan (tenant_id, tenant_code, plan_id, assigned_at, created_at, updated_at, created_by, updated_by)
VALUES (1, 'AIA_SG', 1, '2024-01-01', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'system', 'system'),
       (2, 'AIA_MY', 2, '2024-01-01', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'system', 'system'),
       (3, 'AIA_TH', 3, '2024-01-01', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'system', 'system'),
       (4, 'AIA_HK', 4, '2024-01-01', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'system', 'system'),
       (5, 'AH_INTERNAL', 5, '2024-01-01', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'system', 'system');

-- Insert into package
INSERT INTO package (package_id, plan_id, name, type, price, package_status, start_date, end_date, version, created_at, updated_at, created_by,
                     updated_by)
VALUES (1, 1, 'Insight Package', 'Type1', 1000, 'ACTIVE', '2024-01-01', '9999-12-31', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'system', 'system'),
       (2, 2, 'Core Claims Package', 'Type2', 1200, 'ACTIVE', '2024-01-01', '9999-12-31', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'system',
        'system'),
       (3, 4, 'Insight Package HK', 'Type1', 1000, 'ACTIVE', '2024-01-01', '9999-12-31', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'system',
        'system'),
       (4, 5, 'Insight Package', 'Type1', 1000, 'ACTIVE', '2024-01-01', '9999-12-31', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'system', 'system'),
       (5, 2, 'Insight Package', 'Type1', 1000, 'ACTIVE', '2024-01-01', '9999-12-31', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'system', 'system');

-- Insert into product
INSERT INTO product (product_id, product_code, product_name, description, product_status, created_at, updated_at, created_by, updated_by)
VALUES (1, 'AHIS', 'Amplify Health Insights Studio', 'Your analytics command center', 'ACTIVE', '2024-01-01', '2024-01-01', 'system', 'system'),
       (2, 'AHCC', 'Amplify Health Core Claims', 'Core Claims tool', 'ACTIVE', '2024-01-01', '2024-01-01', 'system', 'system');

-- Insert into module
INSERT INTO module (module_id, name, price, product_id, module_status, created_at, updated_at, created_by, updated_by)
VALUES (1, 'Provider Management', 500, 1, 'ACTIVE', '2024-01-01', '2024-01-01', 'system', 'system'),
       (2, 'Payment Management', 400, 1, 'INACTIVE', '2024-01-01', '2024-01-01', 'system', 'system');

-- Insert into package_module
INSERT INTO package_module (package_module_id, package_id, module_id, created_at, updated_at, created_by, updated_by)
VALUES (1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'system', 'system'),
       (2, 1, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'system', 'system'),
       (3, 3, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'system', 'system'),
       (4, 4, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'system', 'system'),
       (5, 5, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'system', 'system');

-- Insert into product_config
INSERT INTO product_config (id, product_id, product_code, config_type, config_value, config_value_format_type, regex, product_config_status,
                            created_at, updated_at, created_by, updated_by)
VALUES (1, 1, 'AHIS', 'URL', 'https://sit.insights.amplifyhealth.com/authorize', 'URL', '^https?://', 'ACTIVE', CURRENT_TIMESTAMP,
        CURRENT_TIMESTAMP,
        'system', 'system');

-- Update id sequence: This is to ensure that the next insert will start from the next available id
SELECT setval(pg_get_serial_sequence('plan', 'plan_id'), COALESCE((SELECT MAX(plan_id) FROM plan), 1));
SELECT setval(pg_get_serial_sequence('tenant_plan', 'tenant_id'), COALESCE((SELECT MAX(tenant_id) FROM tenant_plan), 1));
SELECT setval(pg_get_serial_sequence('package', 'package_id'), COALESCE((SELECT MAX(package_id) FROM package), 1));
SELECT setval(pg_get_serial_sequence('product', 'product_id'), COALESCE((SELECT MAX(product_id) FROM product), 1));
SELECT setval(pg_get_serial_sequence('module', 'module_id'), COALESCE((SELECT MAX(module_id) FROM module), 1));
SELECT setval(pg_get_serial_sequence('package_module', 'package_module_id'), COALESCE((SELECT MAX(package_module_id) FROM package_module), 1));
SELECT setval(pg_get_serial_sequence('product_config', 'id'), COALESCE((SELECT MAX(id) FROM product_config), 1));
