DO
$$
    DECLARE
        new_product_id BIGINT;
        new_package_id BIGINT;
        new_module_id  BIGINT;
        plan_id        BIGINT := 6;
        product_code   TEXT   := 'AHCCSB';
    BEGIN
        -- Insert Product and retrieve the product_id
        INSERT INTO AHPP.product (product_code, product_name, description, product_status, created_at, updated_at, created_by, updated_by)
        VALUES (product_code, 'Amplify Health Core Claims SB', 'To automate the claims processing', 'ACTIVE', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP,
                'system', 'system')
        RETURNING product_id INTO new_product_id;

        -- Use the retrieved product_id to insert into product_config
        INSERT INTO AHPP.product_config (product_id, product_code, config_type, config_value, config_value_format_type, regex,
                                         product_config_status,
                                         created_at, updated_at, created_by, updated_by)
        VALUES (new_product_id, product_code, 'URL', 'https://sanbox.app.coreclaims.amplifyhealth.com/customer-ui', 'URL', '^https?://', 'ACTIVE',
                CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'system', 'system');

        -- Insert into package
        INSERT INTO AHPP.package (plan_id, name, type, price, package_status, start_date, end_date, version, created_at, updated_at, created_by,
                                  updated_by)
        VALUES (plan_id, 'Core Claims Sandbox Package', 'Type1', 1000, 'ACTIVE', '2024-01-01', '9999-12-31', 1, CURRENT_TIMESTAMP,
                CURRENT_TIMESTAMP, 'system', 'system')
        RETURNING package_id INTO new_package_id;

        -- Insert into module
        INSERT INTO AHPP.module (name, price, product_id, module_status, created_at, updated_at, created_by, updated_by)
        VALUES ('Payment Management', 400, new_product_id, 'ACTIVE', '2024-01-01', '2024-01-01', 'system', 'system')
        RETURNING module_id INTO new_module_id;

        -- Insert into package_module
        INSERT INTO AHPP.package_module (package_id, module_id, created_at, updated_at, created_by, updated_by)
        VALUES (new_package_id, new_module_id, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'system', 'system');
    END
$$;
