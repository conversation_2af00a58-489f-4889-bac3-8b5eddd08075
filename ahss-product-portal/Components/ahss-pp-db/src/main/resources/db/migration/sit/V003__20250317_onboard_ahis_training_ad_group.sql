-- Insert into plan
INSERT INTO plan (plan_id, name, discount_rate, start_date, end_date, plan_type, plan_status, created_at, updated_at, created_by, updated_by)
VALUES (8, 'Training billing plan', 0, '2024-01-01', '9999-12-31', 'SUBSCRIPTION', 'ACTIVE', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'system',
        'system');

-- Insert into package
INSERT INTO package (package_id, plan_id, name, type, price, package_status, start_date, end_date, version, created_at, updated_at, created_by,
                     updated_by)
VALUES (6, 8, 'Insight Package Training', 'Type1', 1000, 'ACTIVE', '2024-01-01', '9999-12-31', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'system',
        'system');

-- Insert into package_module
INSERT INTO package_module (package_module_id, package_id, module_id, created_at, updated_at, created_by, updated_by)
VALUES (10, 6, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'system', 'system');

-- Insert into tenant_plan
INSERT INTO tenant_plan (tenant_id, tenant_code, plan_id, assigned_at, created_at, updated_at, created_by, updated_by)
VALUES (10, 'AH_TRAINING', 8, '2024-01-01', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'system', 'system');
