package com.ahss.pp.db.repository;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.ahss.pp.db.AhssProductPortalTestApplication;
import com.ahss.pp.db.config.TestPostgresContainersConfiguration;
import com.ahss.pp.db.enums.ProductStatus;
import com.ahss.pp.db.model.Product;
import com.ahss.pp.db.model.UserProduct;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Optional;
import javax.sql.DataSource;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.boot.test.autoconfigure.orm.jpa.TestEntityManager;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.testcontainers.containers.PostgreSQLContainer;
import org.testcontainers.junit.jupiter.Testcontainers;

@ActiveProfiles(value = {"integration"})
@ExtendWith(SpringExtension.class)
@Testcontainers
@DataJpaTest
@ContextConfiguration(classes = {AhssProductPortalTestApplication.class, TestPostgresContainersConfiguration.class})
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class UserProductRepositoryTest {


    @Autowired
    private PostgreSQLContainer postgreSQLContainer;

    @Autowired
    private TestEntityManager entityManager;

    @Autowired
    private DataSource dataSource;

    @Autowired
    private UserProductRepository userProductRepository;

    @BeforeAll
    void beforeAll() {
        postgreSQLContainer.start();
    }

    @AfterEach
    void tearDown() {
        entityManager.clear();
    }

    private Product createProduct() {
        Product product = new Product();
        product.setProductId(1000);
        product.setProductName("product1");
        product.setDescription("description1");
        product.setProductCode("code1");
        product.setCreatedAt(ZonedDateTime.of(2021, 1, 1, 0, 0, 0, 0, ZoneId.systemDefault()));
        product.setCreatedBy("system");
        product.setUpdatedAt(ZonedDateTime.of(2021, 1, 1, 0, 0, 0, 0, ZoneId.systemDefault()));
        product.setUpdatedBy("system");
        product.setProductStatus(ProductStatus.ACTIVE);
        Product p = entityManager.find(Product.class, 1);
        if (p == null) {
            return entityManager.persist(product);
        } else {
            return p;
        }
    }

    @Test
    void findByUidAndIsFavorite() {
        // Arrange
        Product product = createProduct();

        UserProduct userProduct = new UserProduct();
        userProduct.setUid("user123");
        userProduct.setProduct(product);
        userProduct.setIsFavorite(true);
        entityManager.persist(userProduct);
        entityManager.flush();

        // Act
        List<UserProduct> result = userProductRepository.findByUidAndIsFavorite("user123", true);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertTrue(result.get(0).getIsFavorite());
        assertEquals("user123", result.get(0).getUid());
        assertEquals(product.getProductId(), result.get(0).getProduct().getProductId());
    }

    @Test
    void findFirstByProduct_ProductIdAndUid() {
        Product product = createProduct();

        UserProduct userProduct = new UserProduct();
        userProduct.setUid("user123");
        userProduct.setProduct(product);
        userProduct.setIsFavorite(false);
        entityManager.persist(userProduct);
        entityManager.flush();

        // Act
        Optional<UserProduct> result = userProductRepository.findFirstByProduct_ProductIdAndUid(product.getProductId(), "user123");

        // Assert
        assertTrue(result.isPresent());
        assertEquals("user123", result.get().getUid());
        assertEquals(product.getProductId(), result.get().getProduct().getProductId());
    }
}
