package com.ahss.pp.db.repository;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.ahss.pp.db.AhssProductPortalTestApplication;
import com.ahss.pp.db.config.TestPostgresContainersConfiguration;
import com.ahss.pp.db.enums.ProductStatus;
import com.ahss.pp.db.model.Product;
import jakarta.transaction.Transactional;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Optional;
import javax.sql.DataSource;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.boot.test.autoconfigure.orm.jpa.TestEntityManager;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.testcontainers.containers.PostgreSQLContainer;
import org.testcontainers.junit.jupiter.Testcontainers;


@ActiveProfiles(value = {"integration"})
@ExtendWith(SpringExtension.class)
@Testcontainers
@DataJpaTest
@ContextConfiguration(classes = {AhssProductPortalTestApplication.class, TestPostgresContainersConfiguration.class})
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@Transactional
class ProductRepositoryTest {

    @Autowired
    private PostgreSQLContainer postgreSQLContainer;

    @Autowired
    private TestEntityManager entityManager;

    @Autowired
    private DataSource dataSource;

    @Autowired
    private ProductRepository productRepository;

    /*
     * During the integration tests, we’ll spin up a Docker container containing the database server. Since the database port exposed by the container
     * will be dynamically allocated, we cannot define the database URL in the properties file. As a result, for a Spring Boot application with a
     * version prior to 3.1, we’d need to use @DynamicPropertySource annotation in order to add these properties to a DynamicPropertyRegistry
     *
     * @param registry
     */
    /*@DynamicPropertySource
    static void configure(DynamicPropertyRegistry registry) {
        registry.add("spring.datasource.url", postgresContainer::getJdbcUrl);
    }*/
    @BeforeAll
    void beforeAll() {
        postgreSQLContainer.start();
    }

    private Product createProduct(String productCode) {
        Product product = new Product();
        product.setProductCode(productCode);
        product.setProductName("Product 1");
        product.setProductStatus(ProductStatus.ACTIVE);
        product.setDescription("Description 1");
        product.setCreatedAt(ZonedDateTime.of(2021, 1, 1, 0, 0, 0, 0, ZoneId.systemDefault()));
        product.setCreatedBy("system");
        product.setUpdatedAt(ZonedDateTime.of(2021, 1, 1, 0, 0, 0, 0, ZoneId.systemDefault()));
        product.setUpdatedBy("system");
        return product;
    }

    @Test
    void findAllProductsTest() {
        List<Product> products = productRepository.findAll();
        assertNotNull(products);
    }


    @Test
    void findProductByTenantCodesAndId_ReturnsProduct() {
        Product product = productRepository.findByProductCode("AHIS").get();
        entityManager.persist(product);
        entityManager.flush();

        Optional<Product> result = productRepository.findProductByTenantCodesAndId(List.of("AIA_SG"), product.getProductId());

        assertTrue(result.isPresent());
        assertEquals(product, result.get());
    }

    @Test
    void findProductByTenantCodesAndId_ReturnsEmpty() {
        Optional<Product> result = productRepository.findProductByTenantCodesAndId(List.of("tenantCode"), 1);

        assertFalse(result.isPresent());
    }

    @Test
    void findByProductCode_ReturnsProduct() {
        Product product = createProduct("code");
        entityManager.persist(product);
        entityManager.flush();

        Optional<Product> result = productRepository.findByProductCode("code");

        assertTrue(result.isPresent());
        assertEquals(product, result.get());
    }

    @Test
    void findByProductCode_ReturnsEmpty() {
        Optional<Product> result = productRepository.findByProductCode("code");

        assertFalse(result.isPresent());
    }

    @Test
    void findByProductCodeAndProductIdNot_ReturnsProduct() {
        Product product = createProduct("code");
        entityManager.persist(product);
        entityManager.flush();

        Optional<Product> result = productRepository.findByProductCodeAndProductIdNot("code", product.getProductId() + 1);

        assertTrue(result.isPresent());
        assertEquals(product, result.get());
    }

    @Test
    void findByProductCodeAndProductIdNot_ReturnsEmpty() {
        Optional<Product> result = productRepository.findByProductCodeAndProductIdNot("code", 1);

        assertFalse(result.isPresent());
    }

    @Test
    void findProductByProductCodeAndTenantCodes_ReturnsProduct() {
        Product product = productRepository.findByProductCode("AHIS").get();
        entityManager.persist(product);
        entityManager.flush();

        Optional<Product> result = productRepository.findProductByProductCodeAndTenantCodes("AHIS", List.of("AIA_SG"));

        assertTrue(result.isPresent());
        assertEquals(product, result.get());
    }

    @Test
    void findProductByProductCodeAndTenantCodes_ReturnsEmpty() {
        Optional<Product> result = productRepository.findProductByProductCodeAndTenantCodes("code", List.of("tenantCode"));

        assertFalse(result.isPresent());
    }
}
