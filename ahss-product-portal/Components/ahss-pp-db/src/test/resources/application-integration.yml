server:
  port: 8001
spring:
  application:
    name: "ahss-product-portal"
  datasource:
    url: ${DB_URL:*****************************************}
    driver-class-name: org.postgresql.Driver
    username: ${DB_USERNAME:ahss}
    password: ${DB_PASSWORD:password}
    hikari:
      maximum-pool-size: 10
  flyway:
    schemas:
      - ahpp
    connect-retries: 3
    locations:
      - classpath:db/migration/dev
    fail-on-missing-locations: false
    create-schemas: true
  test:
    database:
      replace: none
  testcontainers:
    postgres:
      db: dev_ahss
